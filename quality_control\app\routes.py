from fastapi import API<PERSON>out<PERSON>, Request, WebSocket, HTTPException
from fastapi.templating import <PERSON><PERSON>2Templates
import cv2
import numpy as np
from ultralytics import YOLO
import base64
import json
import asyncio
from pydantic import BaseModel


router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Load YOLOv11 model
model = YOLO("./app/model/QC_model.pt",task="detect")

# Dictionary to store WebSocket states
active_connections = {}

recommended_water_level = 0.5


@router.get("/")
async def show_quality_control_page(request: Request):
    return templates.TemplateResponse("quality_control.html", {"request": request})

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    global recommended_water_level
    await websocket.accept()
    client_id = id(websocket)
    active_connections[client_id] = True  # Mark connection as active
 

    cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)  # Open external webcam
    if not cap.isOpened():
        await websocket.send_text(json.dumps({"error": "Camera not found"}))
        await websocket.close()
        return

    print("Video capture started.")

    try:
        while active_connections.get(client_id, False):
            ret, frame = cap.read()
            if not ret:
                await websocket.send_text(json.dumps({"error": "Camera not found"}))  # Notify frontend
                break

            frame = cv2.resize(frame, (640, 480))

            # Initialize observation results
            observations = {"bottle": "N/A", "cap": "N/A", "waterLevel": "N/A"}

            boxes, class_ids, confidences = detect_objects(frame)

            for box, cls_id in zip(boxes, class_ids):
                x, y, a, b = box

                if cls_id == 2:  # Invalid Bottle
                    observations["bottle"] = "Invalid"
                elif cls_id == 0:  # Valid Bottle
                    observations["bottle"] = "Valid"    

                    # # from the top of the bottle downward
                    # d = int(y + ((b - y) * recommended_water_level))

                    # bottom upward
                    d = int(b - ((b - y) * recommended_water_level))


                    cv2.line(frame, (x, d), (a, d), (245, 66, 149), 2)
                    cv2.putText(frame, "Recommended Waterlevel", (a + 5, d + 5), cv2.FONT_HERSHEY_PLAIN, 1, (3, 252, 252), 2)

                    for det, in_cls in zip(boxes, class_ids):
                        if in_cls == 1:  # Cap Present
                            observations["cap"] = "OK"
                        elif in_cls == 3:  # Cap Not Present
                            observations["cap"] = "Not Present"
                        elif in_cls == 4:  # Water Level
                            ix, iy, ia, ib = det
                            waterlevel = ((ix, iy), (ia, iy))
                            cv2.line(frame, waterlevel[0], waterlevel[1], (245, 206, 65), 2)
                            cv2.putText(frame, "Waterlevel", waterlevel[1], cv2.FONT_HERSHEY_PLAIN, 1, (206, 252, 3), 2)

                            if (iy - d) > 5:
                                observations["waterLevel"] = "Below Recommended"
                            elif (d - iy) > 5:
                                observations["waterLevel"] = "Above Recommended"
                            else:
                                observations["waterLevel"] = "Ok"


            _, buffer = cv2.imencode('.jpg', frame)
            frame_base64 = base64.b64encode(buffer).decode("utf-8")

            await websocket.send_text(json.dumps({"image": frame_base64, **observations}))

            # Allow other tasks to run (reduces CPU load)
            await asyncio.sleep(0.01)

    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        cap.release()
        if client_id in active_connections:
            del active_connections[client_id]
        print("Video capture stopped.")

def detect_objects(frame):
    results = model(frame,verbose=False)[0]
    boxes, class_ids, confidences = [], [], []

    for det in results.boxes:
        x, y, a, b = map(int, det.xyxy[0])
        conf = det.conf[0]
        class_id = int(det.cls)

        if conf > 0.5:
            boxes.append([x, y, a, b])
            class_ids.append(class_id)
            confidences.append(float(conf))

    return boxes, class_ids, confidences


class WaterLevelRequest(BaseModel):
    level: float  # Expecting a decimal between 0 and 1

@router.post("/set-waterlevel")
async def set_water_level(data: WaterLevelRequest):
    global recommended_water_level
    level = data.level

    if not (0 <= level <= 1):
        raise HTTPException(status_code=400, detail="Invalid water level. Must be between 0 and 1.")

    recommended_water_level = level  # Store the new level dynamically
    return {"status": "success", "message": f"Water level set to {level * 100}% successfully"}
