<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel - Face Recognition System</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #1abc9c;
      --danger-color: #e74c3c;
      --warning-color: #f39c12;
      --success-color: #2ecc71;
      --text-light: #ecf0f1;
      --text-dark: #2c3e50;
      --bg-light: #f5f7fa;
      --bg-dark: #34495e;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    .header {
      background-color: var(--primary-color);
      color: var(--text-light);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--shadow);
    }

    .header-title {
      font-size: 20px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #c0392b;
      transform: translateY(-2px);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: #27ae60;
      transform: translateY(-2px);
    }

    .page-title {
      text-align: center;
      margin: 30px 0;
      color: var(--primary-color);
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);
    }

    .page-title:after {
      content: '';
      position: absolute;
      width: 60px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px 40px;
    }

    .card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .card-images img {
      max-width: 140px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .card-images img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .form-container {
      background-color: var(--bg-light);
      padding: 20px;
      border-radius: 8px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: white;
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .empty-state {
      text-align: center;
      padding: 50px 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .empty-state i {
      font-size: 48px;
      color: var(--secondary-color);
      margin-bottom: 20px;
    }

    .empty-state h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #666;
      margin-bottom: 20px;
    }

    .loading {
      text-align: center;
      padding: 30px;
      color: var(--primary-color);
    }

    /* Admin Tabs */
    .admin-tabs {
      display: flex;
      background-color: var(--bg-light);
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 25px;
      box-shadow: var(--shadow);
    }

    .tab-btn {
      flex: 1;
      padding: 15px;
      background-color: transparent;
      border: none;
      cursor: pointer;
      font-weight: 500;
      color: var(--text-dark);
      transition: var(--transition);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-bottom: 3px solid transparent;
    }

    .tab-btn i {
      font-size: 18px;
    }

    .tab-btn:hover {
      background-color: rgba(52, 152, 219, 0.1);
    }

    .tab-btn.active {
      background-color: white;
      border-bottom: 3px solid var(--secondary-color);
      color: var(--secondary-color);
      font-weight: 600;
    }

    .tab-content {
      position: relative;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    /* Form Styling */
    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: var(--bg-light);
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .file-input-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .file-status {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }

    /* Camera List Section */
    .camera-list-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-header h3 {
      font-size: 18px;
      color: var(--primary-color);
      margin: 0;
    }

    .camera-list, .users-list {
      background-color: var(--bg-light);
      border-radius: 8px;
      padding: 15px;
      min-height: 100px;
    }

    .camera-table, .users-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .camera-table th, .users-table th,
    .camera-table td, .users-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .camera-table th, .users-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .camera-table th:first-child, .users-table th:first-child {
      border-top-left-radius: 8px;
    }

    .camera-table th:last-child, .users-table th:last-child {
      border-top-right-radius: 8px;
    }

    .camera-table tr:last-child td, .users-table tr:last-child td {
      border-bottom: none;
    }

    .camera-table tr:hover, .users-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    .camera-url, .user-email {
      font-size: 12px;
      color: #666;
      word-break: break-all;
    }

    .btn-sm {
      padding: 6px 10px;
      font-size: 12px;
    }

    /* User Management Styles */
    .user-management-controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .search-box {
      display: flex;
      gap: 10px;
      width: 60%;
    }

    .search-box input {
      flex-grow: 1;
    }

    .user-actions {
      display: flex;
      gap: 5px;
    }

    .user-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #eee;
      transition: var(--transition);
    }

    .user-image:hover {
      transform: scale(1.1);
      border-color: var(--secondary-color);
    }

    /* Attendance section styling */
    .attendance-section {
      margin: 15px 0;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .attendance-section h4 {
      margin-top: 0;
      margin-bottom: 10px;
      color: var(--primary-color);
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .attendance-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .attendance-table th,
    .attendance-table td {
      padding: 8px 12px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }

    .attendance-table th {
      background-color: var(--primary-color);
      color: white;
    }

    .attendance-table tr:last-child td {
      border-bottom: none;
    }

    .attendance-table tr:hover td {
      background-color: rgba(52, 152, 219, 0.05);
    }

    /* Clustering Styles */
    .unknown-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      background-color: white;
      padding: 15px;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .control-buttons {
      display: flex;
      gap: 10px;
    }

    .cluster-settings {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .cluster-settings .form-group {
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .cluster-settings .form-control {
      width: 70px;
      padding: 8px;
    }

    .cluster-settings label {
      margin-bottom: 0;
      white-space: nowrap;
      cursor: help;
    }

    .cluster-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.85rem;
      color: #666;
      background-color: rgba(243, 156, 18, 0.1);
      padding: 6px 10px;
      border-radius: 4px;
      border-left: 3px solid var(--warning-color);
    }

    .cluster-card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
      border-left: 5px solid var(--secondary-color);
    }

    .cluster-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .cluster-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .cluster-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .cluster-count {
      background-color: var(--secondary-color);
      color: white;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .cluster-faces {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .cluster-faces img {
      max-width: 120px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .cluster-faces img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Modal Styling */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      animation: fadeIn 0.3s ease;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 30px;
      border: none;
      width: 500px;
      max-width: 90%;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      position: relative;
      animation: slideIn 0.3s ease;
    }

    .modal-content h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 25px;
      text-align: center;
      position: relative;
    }

    .modal-content h3:after {
      content: '';
      position: absolute;
      width: 50px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .modal-content button {
      margin-bottom: 10px;
    }

    /* Camera Container */
    #camera-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 2000;
      transition: opacity 0.3s ease, visibility 0.3s ease;
      display: none;
    }

    .camera-content {
      background-color: white;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 500px;
      width: 90%;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .camera-content h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 24px;
    }

    .camera-buttons {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 15px;
      margin-bottom: 15px;
    }

    #video, #image-preview {
      width: 100%;
      max-width: 400px;
      border-radius: 8px;
      box-shadow: var(--shadow);
      background-color: #f0f0f0;
      margin: 0 auto;
      display: block;
    }

    #video {
      height: 300px;
      object-fit: cover;
    }

    #image-preview {
      margin-top: 15px;
      border: 3px solid var(--success-color);
      max-height: 300px;
      object-fit: contain;
    }

    /* Only apply animation to the spinner icon */
    .fa-spinner.fa-spin {
      font-size: 40px;
      animation: spin 1s linear infinite;
    }

    /* Explicitly disable animation for all other icons */
    .fas:not(.fa-spin) {
      animation: none !important;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Confirmation Dialog */
    .confirm-dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1100;
      display: none;
      justify-content: center;
      align-items: center;
    }

    .confirm-dialog::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      backdrop-filter: blur(3px);
    }

    .confirm-dialog h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: var(--primary-color);
      position: relative;
      font-size: 24px;
    }

    .confirm-dialog p {
      margin-bottom: 20px;
      color: var(--text-dark);
      position: relative;
      font-size: 16px;
      line-height: 1.5;
    }

    .confirm-dialog-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      width: 400px;
      max-width: 90%;
      text-align: center;
      position: relative;
      z-index: 1101;
      animation: dialogFadeIn 0.3s ease;
    }

    @keyframes dialogFadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .confirm-dialog-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      position: relative;
      margin-top: 25px;
    }

    .confirm-dialog-buttons button {
      min-width: 100px;
      padding: 10px 15px;
      font-weight: 500;
    }

    /* Toast Notification */
    .toast {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      background-color: var(--primary-color);
      color: white;
      padding: 12px 25px;
      border-radius: 8px;
      z-index: 1200;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: opacity 0.3s, transform 0.3s;
      pointer-events: none;
    }

    .toast.show {
      opacity: 1;
      transform: translate(-50%, -10px);
    }

    .toast.success {
      background-color: var(--success-color);
    }

    .toast.error {
      background-color: var(--danger-color);
    }

    .toast.info {
      background-color: var(--secondary-color);
    }

    /* Responsive Design */
    /* Card Images and Image Container Styles */
    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
    }

    .image-container {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .image-container:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .card-images img, .cluster-faces img, .image-container img {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #eee;
      transition: var(--transition);
    }

    .image-actions {
      position: absolute;
      top: 5px;
      right: 5px;
      display: flex;
      gap: 5px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .image-container:hover .image-actions {
      opacity: 1;
    }

    .delete-image-btn, .move-image-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .delete-image-btn {
      background-color: rgba(220, 53, 69, 0.8);
    }

    .move-image-btn {
      background-color: rgba(0, 123, 255, 0.8);
    }

    .delete-image-btn i, .move-image-btn i {
      font-size: 12px;
    }

    .delete-image-btn:hover {
      background-color: rgba(220, 53, 69, 1);
    }

    .move-image-btn:hover {
      background-color: rgba(0, 123, 255, 1);
    }

    /* Cluster options in move modal */
    .clusters-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 15px 0;
      max-height: 300px;
      overflow-y: auto;
    }

    .cluster-option {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background-color: var(--light-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
      font-size: 14px;
    }

    .cluster-option:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .cluster-option i {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    /* Settings Tiles */
    .settings-tiles {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 25px;
    }

    .settings-tile {
      flex: 1;
      min-width: 150px;
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      box-shadow: var(--shadow);
      cursor: pointer;
      transition: var(--transition);
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .settings-tile i {
      font-size: 24px;
      color: var(--primary-color);
    }

    .settings-tile span {
      font-weight: 500;
    }

    .settings-tile:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .settings-tile.active {
      border-color: var(--secondary-color);
      background-color: rgba(52, 152, 219, 0.05);
    }

    .settings-section {
      display: none;
      animation: fadeIn 0.3s ease;
    }

    .settings-section.active {
      display: block;
    }

    /* Settings Sidebar */
    .settings-sidebar {
      display: flex;
      flex-direction: column;
      width: 250px;
      background-color: var(--bg-light);
      border-radius: 8px;
      overflow: hidden;
      box-shadow: var(--shadow);
      margin-right: 25px;
      float: left;
    }

    .settings-sidebar-item {
      padding: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      transition: var(--transition);
      border-left: 3px solid transparent;
    }

    .settings-sidebar-item i {
      font-size: 18px;
      width: 24px;
      text-align: center;
      color: var(--primary-color);
    }

    .settings-sidebar-item:hover {
      background-color: rgba(52, 152, 219, 0.1);
    }

    .settings-sidebar-item.active {
      background-color: white;
      border-left-color: var(--secondary-color);
      font-weight: 500;
    }

    .settings-subsection {
      display: none;
      animation: fadeIn 0.3s ease;
      margin-left: 275px;
    }

    .settings-subsection.active {
      display: block;
    }

    @media (max-width: 768px) {
      .settings-sidebar {
        width: 100%;
        float: none;
        margin-right: 0;
        margin-bottom: 20px;
        flex-direction: row;
        overflow-x: auto;
      }

      .settings-sidebar-item {
        border-left: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
      }

      .settings-sidebar-item.active {
        border-left-color: transparent;
        border-bottom-color: var(--secondary-color);
      }

      .settings-subsection {
        margin-left: 0;
      }
    }

    /* Webhook, WhatsApp, SMS, and Email Lists */
    .webhook-list,
    .whatsapp-list,
    .sms-list,
    .email-list {
      background-color: var(--bg-light);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }

    .webhook-table,
    .whatsapp-table,
    .sms-table,
    .email-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .webhook-table th,
    .webhook-table td,
    .whatsapp-table th,
    .whatsapp-table td,
    .sms-table th,
    .sms-table td,
    .email-table th,
    .email-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .webhook-table th,
    .whatsapp-table th,
    .sms-table th,
    .email-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .webhook-table th:first-child,
    .whatsapp-table th:first-child,
    .sms-table th:first-child,
    .email-table th:first-child {
      border-top-left-radius: 8px;
    }

    .webhook-table th:last-child,
    .whatsapp-table th:last-child,
    .sms-table th:last-child,
    .email-table th:last-child {
      border-top-right-radius: 8px;
    }

    .webhook-table tr:last-child td,
    .whatsapp-table tr:last-child td,
    .sms-table tr:last-child td,
    .email-table tr:last-child td {
      border-bottom: none;
    }

    .webhook-table tr:hover,
    .whatsapp-table tr:hover,
    .sms-table tr:hover,
    .email-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    .webhook-url,
    .whatsapp-phone,
    .sms-phone,
    .email-address {
      font-size: 14px;
      word-break: break-all;
      font-family: monospace;
    }

    .webhook-description,
    .whatsapp-name,
    .sms-name,
    .email-name {
      font-size: 14px;
      color: #666;
    }

    .webhook-template {
      font-size: 12px;
      font-family: monospace;
      max-width: 200px;
      word-break: break-word;
    }

    .webhook-template span {
      cursor: help;
    }

    .webhook-status,
    .whatsapp-status,
    .sms-status,
    .email-status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .webhook-status.active,
    .whatsapp-status.active,
    .sms-status.active,
    .email-status.active {
      background-color: rgba(46, 204, 113, 0.2);
      color: #27ae60;
    }

    .webhook-status.inactive,
    .whatsapp-status.inactive,
    .sms-status.inactive,
    .email-status.inactive {
      background-color: rgba(231, 76, 60, 0.2);
      color: #c0392b;
    }

    .webhook-actions,
    .whatsapp-actions,
    .sms-actions,
    .email-actions {
      display: flex;
      gap: 5px;
    }

    @media (max-width: 768px) {
      .form-actions {
        flex-direction: column;
      }

      .card-images img, .image-container, .cluster-faces img, .unknown-image {
        max-width: 100px;
        max-height: 100px;
      }

      .settings-tiles {
        flex-direction: column;
      }

      .settings-tile {
        min-width: 100%;
      }
    }

    /* Template Editor Styles - Compact Professional Layout */
    .template-editor-compact {
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: white;
      padding: 20px;
    }

    .template-layout {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
      align-items: start;
    }

    .template-input-column {
      display: flex;
      flex-direction: column;
    }

    .template-input-column label {
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--primary-color);
    }

    .template-textarea {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      resize: vertical;
      min-height: 160px;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 15px;
    }

    .template-reference-column {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      height: fit-content;
    }

    .variables-reference h6 {
      margin: 0 0 10px 0;
      color: var(--primary-color);
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .variables-compact {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 20px;
    }

    .var-tag {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      font-weight: bold;
      user-select: all;
      cursor: text;
      border: 1px solid #bbdefb;
    }

    .example-section {
      margin-bottom: 20px;
    }

    .example-template {
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
    }

    .example-template pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      color: #333;
      line-height: 1.4;
    }

    .preview-section {
      margin-bottom: 0;
    }

    .template-preview-compact {
      background-color: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      min-height: 60px;
      max-height: 120px;
      overflow-y: auto;
    }

    .template-preview-compact pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      white-space: pre-wrap;
      word-wrap: break-word;
      color: #333;
    }



    /* Webhook Header Styles */
    .webhook-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 25px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      border: 1px solid #dee2e6;
    }

    .section-info h3 {
      margin: 0 0 5px 0;
      color: var(--primary-color);
      font-size: 24px;
    }

    .section-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    /* Webhook Form Section Styles */
    .webhook-form-section {
      background-color: white;
      border: 1px solid #dee2e6;
      border-radius: 12px;
      margin-bottom: 25px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-bottom: 1px solid #dee2e6;
      border-radius: 12px 12px 0 0;
    }

    .form-header h4 {
      margin: 0;
      color: var(--primary-color);
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .webhook-form-section form {
      padding: 25px;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #dee2e6;
    }

    /* Webhook Cards Styles */
    .webhook-cards {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-top: 20px;
    }

    .webhook-card {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .webhook-card:hover {
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
      transform: translateY(-2px);
    }

    .webhook-card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #dee2e6;
    }

    .webhook-info {
      flex: 1;
    }

    .webhook-details {
      display: flex;
      gap: 15px;
      margin-top: 8px;
      flex-wrap: wrap;
    }

    .webhook-method,
    .webhook-headers {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 4px 8px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-size: 0.85em;
      color: #495057;
    }

    .webhook-method {
      background-color: #e3f2fd;
      border-color: #bbdefb;
      color: #1976d2;
    }

    /* Method-specific colors for webhook cards */
    .webhook-method-get {
      background-color: rgba(97, 175, 254, 0.1);
      border-color: #61affe;
      color: #61affe;
    }

    .webhook-method-post {
      background-color: rgba(73, 204, 144, 0.1);
      border-color: #49cc90;
      color: #49cc90;
    }

    .webhook-method-put {
      background-color: rgba(252, 161, 48, 0.1);
      border-color: #fca130;
      color: #fca130;
    }

    .webhook-method-patch {
      background-color: rgba(80, 227, 194, 0.1);
      border-color: #50e3c2;
      color: #50e3c2;
    }

    .webhook-method-delete {
      background-color: rgba(249, 62, 62, 0.1);
      border-color: #f93e3e;
      color: #f93e3e;
    }

    .webhook-headers {
      background-color: #f3e5f5;
      border-color: #ce93d8;
      color: #7b1fa2;
    }

    /* Postman-style URL container */
    .postman-url-container {
      display: flex;
      border: 1px solid #ced4da;
      border-radius: 4px;
      overflow: hidden;
      background-color: white;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .postman-url-container:focus-within {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .method-dropdown {
      border: none;
      background-color: #f8f9fa;
      color: #495057;
      font-weight: 600;
      font-size: 14px;
      padding: 8px 12px;
      min-width: 100px;
      border-right: 1px solid #dee2e6;
      outline: none;
      cursor: pointer;
      text-align: center;
    }

    .method-dropdown:focus {
      background-color: #e9ecef;
      outline: none;
    }

    .method-dropdown option {
      font-weight: 600;
      padding: 5px;
    }

    .url-input {
      flex: 1;
      border: none;
      padding: 8px 12px;
      font-size: 14px;
      outline: none;
      background-color: white;
    }

    .url-input:focus {
      outline: none;
    }

    .url-input::placeholder {
      color: #6c757d;
      font-style: italic;
    }

    /* Method-specific colors (like Postman) */
    .method-dropdown option[value="GET"] {
      color: #61affe;
    }

    .method-dropdown option[value="POST"] {
      color: #49cc90;
    }

    .method-dropdown option[value="PUT"] {
      color: #fca130;
    }

    .method-dropdown option[value="PATCH"] {
      color: #50e3c2;
    }

    .method-dropdown option[value="DELETE"] {
      color: #f93e3e;
    }

    .webhook-url-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
      font-family: 'Courier New', monospace;
      word-break: break-all;
    }

    .webhook-description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.4;
    }

    .webhook-status-badge {
      margin-left: 20px;
    }

    .webhook-status {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .webhook-status.active {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .webhook-status.inactive {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .webhook-template-section {
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
    }

    .webhook-template-section h5 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .template-preview-card {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      position: relative;
    }

    .template-content {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #333;
      line-height: 1.4;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 150px;
      overflow-y: auto;
    }

    .view-full-template {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 4px 8px;
      font-size: 11px;
    }

    .webhook-actions {
      padding: 20px;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      background-color: #f8f9fa;
    }

    .webhook-actions .btn {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .template-layout {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .template-reference-column {
        order: -1;
      }

      .variables-compact {
        justify-content: center;
      }

      .webhook-card-header {
        flex-direction: column;
        gap: 15px;
      }

      .webhook-status-badge {
        margin-left: 0;
        align-self: flex-start;
      }

      .webhook-actions {
        flex-direction: column;
        gap: 8px;
      }

      .webhook-actions .btn {
        justify-content: center;
      }
    }

    /* Template Modal Styles */
    .template-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .template-modal-content {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 80%;
      max-height: 80%;
      width: 600px;
      display: flex;
      flex-direction: column;
    }

    .template-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px 12px 0 0;
    }

    .template-modal-header h3 {
      margin: 0;
      color: var(--primary-color);
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .template-modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s ease;
    }

    .template-modal-close:hover {
      background-color: #f8f9fa;
      color: #333;
    }

    .template-modal-body {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
    }

    .template-modal-code {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      color: #333;
      line-height: 1.5;
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #dee2e6;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .template-modal-footer {
      padding: 20px;
      border-top: 1px solid #dee2e6;
      display: flex;
      justify-content: flex-end;
      background-color: #f8f9fa;
      border-radius: 0 0 12px 12px;
    }

    /* Camera Permissions Modal Styles */
    .camera-permissions-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .camera-permissions-content {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      display: flex;
      flex-direction: column;
    }

    .camera-permissions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
      border-radius: 12px 12px 0 0;
    }

    .camera-permissions-header h3 {
      margin: 0;
      color: #2e7d32;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .camera-permissions-body {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
    }

    .camera-permissions-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .camera-permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background-color: #f8f9fa;
      transition: all 0.2s ease;
    }

    .camera-permission-item:hover {
      background-color: #e9ecef;
      border-color: #adb5bd;
    }

    .camera-info {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .camera-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .camera-url {
      font-size: 12px;
      color: #666;
      font-family: monospace;
      word-break: break-all;
    }

    .permission-toggle {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .permission-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }

    .permission-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .permission-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }

    .permission-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .permission-slider {
      background-color: #4caf50;
    }

    input:checked + .permission-slider:before {
      transform: translateX(26px);
    }

    .permission-status {
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .permission-status.allowed {
      color: #4caf50;
    }

    .permission-status.denied {
      color: #f44336;
    }

    .camera-permissions-footer {
      padding: 20px;
      border-top: 1px solid #dee2e6;
      display: flex;
      justify-content: space-between;
      background-color: #f8f9fa;
      border-radius: 0 0 12px 12px;
    }

    .permissions-summary {
      display: flex;
      align-items: center;
      gap: 15px;
      font-size: 14px;
      color: #666;
    }

    .summary-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .summary-count {
      font-weight: 600;
      color: #333;
    }
  </style>
</head>
<body data-role="{{ user_role }}">
  <script>
    // Check if user has admin role (this is a client-side check for UI only)
    // The server-side check is more important and already implemented
    window.addEventListener('DOMContentLoaded', function() {
      // If we were redirected here without admin privileges, go back to face recognition page
      if (document.body.getAttribute('data-role') !== 'admin') {
        window.location.href = '/face_recognition';
      }
    });
  </script>
  <!-- Header -->
  <div class="header">
    <div class="header-title">Face Recognition System</div>
    <div class="header-actions">
      <button class="btn btn-secondary" id="backBtn">
        <i class="fas fa-arrow-left" style="animation: none;"></i> Back to Dashboard
      </button>
      <button class="btn btn-primary" id="refreshBtn">
        <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh Data
      </button>
    </div>
  </div>

  <h1 class="page-title">Administrator Panel</h1>

  <div class="container">
    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <button class="tab-btn active" data-tab="unknown-persons">
        <i class="fas fa-user-question" style="animation: none;"></i> Unknown Persons
      </button>
      <button class="tab-btn" data-tab="user-registration">
        <i class="fas fa-user-plus" style="animation: none;"></i> User Registration
      </button>
      <button class="tab-btn" data-tab="user-management">
        <i class="fas fa-users-cog" style="animation: none;"></i> User Management
      </button>
      <button class="tab-btn" data-tab="camera-management">
        <i class="fas fa-video" style="animation: none;"></i> Camera Management
      </button>
      <button class="tab-btn" data-tab="settings">
        <i class="fas fa-cog" style="animation: none;"></i> Settings
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Unknown Persons Tab -->
      <div class="tab-pane active" id="unknown-persons-tab">
        <!-- <div class="unknown-controls">
          <div class="control-buttons">
            <button id="clusterBtn" class="btn btn-primary">
              <i class="fas fa-object-group" style="animation: none;"></i> Auto-Cluster Similar Faces
            </button>
            <button id="viewModeBtn" class="btn btn-secondary">
              <i class="fas fa-th-large" style="animation: none;"></i> <span id="viewModeText">Switch to Clustered View</span>
            </button>
          </div>
          <div class="cluster-settings">
            <div class="form-group">
              <label for="minClusterSize" title="Minimum number of faces required to form a cluster. For small datasets, this will be automatically adjusted.">
                Min Cluster Size:
                <i class="fas fa-info-circle" style="animation: none;"></i>
              </label>
              <input type="number" id="minClusterSize" class="form-control" value="2" min="2" max="10">
            </div>
            <div class="cluster-info">
              <i class="fas fa-lightbulb" style="animation: none; color: var(--warning-color);"></i>
              <span>HDBSCAN automatically determines optimal clusters based on face similarity. For small datasets (2-3 faces), clustering may group all faces together.</span>
            </div>
          </div>
        </div> -->
        <div id="unknownContainer" class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      </div>

      <!-- User Registration Tab -->
      <div class="tab-pane" id="user-registration-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Register New User</h2>
            <p>Add a new user to the face recognition system</p>
          </div>

          <form id="registerForm" enctype="multipart/form-data">
            <div class="form-group">
              <label for="username"><i class="fas fa-user" style="animation: none;"></i> Username</label>
              <input type="text" id="username" name="username" class="form-control" placeholder="Enter username" required>
            </div>

            <div class="form-group">
              <label for="email"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
              <input type="email" id="email" name="email" class="form-control" placeholder="Enter email address" required>
            </div>

            <div class="form-group">
              <label for="profile_image"><i class="fas fa-camera" style="animation: none;"></i> Profile Image</label>
              <div class="file-input-group">
                <button type="button" id="choose-file-btn" class="btn btn-primary">
                  <i class="fas fa-image" style="animation: none;"></i> Select Profile Image
                </button>
                <input type="file" id="upload-input" name="image_file" accept="image/*" style="display: none;" required>
                <input type="hidden" id="captured_image" name="captured_image">
                <div id="file-status" class="file-status">No file chosen</div>
              </div>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-user-plus" style="animation: none;"></i> Register User
            </button>
          </form>
        </div>
      </div>

      <!-- User Management Tab -->
      <div class="tab-pane" id="user-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">User Management</h2>
            <p>View and manage registered users</p>
          </div>

          <div class="user-management-controls">
            <div class="search-box">
              <input type="text" id="userSearchInput" class="form-control" placeholder="Search users...">
              <button class="btn btn-primary" id="searchUsersBtn">
                <i class="fas fa-search" style="animation: none;"></i> Search
              </button>
            </div>

            <button class="btn btn-primary" id="refreshUsersBtn">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
            </button>
          </div>

          <div id="usersList" class="users-list">
            <div class="loading">
              <i class="fas fa-spinner fa-spin"></i>
              <p>Loading users...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Camera Management Tab -->
      <div class="tab-pane" id="camera-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Camera Management</h2>
            <p>Add and manage cameras for face recognition</p>
          </div>

          <form id="cameraForm">
            <div class="form-group">
              <label for="cameraName"><i class="fas fa-tag" style="animation: none;"></i> Camera Name</label>
              <input type="text" id="cameraName" name="cameraName" class="form-control" placeholder="Enter a unique camera name" required>
            </div>

            <div class="form-group">
              <label for="rtspUrl"><i class="fas fa-link" style="animation: none;"></i> RTSP URL</label>
              <input type="text" id="rtspUrl" name="rtspUrl" class="form-control" placeholder="rtsp://username:password@ip:port/path" required>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-plus-circle" style="animation: none;"></i> Add Camera
            </button>
          </form>

          <div class="camera-list-section">
            <div class="section-header">
              <h3>Configured Cameras</h3>
              <button class="btn btn-primary" id="refreshCamerasBtn">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
              </button>
            </div>

            <div id="cameraList" class="camera-list">
              <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading cameras...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-pane" id="settings-tab">
        <div class="card">
          <!-- Main Settings Tiles -->
          <div class="settings-tiles">
            <div class="settings-tile active" data-settings-section="application">
              <i class="fas fa-cogs" style="animation: none;"></i>
              <span>Application Settings</span>
            </div>
            <div class="settings-tile" data-settings-section="alert-management">
              <i class="fas fa-bell" style="animation: none;"></i>
              <span>Alert Management</span>
            </div>
          </div>

          <!-- Application Settings Section -->
          <div id="application-settings" class="settings-section active">
            <!-- Application Settings Sidebar -->
            <div class="settings-sidebar" style="display: flex; margin-bottom: 20px;">
              <div class="settings-sidebar-item active" data-subsection="accuracy-threshold">
                <i class="fas fa-sliders-h" style="animation: none;"></i>
                <span>Accuracy Threshold</span>
              </div>
              <!-- Add more application settings options here in the future -->
            </div>

            <!-- Accuracy Threshold Settings -->
            <div id="accuracy-threshold-subsection" class="settings-subsection active">
              <div class="section-header">
                <h3>Face Recognition Accuracy</h3>
                <p>Configure the threshold for face recognition matching</p>
              </div>

              <form id="settingsForm">
                <div class="form-group">
                  <label for="faceThreshold">
                    <i class="fas fa-sliders-h" style="animation: none;"></i> Face Recognition Threshold
                    <small class="text-muted">(Higher values require more similarity for a match)</small>
                  </label>
                  <div class="threshold-container" style="display: flex; align-items: center; gap: 15px;">
                    <input type="range" id="faceThreshold" name="faceThreshold" class="form-control" min="0.1" max="0.9" step="0.05" value="0.7">
                    <span id="thresholdValue" style="min-width: 40px; font-weight: bold;">0.7</span>
                  </div>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-save" style="animation: none;"></i> Save Settings
                </button>
              </form>

              <div id="settingsStatus" class="mt-3" style="display: none;">
                <div class="alert alert-success">
                  <i class="fas fa-check-circle" style="animation: none;"></i> Settings saved successfully!
                </div>
              </div>
            </div>
          </div>

          <!-- Alert Management Settings Section -->
          <div id="alert-management-settings" class="settings-section">
            <!-- Alert Management Sidebar -->
            <div class="settings-sidebar" style="display: flex; margin-bottom: 20px;">
              <div class="settings-sidebar-item active" data-subsection="webhook">
                <i class="fas fa-link" style="animation: none;"></i>
                <span>Webhook Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="whatsapp">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <span>WhatsApp Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="sms">
                <i class="fas fa-sms" style="animation: none;"></i>
                <span>SMS Management</span>
              </div>
              <div class="settings-sidebar-item" data-subsection="email">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <span>Email Management</span>
              </div>
            </div>

            <!-- Webhook Management Subsection -->
            <div id="webhook-subsection" class="settings-subsection active">
              <!-- Header with Add Button -->
              <div class="webhook-header">
                <div class="section-info">
                  <h3><i class="fas fa-bell" style="animation: none;"></i> Webhook Management</h3>
                  <p>Configure webhooks for system alerts and notifications</p>
                </div>
                <button id="toggleWebhookFormBtn" class="btn btn-success">
                  <i class="fas fa-plus" style="animation: none;"></i> Add New Webhook
                </button>
              </div>

              <!-- Collapsible Form Section -->
              <div id="webhookFormSection" class="webhook-form-section" style="display: none;">
                <div class="form-header">
                  <h4 id="formTitle"><i class="fas fa-plus" style="animation: none;"></i> Add New Webhook</h4>
                  <button id="closeFormBtn" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times" style="animation: none;"></i> Close
                  </button>
                </div>

                <form id="webhookForm">
                  <!-- Postman-style URL input with method dropdown -->
                  <div class="form-group">
                    <label><i class="fas fa-link" style="animation: none;"></i> Webhook URL</label>
                    <div class="postman-url-container">
                      <select id="webhookMethod" name="webhookMethod" class="method-dropdown" required>
                        <option value="POST">POST</option>
                        <option value="GET">GET</option>
                        <option value="PUT">PUT</option>
                        <option value="PATCH">PATCH</option>
                        <option value="DELETE">DELETE</option>
                      </select>
                      <input type="url" id="webhookUrl" name="webhookUrl" class="url-input" placeholder="https://example.com/webhook" required>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="webhookDescription"><i class="fas fa-info-circle" style="animation: none;"></i> Description</label>
                    <input type="text" id="webhookDescription" name="webhookDescription" class="form-control" placeholder="Enter a description for this webhook">
                  </div>

                  <div class="form-group">
                    <label for="webhookHeaders"><i class="fas fa-list" style="animation: none;"></i> Headers (JSON)</label>
                    <textarea id="webhookHeaders" name="webhookHeaders" class="form-control" rows="3" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your-token"}'></textarea>
                    <small class="form-text text-muted">
                      Optional. Enter headers as JSON key-value pairs. Leave empty if no custom headers are needed.
                    </small>
                  </div>

                  <!-- Body Template Section -->
                  <div class="form-group">
                    <label><i class="fas fa-code" style="animation: none;"></i> Webhook Body Template</label>

                    <div class="template-editor-compact">
                      <!-- Variables Reference and Template Input in Two Columns -->
                      <div class="template-layout">
                        <!-- Left Column: Template Input -->
                        <div class="template-input-column">
                          <label for="webhookTemplate">JSON Template</label>
                          <textarea id="webhookTemplate" name="webhookTemplate" class="form-control template-textarea" rows="8" placeholder='{"event": "{% raw %}{{ event_type }}{% endraw %}", "user": {"id": "{% raw %}{{ user_id }}{% endraw %}", "name": "{% raw %}{{ username }}{% endraw %}"}, "camera": {"id": "{% raw %}{{ camera_id }}{% endraw %}", "name": "{% raw %}{{ camera_name }}{% endraw %}"}, "timestamp": "{% raw %}{{ timestamp }}{% endraw %}"}'  required></textarea>
                          <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Define your custom JSON structure using variables from the reference panel.
                          </small>
                        </div>

                        <!-- Right Column: Variables Reference -->
                        <div class="template-reference-column">
                          <div class="variables-reference">
                            <h6><i class="fas fa-list"></i> Available Variables</h6>
                            <div class="variables-compact">
                              <span class="var-tag">{% raw %}{{ user_id }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ username }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ camera_id }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ camera_name }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ timestamp }}{% endraw %}</span>
                              <span class="var-tag">{% raw %}{{ event_type }}{% endraw %}</span>
                            </div>

                            <div class="example-section">
                              <h6><i class="fas fa-lightbulb"></i> Example Template</h6>
                              <div class="example-template">
                                <pre>{
  "alert": "Face detected",
  "person": "{% raw %}{{ username }}{% endraw %}",
  "camera": "{% raw %}{{ camera_name }}{% endraw %}",
  "time": "{% raw %}{{ timestamp }}{% endraw %}"
}</pre>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-plus-circle" style="animation: none;"></i> Add Webhook
                    </button>
                    <button type="button" id="cancelFormBtn" class="btn btn-secondary">
                      <i class="fas fa-times" style="animation: none;"></i> Cancel
                    </button>
                  </div>
                </form>
              </div>

              <!-- Webhooks List Section -->
              <div class="webhook-management">
                <div class="section-header">
                  <h4><i class="fas fa-list" style="animation: none;"></i> Configured Webhooks</h4>
                  <button id="refreshWebhooksBtn" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
                  </button>
                </div>
                <div id="webhookList">
                  <!-- Webhooks will be loaded here -->
                </div>
              </div>
            </div>

            <!-- WhatsApp Management Subsection -->
            <div id="whatsapp-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>WhatsApp Management</h3>
                <p>Configure WhatsApp notifications for alerts</p>
              </div>

              <form id="whatsappForm">
                <div class="form-group">
                  <label for="whatsappPhone"><i class="fas fa-phone" style="animation: none;"></i> Phone Number</label>
                  <input type="text" id="whatsappPhone" name="whatsappPhone" class="form-control" placeholder="+1234567890" required>
                </div>

                <div class="form-group">
                  <label for="whatsappName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="whatsappName" name="whatsappName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add WhatsApp Contact
                </button>
              </form>

              <div class="whatsapp-list-section">
                <div class="section-header">
                  <h3>Configured WhatsApp Contacts</h3>
                  <button class="btn btn-primary" id="refreshWhatsappBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="whatsappList" class="whatsapp-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading WhatsApp contacts...</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- SMS Management Subsection -->
            <div id="sms-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>SMS Management</h3>
                <p>Configure SMS notifications for alerts</p>
              </div>

              <form id="smsForm">
                <div class="form-group">
                  <label for="smsPhone"><i class="fas fa-phone" style="animation: none;"></i> Phone Number</label>
                  <input type="text" id="smsPhone" name="smsPhone" class="form-control" placeholder="+1234567890" required>
                </div>

                <div class="form-group">
                  <label for="smsName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="smsName" name="smsName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add SMS Contact
                </button>
              </form>

              <div class="sms-list-section">
                <div class="section-header">
                  <h3>Configured SMS Contacts</h3>
                  <button class="btn btn-primary" id="refreshSmsBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="smsList" class="sms-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading SMS contacts...</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Email Management Subsection -->
            <div id="email-subsection" class="settings-subsection">
              <div class="section-header">
                <h3>Email Management</h3>
                <p>Configure email notifications for alerts</p>
              </div>

              <form id="emailForm">
                <div class="form-group">
                  <label for="emailAddress"><i class="fas fa-envelope" style="animation: none;"></i> Email Address</label>
                  <input type="email" id="emailAddress" name="emailAddress" class="form-control" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                  <label for="emailName"><i class="fas fa-user" style="animation: none;"></i> Full Name</label>
                  <input type="text" id="emailName" name="emailName" class="form-control" placeholder="Enter full name" required>
                </div>

                <button type="submit" class="btn btn-success">
                  <i class="fas fa-plus-circle" style="animation: none;"></i> Add Email Contact
                </button>
              </form>

              <div class="email-list-section">
                <div class="section-header">
                  <h3>Configured Email Contacts</h3>
                  <button class="btn btn-primary" id="refreshEmailBtn">
                    <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
                  </button>
                </div>

                <div id="emailList" class="email-list">
                  <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading email contacts...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-dialog-content">
      <h3>Confirm Action</h3>
      <p id="confirmMessage">Are you sure you want to perform this action?</p>
      <div class="confirm-dialog-buttons">
        <button class="btn btn-danger" id="confirmYes">Delete</button>
        <button class="btn btn-primary" id="confirmNo">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Modal for image selection -->
  <div id="modal" class="modal" style="display: none;">
    <div class="modal-content">
      <h3>Select Profile Image Source</h3>
      <button type="button" id="upload-btn" class="btn btn-primary">
        <i class="fas fa-upload" style="animation: none;"></i> Upload from Device
      </button>
      <button type="button" id="capture-btn" class="btn btn-success">
        <i class="fas fa-camera" style="animation: none;"></i> Take Picture with Camera
      </button>
      <button type="button" id="close-modal-btn" class="btn btn-secondary">
        <i class="fas fa-times" style="animation: none;"></i> Cancel
      </button>
    </div>
  </div>

  <!-- Camera Section -->
  <div id="camera-container">
    <div class="camera-content">
      <h3>Take Profile Picture</h3>
      <video id="video" autoplay playsinline></video>
      <div class="camera-buttons">
        <button type="button" id="capture-image-btn" class="btn btn-success">
          <i class="fas fa-camera" style="animation: none;"></i> Capture Image
        </button>
        <button type="button" id="close-camera-btn" class="btn btn-danger">
          <i class="fas fa-times" style="animation: none;"></i> Close Camera
        </button>
      </div>
      <canvas id="canvas" style="display: none;"></canvas>
      <img id="image-preview" alt="Captured Image Preview" style="display: none;">
    </div>
  </div>

  <script>
    // DOM Elements
    const backBtn = document.getElementById('backBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const container = document.getElementById('unknownContainer');
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmYes = document.getElementById('confirmYes');
    const confirmNo = document.getElementById('confirmNo');
    const confirmMessage = document.getElementById('confirmMessage');

    // State variables
    let pendingAction = null;
    let pendingId = null;

    // Event Listeners
    backBtn.addEventListener('click', () => {
      // Navigate back to the face recognition page without stopping the monitoring
      window.location.href = '/face_recognition';
    });

    refreshBtn.addEventListener('click', () => {
      showToast('Refreshing data...', 'info');
      loadUnknowns();
    });

    // Function to show confirmation dialog
    function showConfirmDialog(message, id, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.style.display = 'flex';

      // Store the callback and ID for later use
      pendingAction = onConfirm;
      pendingId = id;

      // Set up event listeners for the buttons
      confirmYes.onclick = () => {
        confirmDialog.style.display = 'none';
        if (pendingAction) {
          if (pendingId !== null) {
            pendingAction(pendingId);
          } else {
            pendingAction();
          }
        }
        pendingAction = null;
        pendingId = null;
      };

      confirmNo.onclick = () => {
        confirmDialog.style.display = 'none';
        pendingAction = null;
        pendingId = null;
      };

      // Also close when clicking outside the dialog content
      confirmDialog.addEventListener('click', function(event) {
        if (event.target === confirmDialog) {
          confirmDialog.style.display = 'none';
          pendingAction = null;
          pendingId = null;
        }
      }, { once: true });
    }

    // Function to show toast notification
    function showToast(message, type = 'info') {
      // Create toast element if it doesn't exist
      let toast = document.getElementById('toast');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        document.body.appendChild(toast);
      }

      // Set toast content and style based on type
      toast.textContent = message;
      toast.className = `toast ${type}`;

      // Show the toast
      toast.classList.add('show');

      // Hide after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }

    // Function to load unknown persons
    async function loadUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      `;

      try {
        const res = await fetch('/face_recognition/get-unknowns');
        const data = await res.json();

        if (!data.unknowns || data.unknowns.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Clear container
        container.innerHTML = '';

        // Create cards for each unknown person
        data.unknowns.forEach(person => {
          const card = document.createElement('div');
          card.className = 'card';
          card.setAttribute('data-id', person.id);

          const imageSection = person.image_paths.map(path => {
            // Skip invalid paths
            if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
              console.error('Invalid image path:', path);
              return '';
            }

            // Sanitize path - replace backslashes with forward slashes
            const sanitizedPath = path.replace(/\\/g, '/');
            // Encode the path to prevent issues with special characters
            const encodedPath = sanitizedPath.replace(/'/g, "\\'");

            return `<div class="image-container">
              <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
              <div class="image-actions">
                <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                  <i class="fas fa-exchange-alt" style="animation: none;"></i>
                </button>
                <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </div>`;
          }).join('');

          // Create attendance section if there are attendance records
          let attendanceSection = '';
          if (person.attendance_records && person.attendance_records.length > 0) {
            attendanceSection = `
              <div class="attendance-section">
                <h4><i class="fas fa-clock" style="animation: none;"></i> Attendance Records</h4>
                <div class="attendance-list">
                  <table class="attendance-table">
                    <thead>
                      <tr>
                        <th>Date & Time</th>
                        <th>Camera</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${person.attendance_records.map(record => `
                        <tr>
                          <td>${record.timestamp}</td>
                          <td>${record.camera_name}</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              </div>
            `;
          }

          card.innerHTML = `
            <div class="card-header">
              <div class="card-title">
                <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
              </div>
              <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </div>
            <div class="card-images">${imageSection}</div>
            ${attendanceSection}
            <div class="form-container">
              <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                <input type="hidden" name="unknown_id" value="${person.id}">

                <!-- Mode selection buttons -->
                <div class="mode-selection" style="margin-bottom: 15px;">
                  <div class="btn-group" role="group" style="width: 100%;">
                    <button type="button" class="btn btn-outline-primary mode-btn" data-mode="existing" onclick="switchMode(${person.id}, 'existing')" style="width: 50%;">
                      <i class="fas fa-user-check" style="animation: none;"></i> Existing User
                    </button>
                    <button type="button" class="btn btn-outline-primary mode-btn" data-mode="new" onclick="switchMode(${person.id}, 'new')" style="width: 50%;">
                      <i class="fas fa-user-plus" style="animation: none;"></i> New User
                    </button>
                  </div>
                </div>

                <!-- Loading indicator for potential matches -->
                <div id="loading-matches-${person.id}" style="display: none; text-align: center; margin-bottom: 15px;">
                  <i class="fas fa-spinner fa-spin"></i> Checking for potential matches...
                </div>

                <!-- Potential matches section (initially hidden) -->
                <div id="potential-matches-${person.id}" style="display: none; margin-bottom: 15px;">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Potential matches found. Select a user from the dropdown or switch to "New User" mode.
                  </div>
                </div>

                <!-- Existing user selection (initially hidden) -->
                <div id="existing-user-${person.id}" class="mode-container" style="display: none;">
                  <div class="form-group">
                    <label for="user-select-${person.id}"><i class="fas fa-users" style="animation: none;"></i> Select User</label>
                    <select id="user-select-${person.id}" class="form-control" onchange="userSelected(${person.id})">
                      <option value="">-- Select a user --</option>
                    </select>
                  </div>
                  <div class="selected-user-info" id="selected-user-info-${person.id}" style="display: none; margin-top: 10px;">
                    <div class="alert alert-success">
                      <strong>Selected User:</strong> <span id="selected-username-${person.id}"></span><br>
                      <strong>Email:</strong> <span id="selected-email-${person.id}"></span>
                      <input type="hidden" id="selected-username-input-${person.id}" name="username">
                      <input type="hidden" id="selected-email-input-${person.id}" name="email">
                    </div>
                  </div>
                </div>

                <!-- New user input (initially shown) -->
                <div id="new-user-${person.id}" class="mode-container">
                  <div class="form-group">
                    <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username">
                  </div>
                  <div class="form-group">
                    <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address">
                  </div>
                </div>

                <div class="form-actions">
                  <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                  </button>
                  <small class="form-text text-muted" style="margin-top: 8px;">
                    <i class="fas fa-info-circle"></i> You can assign multiple images to the same user to improve recognition.
                  </small>
                </div>
              </form>
            </div>
          `;

          container.appendChild(card);

          // Add form submission handler
          const form = document.getElementById(`form-${person.id}`);
          form.addEventListener('submit', function(e) {
            e.preventDefault();
            assignUnknown(this, person.id);
          });

          // Initialize the UI - check for potential matches
          setTimeout(() => {
            // Set the "New User" mode as default initially
            switchMode(person.id, 'new');

            // Check if this unknown person might match existing users
            checkUnknownMatches(person.id).then(result => {
              if (result.is_likely_registered && result.matches.length > 0) {
                // If there are potential matches, switch to existing user mode
                switchMode(person.id, 'existing');
              }
            });
          }, 100);
        });
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load unknown persons. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Global variable to store registered users
    let registeredUsers = [];

    // Function to load registered users
    async function loadRegisteredUsers() {
      try {
        const response = await fetch('/face_recognition/get-registered-users');
        const data = await response.json();
        registeredUsers = data.users;
        return registeredUsers;
      } catch (error) {
        console.error('Error loading registered users:', error);
        showToast('Failed to load registered users', 'error');
        return [];
      }
    }

    // Function to check if an unknown person matches any registered users
    async function checkUnknownMatches(unknownId) {
      try {
        const response = await fetch(`/face_recognition/check-unknown-matches/${unknownId}`);
        return await response.json();
      } catch (error) {
        console.error('Error checking unknown matches:', error);
        return { matches: [], is_likely_registered: false };
      }
    }

    // Function to switch between existing and new user modes
    async function switchMode(personId, mode) {
      // Update button states
      const modeButtons = document.querySelectorAll(`[data-id="${personId}"] .mode-btn`);
      modeButtons.forEach(btn => {
        if (btn.getAttribute('data-mode') === mode) {
          btn.classList.remove('btn-outline-primary');
          btn.classList.add('btn-primary');
        } else {
          btn.classList.remove('btn-primary');
          btn.classList.add('btn-outline-primary');
        }
      });

      // Show/hide appropriate containers
      const existingUserContainer = document.getElementById(`existing-user-${personId}`);
      const newUserContainer = document.getElementById(`new-user-${personId}`);

      // Get the form inputs
      const usernameInput = document.getElementById(`username-${personId}`);
      const emailInput = document.getElementById(`email-${personId}`);
      const userSelect = document.getElementById(`user-select-${personId}`);

      if (mode === 'existing') {
        // Switch to existing user mode
        existingUserContainer.style.display = 'block';
        newUserContainer.style.display = 'none';

        // Make dropdown required and manual inputs not required
        userSelect.setAttribute('required', 'required');
        usernameInput.removeAttribute('required');
        emailInput.removeAttribute('required');

        // Show loading indicator
        document.getElementById(`loading-matches-${personId}`).style.display = 'block';

        // Load registered users if not already loaded
        if (registeredUsers.length === 0) {
          await loadRegisteredUsers();
        }

        // Check for potential matches
        const matchesResult = await checkUnknownMatches(personId);

        // Hide loading indicator
        document.getElementById(`loading-matches-${personId}`).style.display = 'none';

        // Populate the dropdown with all users
        const selectElement = document.getElementById(`user-select-${personId}`);
        selectElement.innerHTML = '<option value="">-- Select a user --</option>';

        // Add matched users first with a special group if there are matches
        if (matchesResult.matches.length > 0) {
          const matchesGroup = document.createElement('optgroup');
          matchesGroup.label = 'Potential Matches';

          matchesResult.matches.forEach(match => {
            const option = document.createElement('option');
            option.value = match.id;
            option.textContent = `${match.username} (${match.email}) - ${Math.round(match.similarity * 100)}% match`;
            option.dataset.username = match.username;
            option.dataset.email = match.email;
            matchesGroup.appendChild(option);
          });

          selectElement.appendChild(matchesGroup);

          // Show potential matches alert
          document.getElementById(`potential-matches-${personId}`).style.display = 'block';
        }

        // Add all other users
        const otherUsersGroup = document.createElement('optgroup');
        otherUsersGroup.label = 'All Users';

        registeredUsers.forEach(user => {
          // Skip users that are already in the matches group
          if (!matchesResult.matches.some(match => match.id === user.id)) {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.username} (${user.email})`;
            option.dataset.username = user.username;
            option.dataset.email = user.email;
            otherUsersGroup.appendChild(option);
          }
        });

        selectElement.appendChild(otherUsersGroup);
      } else {
        // Switch to new user mode
        existingUserContainer.style.display = 'none';
        newUserContainer.style.display = 'block';
        document.getElementById(`potential-matches-${personId}`).style.display = 'none';

        // Make manual inputs required and dropdown not required
        userSelect.removeAttribute('required');
        usernameInput.setAttribute('required', 'required');
        emailInput.setAttribute('required', 'required');

        // Clear the hidden fields
        document.getElementById(`selected-username-input-${personId}`).value = '';
        document.getElementById(`selected-email-input-${personId}`).value = '';
      }
    }

    // Function to handle user selection from dropdown
    function userSelected(personId) {
      const selectElement = document.getElementById(`user-select-${personId}`);
      const selectedOption = selectElement.options[selectElement.selectedIndex];
      const selectedUserInfo = document.getElementById(`selected-user-info-${personId}`);

      if (selectElement.value) {
        // Update the selected user info display
        document.getElementById(`selected-username-${personId}`).textContent = selectedOption.dataset.username;
        document.getElementById(`selected-email-${personId}`).textContent = selectedOption.dataset.email;

        // Update hidden form fields
        document.getElementById(`selected-username-input-${personId}`).value = selectedOption.dataset.username;
        document.getElementById(`selected-email-input-${personId}`).value = selectedOption.dataset.email;

        // Also update the regular input fields (as a fallback)
        const usernameInput = document.getElementById(`username-${personId}`);
        const emailInput = document.getElementById(`email-${personId}`);
        if (usernameInput) usernameInput.value = selectedOption.dataset.username;
        if (emailInput) emailInput.value = selectedOption.dataset.email;

        // Show the selected user info
        selectedUserInfo.style.display = 'block';

        console.log(`Selected user: ${selectedOption.dataset.username}, email: ${selectedOption.dataset.email}`);
      } else {
        // Hide the selected user info if no user is selected
        selectedUserInfo.style.display = 'none';
      }
    }

    // Function to assign an unknown person to a user
    async function assignUnknown(form, id) {
      try {
        // Check which mode is active
        const existingUserMode = document.querySelector(`[data-id="${id}"] .mode-btn[data-mode="existing"]`).classList.contains('btn-primary');

        if (existingUserMode) {
          // In existing user mode, validate that a user is selected
          const selectElement = document.getElementById(`user-select-${id}`);
          if (!selectElement.value) {
            showToast('Please select a user from the dropdown', 'error');
            return;
          }

          // Make sure the hidden fields are populated
          const selectedUsername = document.getElementById(`selected-username-input-${id}`).value;
          const selectedEmail = document.getElementById(`selected-email-input-${id}`).value;

          if (!selectedUsername || !selectedEmail) {
            showToast('Please select a user from the dropdown', 'error');
            return;
          }
        } else {
          // In new user mode, validate the manual inputs
          const usernameInput = document.getElementById(`username-${id}`);
          const emailInput = document.getElementById(`email-${id}`);

          if (!usernameInput.value) {
            showToast('Please enter a username', 'error');
            usernameInput.focus();
            return;
          }

          if (!emailInput.value) {
            showToast('Please enter an email address', 'error');
            emailInput.focus();
            return;
          }
        }

        // Create a new FormData object
        const formData = new FormData();

        // Add the unknown_id
        formData.append('unknown_id', form.querySelector('input[name="unknown_id"]').value);

        if (existingUserMode) {
          // In existing user mode, use the values from the hidden fields
          const username = document.getElementById(`selected-username-input-${id}`).value;
          const email = document.getElementById(`selected-email-input-${id}`).value;

          console.log(`Submitting form with username: ${username}, email: ${email}`);

          formData.append('username', username);
          formData.append('email', email);
        } else {
          // In new user mode, use the values from the input fields
          const username = document.getElementById(`username-${id}`).value;
          const email = document.getElementById(`email-${id}`).value;

          console.log(`Submitting form with username: ${username}, email: ${email}`);

          formData.append('username', username);
          formData.append('email', email);
        }

        showToast('Assigning user...', 'info');

        const response = await fetch('/face_recognition/assign-unknown', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User assigned successfully!', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign user', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign user. Please try again.', 'error');
      }
    }

    // Function to confirm deletion of an unknown person
    function confirmDeleteUnknown(id) {
      showConfirmDialog('Are you sure you want to delete this unknown person?', id, deleteUnknown);
    }

    // Function to delete an unknown person
    async function deleteUnknown(id) {
      try {
        showToast('Deleting unknown person...', 'info');

        const response = await fetch(`/face_recognition/delete-unknown/${id}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Unknown person deleted successfully', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast(result.detail || 'Failed to delete unknown person', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete unknown person. Please try again.', 'error');
      }
    }

    // Function to enlarge an image when clicked
    function enlargeImage(src) {
      // Check if the source is valid
      if (!src || src === '' || src === '.jpg' || src.endsWith('/.jpg')) {
        console.error('Invalid image source:', src);
        return;
      }

      const modal = document.createElement('div');
      modal.style.position = 'fixed';
      modal.style.top = '0';
      modal.style.left = '0';
      modal.style.width = '100%';
      modal.style.height = '100%';
      modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
      modal.style.display = 'flex';
      modal.style.justifyContent = 'center';
      modal.style.alignItems = 'center';
      modal.style.zIndex = '2000';
      modal.style.cursor = 'pointer';

      const img = document.createElement('img');
      img.src = src;
      img.style.maxWidth = '90%';
      img.style.maxHeight = '90%';
      img.style.borderRadius = '8px';
      img.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';

      // Handle image loading error with fallbacks
      img.onerror = function() {
        // Try to determine the username from the src
        const srcParts = src.split('/');
        const filename = srcParts[srcParts.length - 1];

        // Check if filename is valid
        if (!filename || filename === '.jpg') {
          console.error('Invalid filename:', filename);
          this.src = 'https://via.placeholder.com/200?text=No+Image';
          return;
        }

        // Decode the username to handle spaces and special characters
        const username = decodeURIComponent(filename.split('.')[0]);

        console.log(`Image failed to load: ${src}`);

        // Try fallbacks in order
        if (src.includes('/static/images/')) {
          // If original image failed, try cropped face
          console.log(`Trying fallback image for : /cropped_faces/${username}.jpg`);
          // Encode the username again for the URL
          const encodedUsername = encodeURIComponent(username);
          this.src = `/cropped_faces/${encodedUsername}.jpg`;

          // If cropped face also fails, show error
          this.onerror = function() {
            console.error(`All image fallbacks failed for ${username}`);
            this.src = 'https://via.placeholder.com/200?text=No+Image';
          };
        } else {
          // For other images, just show placeholder
          this.src = 'https://via.placeholder.com/200?text=No+Image';
        }
      };

      modal.appendChild(img);
      document.body.appendChild(modal);

      modal.addEventListener('click', () => {
        document.body.removeChild(modal);
      });
    }

    // Tab functionality
    function setupTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');

      // Function to switch to a specific tab
      function switchToTab(tabId) {
        // Remove active class from all buttons and panes
        tabBtns.forEach(b => b.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

        // Add active class to the button with the matching data-tab attribute
        const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);
        if (targetBtn) {
          targetBtn.classList.add('active');

          // Show corresponding tab pane
          const tabPane = document.getElementById(`${tabId}-tab`);

          if (tabPane) {
            tabPane.classList.add('active');

            // Load data for the tab if needed
            if (tabId === 'unknown-persons') {
              loadUnknowns();
            } else if (tabId === 'camera-management') {
              loadCameras();
            } else if (tabId === 'user-management') {
              loadUsers();
            } else if (tabId === 'settings') {
              // For settings tab, check if there's a saved section
              const savedSection = localStorage.getItem('activeSettingsSection');
              if (savedSection) {
                console.log('Found saved settings section:', savedSection);

                // Use a small timeout to ensure the settings tab is fully loaded
                setTimeout(() => {
                  // Get all settings tiles and sections
                  const settingsTiles = document.querySelectorAll('.settings-tile');
                  const settingsSections = document.querySelectorAll('.settings-section');

                  // Remove active class from all tiles and sections
                  settingsTiles.forEach(t => t.classList.remove('active'));
                  settingsSections.forEach(s => s.classList.remove('active'));

                  // Add active class to the tile with matching data-settings-section
                  const targetTile = document.querySelector(`.settings-tile[data-settings-section="${savedSection}"]`);
                  if (targetTile) {
                    targetTile.classList.add('active');
                  }

                  // Show corresponding section
                  const section = document.getElementById(`${savedSection}-settings`);
                  if (section) {
                    section.classList.add('active');
                    console.log('Activated section:', savedSection);

                    // If alert management section is activated, load webhooks
                    if (savedSection === 'alert-management') {
                      if (typeof window.loadWebhooks === 'function') {
                        window.loadWebhooks();
                      }
                    }
                  }
                }, 50);
              } else {
                // Default to face recognition settings
                settingsManager.loadSettings();
              }
            }

            // Update URL hash
            window.location.hash = tabId;
          } else {
            console.error(`Tab pane with ID "${tabId}-tab" not found`);
          }
        } else {
          console.error(`Tab button with data-tab="${tabId}" not found`);
        }
      }

      // Add click event listeners to tab buttons
      tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          const tabId = btn.getAttribute('data-tab');
          switchToTab(tabId);
        });
      });

      // Check for hash in URL on page load
      if (window.location.hash) {
        const tabId = window.location.hash.substring(1); // Remove the # character
        switchToTab(tabId);
      }
    }

    // Camera Management Functions
    function setupCameraManagement() {
      const cameraForm = document.getElementById('cameraForm');
      const refreshCamerasBtn = document.getElementById('refreshCamerasBtn');

      // Load cameras initially
      loadCameras();

      // Refresh cameras button
      refreshCamerasBtn.addEventListener('click', () => {
        loadCameras();
      });

      // Handle form submission to add the camera
      cameraForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const cameraName = document.getElementById('cameraName').value;
        const rtspUrl = document.getElementById('rtspUrl').value;

        try {
          showToast('Adding camera...', 'info');

          // Send data to backend to save the camera information
          const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cameraName, rtspUrl }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            showToast('Camera added successfully!', 'success');
            cameraForm.reset();
            loadCameras();
          } else if (result.status === 'error') {
            showToast(`Error: RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
          } else if (result.status === 'samename') {
            showToast('Camera with this name already exists', 'error');
          } else {
            showToast('Error adding camera.', 'error');
          }
        } catch (error) {
          console.error('Error adding camera:', error);
          showToast('Failed to add camera. Please check your connection.', 'error');
        }
      });
    }

    // Function to load and display the list of cameras
    async function loadCameras() {
      const cameraList = document.getElementById('cameraList');
      console.log('camera list:', cameraList);
      cameraList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading cameras...</p>
        </div>
      `;

      try {
        const response = await fetch('/face_recognition/get-cameras', {
          method: 'GET',
        });

        console.log('getting cameras..');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        console.log('Cameras:', cameras);
        cameraList.innerHTML = ''; // Clear previous list

        if (Object.keys(cameras).length === 0) {
          cameraList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-video-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No cameras configured.</p>
            </div>
          `;
          return;
        }

        // Create a table for cameras
        const table = document.createElement('table');
        table.className = 'camera-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Camera Name</th>
              <th>RTSP URL</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${cameraName}</td>
            <td><span class="camera-url">${Array.isArray(rtspUrl) ? rtspUrl[0] : rtspUrl}</span></td>
            <td>
              <button class="btn btn-danger btn-sm" onclick="confirmDeleteCamera('${cameraName}')">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </td>
          `;
          tbody.appendChild(row);
        }

        cameraList.appendChild(table);
      } catch (error) {
        console.error('Error loading cameras:', error);
        cameraList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load cameras. Please check your connection.</p>
            <button class="btn btn-primary" onclick="loadCameras()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
      console.log('in except catch');
    }

    // Function to confirm camera deletion
    function confirmDeleteCamera(cameraName) {
      showConfirmDialog(`Are you sure you want to delete the camera "${cameraName}"?`, null, () => {
        deleteCamera(cameraName);
      });
    }

    // Function to delete a camera
    async function deleteCamera(cameraName) {
      try {
        showToast('Deleting camera...', 'info');

        const response = await fetch(`/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (result.status === 'success') {
          showToast(result.message, 'success');
          loadCameras(); // Refresh the list
        } else {
          showToast('Error deleting camera: ' + result.message, 'error');
        }
      } catch (error) {
        console.error('Error deleting camera:', error);
        showToast('Failed to delete camera. Please check your connection.', 'error');
      }
    }

    // User Registration Functions
    function setupUserRegistration() {
      const registerForm = document.getElementById('registerForm');
      const chooseFileBtn = document.getElementById('choose-file-btn');
      const uploadInput = document.getElementById('upload-input');
      const fileStatus = document.getElementById('file-status');
      const modal = document.getElementById('modal');
      const uploadBtn = document.getElementById('upload-btn');
      const captureBtn = document.getElementById('capture-btn');
      const closeModalBtn = document.getElementById('close-modal-btn');
      const cameraContainer = document.getElementById('camera-container');
      const video = document.getElementById('video');
      const captureImageBtn = document.getElementById('capture-image-btn');
      const closeCameraBtn = document.getElementById('close-camera-btn');
      const canvas = document.getElementById('canvas');
      const imagePreview = document.getElementById('image-preview');
      const capturedImageInput = document.getElementById('captured_image');

      // Handle file selection button
      chooseFileBtn.addEventListener('click', () => {
        modal.style.display = 'block';
      });

      // Close modal when the close button is clicked
      closeModalBtn.addEventListener('click', () => {
        modal.style.display = 'none';
      });

      // Close modal when clicking outside the modal
      window.addEventListener('click', (event) => {
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      });

      // Upload from device
      uploadBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        uploadInput.click();
      });

      // Update file status when a file is selected
      uploadInput.addEventListener('change', () => {
        if (uploadInput.files.length > 0) {
          fileStatus.textContent = `Selected: ${uploadInput.files[0].name}`;
        } else {
          fileStatus.textContent = 'No file chosen';
        }
      });

      // Take Picture with Camera
      captureBtn.addEventListener('click', async () => {
        modal.style.display = 'none';

        console.log("Opening camera...");

        // Show camera container immediately without animation first
        cameraContainer.style.display = 'block';
        cameraContainer.style.opacity = '1';
        cameraContainer.style.visibility = 'visible';

        try {
          console.log("Requesting camera access...");
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: "user"
            }
          });
          console.log("Camera access granted");
          video.srcObject = stream;

          // Make sure video is visible
          video.style.display = 'block';

          // Log when video is ready
          video.onloadedmetadata = () => {
            console.log("Video metadata loaded, dimensions:", video.videoWidth, "x", video.videoHeight);
          };
        } catch (err) {
          // Create toast notification for error
          showToast('Could not access the camera. Please check permissions.', 'error');
          console.error("Camera error:", err);
          hideCamera();
        }
      });

      // Function to hide camera with animation
      function hideCamera() {
        console.log("Hiding camera...");
        // Directly set styles instead of using classes
        cameraContainer.style.opacity = '0';
        cameraContainer.style.visibility = 'hidden';

        // Wait for transition to complete before hiding
        setTimeout(() => {
          cameraContainer.style.display = 'none';
          console.log("Camera hidden");
        }, 300); // Match this with the transition duration
      }

      // Capture Image
      captureImageBtn.addEventListener('click', () => {
        try {
          const context = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          const dataURL = canvas.toDataURL('image/png');
          imagePreview.src = dataURL;
          imagePreview.style.display = 'block';

          // Convert the base64 string to a Blob
          const byteString = atob(dataURL.split(',')[1]);
          const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          const blob = new Blob([ab], { type: mimeString });
          const file = new File([blob], 'captured-image.png', { type: mimeString });

          // Now append the file to the form input
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          uploadInput.files = dataTransfer.files;

          // Update file status text
          fileStatus.textContent = "Image successfully captured";

          // Automatically set the base64 value in the hidden input for reference (if needed)
          capturedImageInput.value = dataURL;

          // Stop the video stream
          video.srcObject.getTracks().forEach(track => track.stop());

          // Hide camera with animation
          hideCamera();

          // Show success message
          showToast('Image captured successfully!', 'success');
        } catch (err) {
          console.error('Error capturing image:', err);
          showToast('Failed to capture image. Please try again.', 'error');
        }
      });

      // Close Camera
      closeCameraBtn.addEventListener('click', () => {
        // Stop all video tracks
        if (video.srcObject) {
          video.srcObject.getTracks().forEach(track => track.stop());
        }

        // Hide camera with animation
        hideCamera();
      });

      // Form submission with AJAX
      registerForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Always prevent default form submission

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const hasFile = uploadInput.files.length > 0 || document.getElementById('captured_image').value;

        if (!username || !email || !hasFile) {
          let message = 'Please fill in all fields';
          if (!hasFile) {
            message += ' and provide a profile image';
          }
          showToast(message + '.', 'error');
          return;
        }

        // Show loading message
        showToast('Registering user...', 'info');

        // Create FormData object from the form
        const formData = new FormData(registerForm);

        // Submit the form via AJAX
        fetch('/face_recognition/register', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.message) {
            // Show success message
            showToast(data.message, 'success');

            // Clear the form
            registerForm.reset();
            fileStatus.textContent = 'No file chosen';

            // Refresh the user list if we're on that tab
            if (document.querySelector('.tab-btn[data-tab="user-management"]').classList.contains('active')) {
              loadUsers();
            }
          } else if (data.detail) {
            // Show error message
            showToast(data.detail, 'error');
          }
        })
        .catch(error => {
          console.error('Error registering user:', error);
          showToast('Failed to register user. Please try again.', 'error');
        });
      });
    }

    // User Management Functions
    function setupUserManagement() {
      const refreshUsersBtn = document.getElementById('refreshUsersBtn');
      const searchUsersBtn = document.getElementById('searchUsersBtn');
      const userSearchInput = document.getElementById('userSearchInput');

      // Load users initially
      loadUsers();

      // Refresh users button
      refreshUsersBtn.addEventListener('click', () => {
        loadUsers();
      });

      // Search users button
      searchUsersBtn.addEventListener('click', () => {
        const searchTerm = userSearchInput.value.trim();
        loadUsers(searchTerm);
      });

      // Search on Enter key
      userSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const searchTerm = userSearchInput.value.trim();
          loadUsers(searchTerm);
        }
      });
    }

    // Function to load and display users
    async function loadUsers(searchTerm = '') {
      const usersList = document.getElementById('usersList');
      usersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading users...</p>
        </div>
      `;

      try {
        // Construct the URL with search parameter if provided
        let url = '/face_recognition/users';
        if (searchTerm) {
          url += `?search=${encodeURIComponent(searchTerm)}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const users = await response.json();

        if (users.length === 0) {
          usersList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-users-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No users found${searchTerm ? ' matching your search' : ''}.</p>
            </div>
          `;
          return;
        }

        // Create a table for users
        const table = document.createElement('table');
        table.className = 'users-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Image</th>
              <th>User Info</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const user of users) {
          const row = document.createElement('tr');

          // Use the original uploaded image from images folder
          // Encode the username to handle spaces and special characters
          const encodedUsername = encodeURIComponent(user.username);
          let userImage = `/images/${encodedUsername}.jpg`;

          // Store fallback images in a data attribute
          let fallbackImages = [];

          // First fallback: images from database
          if (user.images && user.images.length > 0) {
            user.images.forEach(img => {
              fallbackImages.push(img.image_url.replace('./', '/'));
            });
          }

          // Second fallback: cropped face image
          fallbackImages.push(`/cropped_faces/${encodedUsername}.jpg`);

          // Store fallbacks as JSON in a data attribute
          row.setAttribute('data-fallback-images', JSON.stringify(fallbackImages));

          // Create the row HTML
          row.innerHTML = `
            <td>
              <img src="${userImage}" alt="${user.username}" class="user-image" onclick="enlargeImage('${userImage}')">
            </td>
            <td>
              <strong>${user.username}</strong><br>
              <span class="user-email">${user.email}</span>
            </td>
            <td>
              <div class="user-actions">
                <button class="btn btn-primary btn-sm" onclick="viewUserDetails(${user.id})" title="View Details">
                  <i class="fas fa-eye" style="animation: none;"></i>
                </button>
                <button class="btn btn-success btn-sm" onclick="manageCameraPermissions(${user.id}, '${user.username}')" title="Camera Permissions">
                  <i class="fas fa-video" style="animation: none;"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="confirmDeleteUser(${user.id}, '${user.username}')" title="Delete User">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </td>
          `;

          // Add error handling for the image with multiple fallbacks
          const imgElement = row.querySelector('img');
          imgElement.onerror = function() {
            try {
              // Get the fallback images array
              const fallbackImagesStr = row.getAttribute('data-fallback-images');
              if (fallbackImagesStr) {
                const fallbacks = JSON.parse(fallbackImagesStr);

                if (fallbacks.length > 0) {
                  // Try the next fallback image
                  const nextFallback = fallbacks.shift();
                  console.log(`Trying fallback image for ${user.username}: ${nextFallback}`);
                  this.src = nextFallback;

                  // Update the data attribute with remaining fallbacks
                  row.setAttribute('data-fallback-images', JSON.stringify(fallbacks));

                  // If this is the last fallback, set up final error handler
                  if (fallbacks.length === 0) {
                    this.onerror = function() {
                      console.error(`All image fallbacks failed for ${user.username}`);
                      this.style.display = 'none';
                    };
                  }
                  return;
                }
              }

              // If we get here, no fallbacks worked
              this.style.display = 'none';
            } catch (e) {
              console.error('Error in image fallback handler:', e);
              this.style.display = 'none';
            }
          };

          tbody.appendChild(row);
        }

        usersList.innerHTML = '';
        usersList.appendChild(table);
      } catch (error) {
        console.error('Error loading users:', error);
        usersList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load users. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUsers()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
    }

    // Function to view user details
    function viewUserDetails(userId) {
      // Redirect to the user details page
      window.location.href = `/face_recognition/user/${userId}/page`;
      console.log(`Navigating to user details page: /face_recognition/user/${userId}/page`);
    }

    // Manage camera permissions
    async function manageCameraPermissions(userId, username) {
      try {
        showToast('Loading camera permissions...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}/camera-permissions`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        showCameraPermissionsModal(data);
      } catch (error) {
        console.error('Error loading camera permissions:', error);
        showToast('Failed to load camera permissions. Please try again.', 'error');
      }
    }

    // Show camera permissions modal
    function showCameraPermissionsModal(data) {
      // Create modal
      const modal = document.createElement('div');
      modal.className = 'camera-permissions-modal';
      modal.innerHTML = `
        <div class="camera-permissions-content">
          <div class="camera-permissions-header">
            <h3><i class="fas fa-video"></i> Camera Permissions - ${data.username}</h3>
            <button class="template-modal-close" onclick="closeCameraPermissionsModal()">&times;</button>
          </div>
          <div class="camera-permissions-body">
            <div class="camera-permissions-list" id="cameraPermissionsList">
              ${data.cameras.map(camera => `
                <div class="camera-permission-item">
                  <div class="camera-info">
                    <div class="camera-name">${camera.camera_name}</div>
                    <div class="camera-url">${camera.rtsp_url}</div>
                  </div>
                  <div class="permission-toggle">
                    <span class="permission-status ${camera.has_permission ? 'allowed' : 'denied'}">
                      ${camera.has_permission ? 'Allowed' : 'Denied'}
                    </span>
                    <label class="permission-switch">
                      <input type="checkbox"
                             data-camera-id="${camera.camera_id}"
                             ${camera.has_permission ? 'checked' : ''}
                             onchange="updatePermissionStatus(this)">
                      <span class="permission-slider"></span>
                    </label>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="camera-permissions-footer">
            <div class="permissions-summary">
              <div class="summary-item">
                <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                <span>Allowed: <span class="summary-count" id="allowedCount">0</span></span>
              </div>
              <div class="summary-item">
                <i class="fas fa-times-circle" style="color: #f44336;"></i>
                <span>Denied: <span class="summary-count" id="deniedCount">0</span></span>
              </div>
            </div>
            <div>
              <button class="btn btn-secondary" onclick="closeCameraPermissionsModal()">Cancel</button>
              <button class="btn btn-success" onclick="saveCameraPermissions(${data.user_id})">
                <i class="fas fa-save"></i> Save Changes
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      // Update summary counts
      updatePermissionsSummary();

      // Close on background click
      modal.onclick = (e) => {
        if (e.target === modal) {
          closeCameraPermissionsModal();
        }
      };
    }

    // Update permission status when toggle is changed
    function updatePermissionStatus(checkbox) {
      const statusSpan = checkbox.closest('.camera-permission-item').querySelector('.permission-status');
      if (checkbox.checked) {
        statusSpan.textContent = 'Allowed';
        statusSpan.className = 'permission-status allowed';
      } else {
        statusSpan.textContent = 'Denied';
        statusSpan.className = 'permission-status denied';
      }
      updatePermissionsSummary();
    }

    // Update permissions summary counts
    function updatePermissionsSummary() {
      const checkboxes = document.querySelectorAll('#cameraPermissionsList input[type="checkbox"]');
      const allowedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
      const deniedCount = checkboxes.length - allowedCount;

      const allowedCountEl = document.getElementById('allowedCount');
      const deniedCountEl = document.getElementById('deniedCount');

      if (allowedCountEl) allowedCountEl.textContent = allowedCount;
      if (deniedCountEl) deniedCountEl.textContent = deniedCount;
    }

    // Save camera permissions
    async function saveCameraPermissions(userId) {
      try {
        const checkboxes = document.querySelectorAll('#cameraPermissionsList input[type="checkbox"]:checked');
        const cameraIds = Array.from(checkboxes).map(cb => parseInt(cb.dataset.cameraId));

        showToast('Saving camera permissions...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}/camera-permissions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ camera_ids: cameraIds }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        showToast('Camera permissions updated successfully!', 'success');
        closeCameraPermissionsModal();
      } catch (error) {
        console.error('Error saving camera permissions:', error);
        showToast('Failed to save camera permissions. Please try again.', 'error');
      }
    }

    // Close camera permissions modal
    function closeCameraPermissionsModal() {
      const modal = document.querySelector('.camera-permissions-modal');
      if (modal) {
        document.body.removeChild(modal);
      }
    }

    // Function to confirm user deletion
    function confirmDeleteUser(userId, username) {
      showConfirmDialog(`Are you sure you want to delete the user "${username}"?`, null, () => {
        deleteUser(userId);
      });
    }

    // Function to delete a user
    async function deleteUser(userId) {
      try {
        showToast('Deleting user...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User deleted successfully', 'success');
          loadUsers(); // Refresh the list
        } else {
          showToast(result.detail || 'Failed to delete user', 'error');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        showToast('Failed to delete user. Please try again.', 'error');
      }
    }

    // Settings Management Functions
    function setupSettingsManagement() {
      const settingsForm = document.getElementById('settingsForm');
      const faceThreshold = document.getElementById('faceThreshold');
      const thresholdValue = document.getElementById('thresholdValue');
      const settingsStatus = document.getElementById('settingsStatus');
      const settingsTiles = document.querySelectorAll('.settings-tile');
      const settingsSections = document.querySelectorAll('.settings-section');
      const settingsSidebarItems = document.querySelectorAll('.settings-sidebar-item');
      const settingsSubsections = document.querySelectorAll('.settings-subsection');
      const webhookForm = document.getElementById('webhookForm');
      const refreshWebhooksBtn = document.getElementById('refreshWebhooksBtn');

      // Main Settings Tiles Navigation
      settingsTiles.forEach(tile => {
        tile.addEventListener('click', () => {
          console.log('Settings tile clicked:', tile.getAttribute('data-settings-section'));

          // Remove active class from all tiles and sections
          settingsTiles.forEach(t => t.classList.remove('active'));
          settingsSections.forEach(s => s.classList.remove('active'));

          // Add active class to clicked tile
          tile.classList.add('active');

          // Show corresponding section
          const sectionId = tile.getAttribute('data-settings-section');
          const section = document.getElementById(`${sectionId}-settings`);

          if (section) {
            section.classList.add('active');
            console.log('Activated section:', sectionId);

            // Save the active section to localStorage
            localStorage.setItem('activeSettingsSection', sectionId);
            console.log('Saved active section to localStorage:', sectionId);

            // Activate the first sidebar item in the section by default
            const firstSidebarItem = section.querySelector('.settings-sidebar-item');
            if (firstSidebarItem) {
              // Simulate a click on the first sidebar item
              const subsectionId = firstSidebarItem.getAttribute('data-subsection');
              activateSubsection(section, subsectionId);
            }

            // If alert management section is activated, load webhooks
            if (sectionId === 'alert-management') {
              // Load the appropriate subsection data
              const activeSubsection = document.querySelector('#alert-management-settings .settings-sidebar-item.active');
              if (activeSubsection) {
                const subsectionId = activeSubsection.getAttribute('data-subsection');
                if (subsectionId === 'webhook') {
                  loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    initializeTemplateEditor();
                  }, 100);
                } else if (subsectionId === 'whatsapp') {
                  loadWhatsAppContacts();
                } else if (subsectionId === 'sms') {
                  loadSMSContacts();
                } else if (subsectionId === 'email') {
                  loadEmailContacts();
                }
              } else {
                // Default to loading webhooks if no subsection is active
                loadWebhooks();
                // Initialize template editor when webhook subsection is activated
                setTimeout(() => {
                  initializeTemplateEditor();
                }, 100);
              }
            }
          } else {
            console.error(`Settings section with ID "${sectionId}-settings" not found`);
          }
        });
      });

      // Settings Sidebar Items Navigation
      settingsSidebarItems.forEach(item => {
        item.addEventListener('click', () => {
          const subsectionId = item.getAttribute('data-subsection');
          const parentSection = item.closest('.settings-section');

          if (parentSection) {
            activateSubsection(parentSection, subsectionId);

            // Load the appropriate data based on the subsection
            if (subsectionId === 'webhook') {
              loadWebhooks();
              // Initialize template editor when webhook subsection is activated
              setTimeout(() => {
                initializeTemplateEditor();
              }, 100);
            } else if (subsectionId === 'whatsapp') {
              loadWhatsAppContacts();
            } else if (subsectionId === 'sms') {
              loadSMSContacts();
            } else if (subsectionId === 'email') {
              loadEmailContacts();
            }
          }
        });
      });

      // Function to activate a subsection within a section
      function activateSubsection(parentSection, subsectionId) {
        console.log('Activating subsection:', subsectionId);

        // Remove active class from all sidebar items in this section
        const sidebarItems = parentSection.querySelectorAll('.settings-sidebar-item');
        sidebarItems.forEach(si => si.classList.remove('active'));

        // Add active class to the clicked sidebar item
        const clickedItem = parentSection.querySelector(`.settings-sidebar-item[data-subsection="${subsectionId}"]`);
        if (clickedItem) {
          clickedItem.classList.add('active');
        }

        // Remove active class from all subsections in this section
        const subsections = parentSection.querySelectorAll('.settings-subsection');
        subsections.forEach(ss => ss.classList.remove('active'));

        // Add active class to the corresponding subsection
        const subsection = parentSection.querySelector(`#${subsectionId}-subsection`);
        if (subsection) {
          subsection.classList.add('active');

          // Save the active subsection to localStorage
          localStorage.setItem('activeSettingsSubsection', subsectionId);
          console.log('Saved active subsection to localStorage:', subsectionId);
        } else {
          console.error(`Subsection with ID "${subsectionId}-subsection" not found`);
        }
      }

      // Update the displayed threshold value when the slider changes
      faceThreshold.addEventListener('input', () => {
        thresholdValue.textContent = faceThreshold.value;
      });

      // Load current settings when the tab is shown
      async function loadSettings() {
        try {
          console.log('Loading face recognition settings...');
          const response = await fetch('/face_recognition/get-settings');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const settings = await response.json();

          // Update the slider with the current threshold value
          if (settings.face_threshold) {
            faceThreshold.value = settings.face_threshold;
            thresholdValue.textContent = settings.face_threshold;
          }

          // Restore active section from localStorage
          const activeSection = localStorage.getItem('activeSettingsSection');
          if (activeSection) {
            // Find the tile for this section
            const tile = document.querySelector(`.settings-tile[data-settings-section="${activeSection}"]`);
            if (tile) {
              // Simulate a click on the tile
              tile.click();

              // Restore active subsection from localStorage
              const activeSubsection = localStorage.getItem('activeSettingsSubsection');
              if (activeSubsection) {
                // Find the section element
                const sectionElement = document.getElementById(`${activeSection}-settings`);
                if (sectionElement) {
                  // Find the sidebar item for this subsection
                  const sidebarItem = sectionElement.querySelector(`.settings-sidebar-item[data-subsection="${activeSubsection}"]`);
                  if (sidebarItem) {
                    // Simulate a click on the sidebar item
                    sidebarItem.click();
                  }
                }
              }
            }
          }

          // Check which subsection is active and load appropriate data
          const activeSubsection = localStorage.getItem('activeSettingsSubsection');
          if (activeSubsection === 'webhook') {
            if (typeof window.loadWebhooks === 'function') {
              window.loadWebhooks();
              // Initialize template editor when webhook subsection is activated
              setTimeout(() => {
                initializeTemplateEditor();
              }, 100);
            }
          } else if (activeSubsection === 'whatsapp') {
            if (typeof window.loadWhatsAppContacts === 'function') {
              window.loadWhatsAppContacts();
            }
          } else if (activeSubsection === 'sms') {
            if (typeof window.loadSMSContacts === 'function') {
              window.loadSMSContacts();
            }
          } else if (activeSubsection === 'email') {
            if (typeof window.loadEmailContacts === 'function') {
              window.loadEmailContacts();
            }
          }
        } catch (error) {
          console.error('Error loading settings:', error);
          showToast('Failed to load settings. Please try again.', 'error');
        }
      }

      // Handle form submission to save settings
      settingsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const threshold = faceThreshold.value;

        try {
          showToast('Saving settings...', 'info');

          const response = await fetch('/face_recognition/update-settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ face_threshold: parseFloat(threshold) }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Show success message
          showToast(result.message || 'Settings saved successfully!', 'success');

          // Show the settings status message
          settingsStatus.style.display = 'block';

          // Hide the status message after 3 seconds
          setTimeout(() => {
            settingsStatus.style.display = 'none';
          }, 3000);
        } catch (error) {
          console.error('Error saving settings:', error);
          showToast('Failed to save settings. Please try again.', 'error');
        }
      });

      // Webhook Management
      // Load webhooks - define as a function that can be called globally
      window.loadWebhooks = async function() {
        console.log('Loading webhooks...');
        const webhookList = document.getElementById('webhookList');

        if (!webhookList) {
          console.error('Webhook list element not found');
          return;
        }

        webhookList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading webhooks...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/webhooks');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const webhooks = await response.json();

          if (webhooks.length === 0) {
            webhookList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-bell-slash" style="animation: none;"></i>
                <h3>No Webhooks Configured</h3>
                <p>Add a webhook URL to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create cards to display webhooks for better readability
          let cardsHTML = '<div class="webhook-cards">';

          webhooks.forEach(webhook => {
            // Format body template for display
            let templateDisplay = 'No template';
            let templatePreview = '';
            if (webhook.body_template) {
              try {
                const templateStr = JSON.stringify(webhook.body_template, null, 2);
                templateDisplay = templateStr.length > 100
                  ? `${templateStr.substring(0, 100)}...`
                  : templateStr;
                templatePreview = templateStr;
              } catch (e) {
                templateDisplay = 'Invalid template';
                templatePreview = 'Invalid JSON template';
              }
            }

            cardsHTML += `
              <div class="webhook-card" data-id="${webhook.id}">
                <div class="webhook-card-header">
                  <div class="webhook-info">
                    <h4 class="webhook-url-title">${webhook.url}</h4>
                    <p class="webhook-description">${webhook.description || 'No description provided'}</p>
                    <div class="webhook-details">
                      <span class="webhook-method webhook-method-${(webhook.http_method || 'POST').toLowerCase()}"><i class="fas fa-code"></i> ${webhook.http_method || 'POST'}</span>
                      ${webhook.headers ? `<span class="webhook-headers"><i class="fas fa-list"></i> ${Object.keys(webhook.headers).length} header(s)</span>` : ''}
                    </div>
                  </div>
                  <div class="webhook-status-badge">
                    <span class="webhook-status ${webhook.is_active ? 'active' : 'inactive'}">
                      <i class="fas ${webhook.is_active ? 'fa-check-circle' : 'fa-pause-circle'}"></i>
                      ${webhook.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div class="webhook-template-section">
                  <h5><i class="fas fa-code"></i> Body Template</h5>
                  <div class="template-preview-card">
                    <pre class="template-content">${templateDisplay}</pre>
                    ${templatePreview.length > 100 ? `<button class="btn btn-sm btn-link view-full-template" data-template='${templatePreview.replace(/'/g, "&#39;")}'>View Full Template</button>` : ''}
                  </div>
                </div>

                <div class="webhook-actions">
                  <button class="btn btn-sm btn-primary edit-webhook-btn" data-id="${webhook.id}">
                    <i class="fas fa-edit"></i> Edit
                  </button>
                  <button class="btn btn-sm ${webhook.is_active ? 'btn-warning' : 'btn-success'} toggle-webhook-btn"
                          data-id="${webhook.id}"
                          data-active="${webhook.is_active}">
                    <i class="fas ${webhook.is_active ? 'fa-pause' : 'fa-play'}"></i>
                    ${webhook.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                  <button class="btn btn-sm btn-danger delete-webhook-btn" data-id="${webhook.id}">
                    <i class="fas fa-trash-alt"></i> Delete
                  </button>
                </div>
              </div>
            `;
          });

          cardsHTML += '</div>';
          webhookList.innerHTML = cardsHTML;

          // Add event listeners for all webhook buttons
          document.querySelectorAll('.edit-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => editWebhook(btn.getAttribute('data-id')));
          });

          document.querySelectorAll('.toggle-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleWebhook(btn));
          });

          document.querySelectorAll('.delete-webhook-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteWebhook(btn.getAttribute('data-id')));
          });

          document.querySelectorAll('.view-full-template').forEach(btn => {
            btn.addEventListener('click', () => viewFullTemplate(btn.getAttribute('data-template')));
          });

        } catch (error) {
          console.error('Error loading webhooks:', error);
          webhookList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading Webhooks</h3>
              <p>Failed to load webhooks. Please try again.</p>
              <button class="btn btn-primary" onclick="loadWebhooks()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // WhatsApp Management
      // Load WhatsApp contacts - define as a function that can be called globally
      window.loadWhatsAppContacts = async function() {
        console.log('Loading WhatsApp contacts...');
        const whatsappList = document.getElementById('whatsappList');

        if (!whatsappList) {
          console.error('WhatsApp list element not found');
          return;
        }

        whatsappList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading WhatsApp contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/whatsapp');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            whatsappList.innerHTML = `
              <div class="empty-state">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <h3>No WhatsApp Contacts Configured</h3>
                <p>Add a WhatsApp contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display WhatsApp contacts
          let tableHTML = `
            <table class="whatsapp-table">
              <thead>
                <tr>
                  <th>Phone Number</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="whatsapp-phone">${contact.phone_number}</td>
                <td class="whatsapp-name">${contact.full_name}</td>
                <td>
                  <span class="whatsapp-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="whatsapp-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-whatsapp-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-whatsapp-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          whatsappList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-whatsapp-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleWhatsApp(btn));
          });

          document.querySelectorAll('.delete-whatsapp-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteWhatsApp(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading WhatsApp contacts:', error);
          whatsappList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading WhatsApp Contacts</h3>
              <p>Failed to load WhatsApp contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadWhatsAppContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // SMS Management
      // Load SMS contacts - define as a function that can be called globally
      window.loadSMSContacts = async function() {
        console.log('Loading SMS contacts...');
        const smsList = document.getElementById('smsList');

        if (!smsList) {
          console.error('SMS list element not found');
          return;
        }

        smsList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading SMS contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/sms');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            smsList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-sms" style="animation: none;"></i>
                <h3>No SMS Contacts Configured</h3>
                <p>Add an SMS contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display SMS contacts
          let tableHTML = `
            <table class="sms-table">
              <thead>
                <tr>
                  <th>Phone Number</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="sms-phone">${contact.phone_number}</td>
                <td class="sms-name">${contact.full_name}</td>
                <td>
                  <span class="sms-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="sms-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-sms-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-sms-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          smsList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-sms-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleSMS(btn));
          });

          document.querySelectorAll('.delete-sms-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteSMS(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading SMS contacts:', error);
          smsList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading SMS Contacts</h3>
              <p>Failed to load SMS contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadSMSContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // Email Management
      // Load Email contacts - define as a function that can be called globally
      window.loadEmailContacts = async function() {
        console.log('Loading Email contacts...');
        const emailList = document.getElementById('emailList');

        if (!emailList) {
          console.error('Email list element not found');
          return;
        }

        emailList.innerHTML = `
          <div class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading Email contacts...</p>
          </div>
        `;

        try {
          const response = await fetch('/face_recognition/email');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const contacts = await response.json();

          if (contacts.length === 0) {
            emailList.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <h3>No Email Contacts Configured</h3>
                <p>Add an Email contact to receive system alerts and notifications.</p>
              </div>
            `;
            return;
          }

          // Create table to display Email contacts
          let tableHTML = `
            <table class="email-table">
              <thead>
                <tr>
                  <th>Email Address</th>
                  <th>Full Name</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
          `;

          contacts.forEach(contact => {
            tableHTML += `
              <tr data-id="${contact.id}">
                <td class="email-address">${contact.email_address}</td>
                <td class="email-name">${contact.full_name}</td>
                <td>
                  <span class="email-status ${contact.is_active ? 'active' : 'inactive'}">
                    ${contact.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td class="email-actions">
                  <button class="btn btn-sm ${contact.is_active ? 'btn-warning' : 'btn-success'} toggle-email-btn"
                          data-id="${contact.id}"
                          data-active="${contact.is_active}">
                    <i class="fas ${contact.is_active ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>
                  </button>
                  <button class="btn btn-sm btn-danger delete-email-btn" data-id="${contact.id}">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </td>
              </tr>
            `;
          });

          tableHTML += `
              </tbody>
            </table>
          `;

          emailList.innerHTML = tableHTML;

          // Add event listeners for toggle and delete buttons
          document.querySelectorAll('.toggle-email-btn').forEach(btn => {
            btn.addEventListener('click', () => toggleEmail(btn));
          });

          document.querySelectorAll('.delete-email-btn').forEach(btn => {
            btn.addEventListener('click', () => confirmDeleteEmail(btn.getAttribute('data-id')));
          });

        } catch (error) {
          console.error('Error loading Email contacts:', error);
          emailList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
              <h3>Error Loading Email Contacts</h3>
              <p>Failed to load Email contacts. Please try again.</p>
              <button class="btn btn-primary" onclick="loadEmailContacts()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
              </button>
            </div>
          `;
        }
      }

      // Toggle webhook active status
      async function toggleWebhook(button) {
        const webhookId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} webhook...`, 'info');

          const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}"></i> ${!isActive ? 'Deactivate' : 'Activate'}`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-webhook-btn`;

          // Update status badge in the card
          const card = button.closest('.webhook-card');
          const statusBadge = card.querySelector('.webhook-status');
          statusBadge.className = `webhook-status ${!isActive ? 'active' : 'inactive'}`;
          statusBadge.innerHTML = `<i class="fas ${!isActive ? 'fa-check-circle' : 'fa-pause-circle'}"></i> ${!isActive ? 'Active' : 'Inactive'}`;

          showToast(`Webhook ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling webhook:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} webhook. Please try again.`, 'error');
        }
      }

      // Confirm webhook deletion
      function confirmDeleteWebhook(webhookId) {
        showConfirmDialog('Are you sure you want to delete this webhook?', webhookId, deleteWebhook);
      }

      // Edit webhook
      async function editWebhook(webhookId) {
        try {
          // Fetch webhook details
          const response = await fetch(`/face_recognition/webhooks/${webhookId}`);
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const webhook = await response.json();

          // Populate the form with existing data
          document.getElementById('webhookUrl').value = webhook.url;
          document.getElementById('webhookDescription').value = webhook.description || '';
          document.getElementById('webhookMethod').value = webhook.http_method || 'POST';

          if (webhook.headers) {
            document.getElementById('webhookHeaders').value = JSON.stringify(webhook.headers, null, 2);
          } else {
            document.getElementById('webhookHeaders').value = '';
          }

          if (webhook.body_template) {
            document.getElementById('webhookTemplate').value = JSON.stringify(webhook.body_template, null, 2);
          } else {
            document.getElementById('webhookTemplate').value = '';
          }

          // Update the form to edit mode
          const form = document.getElementById('webhookForm');
          form.setAttribute('data-edit-id', webhookId);

          // Change submit button text
          const submitBtn = form.querySelector('button[type="submit"]');
          submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Webhook';

          // Add cancel button
          if (!form.querySelector('.cancel-edit-btn')) {
            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'btn btn-secondary cancel-edit-btn';
            cancelBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';
            cancelBtn.onclick = cancelEdit;
            submitBtn.parentNode.insertBefore(cancelBtn, submitBtn);
          }

          // Update form title
          const formTitle = document.getElementById('formTitle');
          if (formTitle) {
            formTitle.innerHTML = '<i class="fas fa-edit"></i> Edit Webhook';
          }

          // Show the form
          showWebhookForm();

          // Initialize template editor
          initializeTemplateEditor();

          // Update method color
          updateMethodColor();

          showToast('Webhook loaded for editing', 'info');
        } catch (error) {
          console.error('Error loading webhook for edit:', error);
          showToast('Failed to load webhook details. Please try again.', 'error');
        }
      }

      // Cancel edit mode
      function cancelEdit() {
        const form = document.getElementById('webhookForm');
        form.removeAttribute('data-edit-id');
        form.reset();

        // Reset submit button
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Webhook';

        // Remove cancel button
        const cancelBtn = form.querySelector('.cancel-edit-btn');
        if (cancelBtn) {
          cancelBtn.remove();
        }

        // Reset form title
        const formTitle = document.getElementById('formTitle');
        if (formTitle) {
          formTitle.innerHTML = '<i class="fas fa-plus"></i> Add New Webhook';
        }

        // Hide the form
        hideWebhookForm();

        showToast('Edit cancelled', 'info');
      }

      // View full template
      function viewFullTemplate(template) {
        try {
          const parsedTemplate = JSON.parse(template);
          const formattedTemplate = JSON.stringify(parsedTemplate, null, 2);

          // Create modal to show full template
          const modal = document.createElement('div');
          modal.className = 'template-modal';
          modal.innerHTML = `
            <div class="template-modal-content">
              <div class="template-modal-header">
                <h3><i class="fas fa-code"></i> Full Webhook Template</h3>
                <button class="template-modal-close">&times;</button>
              </div>
              <div class="template-modal-body">
                <pre class="template-modal-code">${formattedTemplate}</pre>
              </div>
              <div class="template-modal-footer">
                <button class="btn btn-secondary template-modal-close">Close</button>
              </div>
            </div>
          `;

          document.body.appendChild(modal);

          // Add event listeners for closing
          modal.querySelectorAll('.template-modal-close').forEach(btn => {
            btn.onclick = () => {
              document.body.removeChild(modal);
            };
          });

          // Close on background click
          modal.onclick = (e) => {
            if (e.target === modal) {
              document.body.removeChild(modal);
            }
          };

        } catch (error) {
          showToast('Error displaying template', 'error');
        }
      }

      // Delete webhook
      async function deleteWebhook(webhookId) {
        try {
          showToast('Deleting webhook...', 'info');

          const response = await fetch(`/face_recognition/webhooks/${webhookId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the card from the list
          const card = document.querySelector(`.webhook-card[data-id="${webhookId}"]`);
          if (card) {
            card.remove();
          }

          // Check if there are any cards left
          const cards = document.querySelectorAll('.webhook-card');
          if (cards.length === 0) {
            document.getElementById('webhookList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-bell-slash" style="animation: none;"></i>
                <h3>No Webhooks Configured</h3>
                <p>Add a webhook URL to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('Webhook deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting webhook:', error);
          showToast('Failed to delete webhook. Please try again.', 'error');
        }
      }

      // Template Editor Functions
      function initializeTemplateEditor() {
        // Template editor is now simplified - no preview functionality needed
        console.log('Template editor initialized');
      }

      // Webhook Form Toggle Functions
      function showWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        const toggleBtn = document.getElementById('toggleWebhookFormBtn');

        formSection.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-minus" style="animation: none;"></i> Hide Form';
        toggleBtn.classList.remove('btn-success');
        toggleBtn.classList.add('btn-outline-secondary');

        // Scroll to form
        formSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      function hideWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        const toggleBtn = document.getElementById('toggleWebhookFormBtn');
        const form = document.getElementById('webhookForm');

        formSection.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-plus" style="animation: none;"></i> Add New Webhook';
        toggleBtn.classList.remove('btn-outline-secondary');
        toggleBtn.classList.add('btn-success');

        // Reset form if not in edit mode
        if (!form.getAttribute('data-edit-id')) {
          form.reset();
          document.getElementById('webhookMethod').value = 'POST';
          document.getElementById('webhookHeaders').value = '';
          document.getElementById('webhookTemplate').value = '';
        }
      }

      function toggleWebhookForm() {
        const formSection = document.getElementById('webhookFormSection');
        if (formSection.style.display === 'none' || formSection.style.display === '') {
          showWebhookForm();
        } else {
          hideWebhookForm();
        }
      }

      // Add event listeners for form toggle buttons
      document.getElementById('toggleWebhookFormBtn').addEventListener('click', toggleWebhookForm);
      document.getElementById('closeFormBtn').addEventListener('click', hideWebhookForm);
      document.getElementById('cancelFormBtn').addEventListener('click', hideWebhookForm);

      // Add event listener for HTTP method dropdown to change colors like Postman
      const methodDropdown = document.getElementById('webhookMethod');

      function updateMethodColor() {
        const selectedMethod = methodDropdown.value;
        const colors = {
          'GET': '#61affe',
          'POST': '#49cc90',
          'PUT': '#fca130',
          'PATCH': '#50e3c2',
          'DELETE': '#f93e3e'
        };

        methodDropdown.style.color = colors[selectedMethod] || '#495057';
        methodDropdown.style.fontWeight = '600';
      }

      methodDropdown.addEventListener('change', updateMethodColor);

      // Set initial color
      updateMethodColor();

      // Add webhook form submission
      webhookForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const url = document.getElementById('webhookUrl').value;
        const description = document.getElementById('webhookDescription').value;
        const httpMethod = document.getElementById('webhookMethod').value;
        const headersText = document.getElementById('webhookHeaders').value;
        const templateText = document.getElementById('webhookTemplate').value;
        const editId = webhookForm.getAttribute('data-edit-id');

        // Validate that template is provided
        if (!templateText.trim()) {
          showToast('Webhook body template is required. Please provide a JSON template.', 'error');
          return;
        }

        // Parse and validate the headers if provided
        let headers = null;
        if (headersText.trim()) {
          try {
            headers = JSON.parse(headersText);
          } catch (error) {
            showToast('Invalid JSON headers format. Please check your headers syntax.', 'error');
            return;
          }
        }

        // Parse and validate the template
        let bodyTemplate = null;
        try {
          bodyTemplate = JSON.parse(templateText);
        } catch (error) {
          showToast('Invalid JSON template format. Please check your template syntax.', 'error');
          return;
        }

        try {
          const isEdit = !!editId;
          showToast(isEdit ? 'Updating webhook...' : 'Adding webhook...', 'info');

          const response = await fetch(
            isEdit ? `/face_recognition/webhooks/${editId}` : '/face_recognition/webhooks',
            {
              method: isEdit ? 'PUT' : 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                url,
                description,
                http_method: httpMethod,
                headers: headers,
                body_template: bodyTemplate
              }),
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Reset form and exit edit mode
          if (isEdit) {
            cancelEdit();
          } else {
            webhookForm.reset();
            document.getElementById('webhookMethod').value = 'POST';
            document.getElementById('webhookHeaders').value = '';
            document.getElementById('webhookTemplate').value = '';
            hideWebhookForm();
          }

          // Reload webhooks list
          loadWebhooks();

          showToast(isEdit ? 'Webhook updated successfully!' : 'Webhook added successfully!', 'success');
        } catch (error) {
          console.error('Error saving webhook:', error);
          showToast(`Failed to ${editId ? 'update' : 'add'} webhook: ${error.message}`, 'error');
        }
      });

      // Toggle WhatsApp active status
      async function toggleWhatsApp(button) {
        const whatsappId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} WhatsApp contact...`, 'info');

          const response = await fetch(`/face_recognition/whatsapp/${whatsappId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-whatsapp-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.whatsapp-status');
          statusCell.className = `whatsapp-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`WhatsApp contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling WhatsApp contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} WhatsApp contact. Please try again.`, 'error');
        }
      }

      // Confirm WhatsApp deletion
      function confirmDeleteWhatsApp(whatsappId) {
        showConfirmDialog('Are you sure you want to delete this WhatsApp contact?', whatsappId, deleteWhatsApp);
      }

      // Delete WhatsApp contact
      async function deleteWhatsApp(whatsappId) {
        try {
          showToast('Deleting WhatsApp contact...', 'info');

          const response = await fetch(`/face_recognition/whatsapp/${whatsappId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${whatsappId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.whatsapp-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('whatsappList').innerHTML = `
              <div class="empty-state">
                <i class="fab fa-whatsapp" style="animation: none;"></i>
                <h3>No WhatsApp Contacts Configured</h3>
                <p>Add a WhatsApp contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('WhatsApp contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting WhatsApp contact:', error);
          showToast('Failed to delete WhatsApp contact. Please try again.', 'error');
        }
      }

      // Add WhatsApp form submission
      const whatsappForm = document.getElementById('whatsappForm');
      whatsappForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone_number = document.getElementById('whatsappPhone').value;
        const full_name = document.getElementById('whatsappName').value;

        try {
          showToast('Adding WhatsApp contact...', 'info');

          const response = await fetch('/face_recognition/whatsapp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone_number, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          whatsappForm.reset();

          // Reload WhatsApp contacts list
          loadWhatsAppContacts();

          showToast('WhatsApp contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding WhatsApp contact:', error);
          showToast(`Failed to add WhatsApp contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Toggle SMS active status
      async function toggleSMS(button) {
        const smsId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} SMS contact...`, 'info');

          const response = await fetch(`/face_recognition/sms/${smsId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-sms-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.sms-status');
          statusCell.className = `sms-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`SMS contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling SMS contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} SMS contact. Please try again.`, 'error');
        }
      }

      // Confirm SMS deletion
      function confirmDeleteSMS(smsId) {
        showConfirmDialog('Are you sure you want to delete this SMS contact?', smsId, deleteSMS);
      }

      // Delete SMS contact
      async function deleteSMS(smsId) {
        try {
          showToast('Deleting SMS contact...', 'info');

          const response = await fetch(`/face_recognition/sms/${smsId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${smsId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.sms-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('smsList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-sms" style="animation: none;"></i>
                <h3>No SMS Contacts Configured</h3>
                <p>Add an SMS contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('SMS contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting SMS contact:', error);
          showToast('Failed to delete SMS contact. Please try again.', 'error');
        }
      }

      // Add SMS form submission
      const smsForm = document.getElementById('smsForm');
      smsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone_number = document.getElementById('smsPhone').value;
        const full_name = document.getElementById('smsName').value;

        try {
          showToast('Adding SMS contact...', 'info');

          const response = await fetch('/face_recognition/sms', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone_number, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          smsForm.reset();

          // Reload SMS contacts list
          loadSMSContacts();

          showToast('SMS contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding SMS contact:', error);
          showToast(`Failed to add SMS contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Refresh WhatsApp contacts button
      const refreshWhatsappBtn = document.getElementById('refreshWhatsappBtn');
      refreshWhatsappBtn.addEventListener('click', () => {
        loadWhatsAppContacts();
        showToast('Refreshing WhatsApp contacts...', 'info');
      });

      // Toggle Email active status
      async function toggleEmail(button) {
        const emailId = button.getAttribute('data-id');
        const isActive = button.getAttribute('data-active') === 'true';

        try {
          showToast(`${isActive ? 'Deactivating' : 'Activating'} Email contact...`, 'info');

          const response = await fetch(`/face_recognition/email/${emailId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ is_active: !isActive }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Update the button and row
          button.setAttribute('data-active', !isActive);
          button.innerHTML = `<i class="fas ${!isActive ? 'fa-pause' : 'fa-play'}" style="animation: none;"></i>`;
          button.className = `btn btn-sm ${!isActive ? 'btn-warning' : 'btn-success'} toggle-email-btn`;

          // Update status cell
          const row = button.closest('tr');
          const statusCell = row.querySelector('.email-status');
          statusCell.className = `email-status ${!isActive ? 'active' : 'inactive'}`;
          statusCell.textContent = !isActive ? 'Active' : 'Inactive';

          showToast(`Email contact ${!isActive ? 'activated' : 'deactivated'} successfully!`, 'success');
        } catch (error) {
          console.error('Error toggling Email contact:', error);
          showToast(`Failed to ${isActive ? 'deactivate' : 'activate'} Email contact. Please try again.`, 'error');
        }
      }

      // Confirm Email deletion
      function confirmDeleteEmail(emailId) {
        showConfirmDialog('Are you sure you want to delete this Email contact?', emailId, deleteEmail);
      }

      // Delete Email contact
      async function deleteEmail(emailId) {
        try {
          showToast('Deleting Email contact...', 'info');

          const response = await fetch(`/face_recognition/email/${emailId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Remove the row from the table
          const row = document.querySelector(`tr[data-id="${emailId}"]`);
          if (row) {
            row.remove();
          }

          // Check if there are any rows left
          const rows = document.querySelectorAll('.email-table tbody tr');
          if (rows.length === 0) {
            document.getElementById('emailList').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-envelope" style="animation: none;"></i>
                <h3>No Email Contacts Configured</h3>
                <p>Add an Email contact to receive system alerts and notifications.</p>
              </div>
            `;
          }

          showToast('Email contact deleted successfully!', 'success');
        } catch (error) {
          console.error('Error deleting Email contact:', error);
          showToast('Failed to delete Email contact. Please try again.', 'error');
        }
      }

      // Add Email form submission
      const emailForm = document.getElementById('emailForm');
      emailForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email_address = document.getElementById('emailAddress').value;
        const full_name = document.getElementById('emailName').value;

        try {
          showToast('Adding Email contact...', 'info');

          const response = await fetch('/face_recognition/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email_address, full_name }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Clear form
          emailForm.reset();

          // Reload Email contacts list
          loadEmailContacts();

          showToast('Email contact added successfully!', 'success');
        } catch (error) {
          console.error('Error adding Email contact:', error);
          showToast(`Failed to add Email contact: ${error.message}`, 'error');
        }
      });

      // Refresh webhooks button
      refreshWebhooksBtn.addEventListener('click', () => {
        loadWebhooks();
        showToast('Refreshing webhooks...', 'info');
      });

      // Refresh WhatsApp contacts button
      document.getElementById('refreshWhatsappBtn').addEventListener('click', () => {
        loadWhatsAppContacts();
        showToast('Refreshing WhatsApp contacts...', 'info');
      });

      // Refresh SMS contacts button
      document.getElementById('refreshSmsBtn').addEventListener('click', () => {
        loadSMSContacts();
        showToast('Refreshing SMS contacts...', 'info');
      });

      // Refresh Email contacts button
      document.getElementById('refreshEmailBtn').addEventListener('click', () => {
        loadEmailContacts();
        showToast('Refreshing Email contacts...', 'info');
      });

      // Make loadSettings available to call when the tab is activated
      // loadWebhooks, loadWhatsAppContacts, loadSMSContacts, and loadEmailContacts are already available globally
      return { loadSettings };
    }

    // Clustering Functions
    let isClusteredView = false;

    function setupClusteringFunctions() {
      const clusterBtn = document.getElementById('clusterBtn');
      const viewModeBtn = document.getElementById('viewModeBtn');
      const viewModeText = document.getElementById('viewModeText');
      const minClusterSizeInput = document.getElementById('minClusterSize');

      // Only add event listeners if elements exist
      if (clusterBtn) {
        // Cluster button click handler
        clusterBtn.addEventListener('click', async () => {
        try {
          showToast('Auto-clustering faces...', 'info');

          const minClusterSize = parseInt(minClusterSizeInput.value) || 2;

          const response = await fetch('/face_recognition/cluster-unknowns', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              min_cluster_size: minClusterSize
            })
          });

          const result = await response.json();

          if (result.success) {
            // Show success message
            showToast(result.message, 'success');

            // If images were removed, show additional information
            if (result.removed_images && result.removed_images > 0) {
              console.log(`${result.removed_images} images without faces were automatically removed`);
            }

            // Switch to clustered view
            isClusteredView = true;
            viewModeText.textContent = 'Switch to Individual View';
            loadClusteredUnknowns();
          } else {
            // Show error message
            if (result.removed_images && result.removed_images > 0) {
              showToast(`Clustering failed but ${result.removed_images} images without faces were removed. ${result.message}`, 'warning');
            } else {
              showToast(result.message || 'Clustering failed', 'error');
            }
          }
        } catch (error) {
          console.error('Error clustering faces:', error);
          showToast('Failed to cluster faces. Please try again.', 'error');
        }
        });
      }

      // View mode button click handler
      if (viewModeBtn && viewModeText) {
        viewModeBtn.addEventListener('click', () => {
          isClusteredView = !isClusteredView;

          if (isClusteredView) {
            viewModeText.textContent = 'Switch to Individual View';
            loadClusteredUnknowns();
          } else {
            viewModeText.textContent = 'Switch to Clustered View';
            loadUnknowns();
          }
        });
      }
    }

    // Function to load clustered unknown faces
    async function loadClusteredUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading clustered faces...</p>
        </div>
      `;

      try {
        // First, get the cluster name mapping
        const clusterRes = await fetch('/face_recognition/get-available-clusters');
        const clusterData = await clusterRes.json();

        // Create a mapping from cluster IDs to display names
        const clusterNameMap = {};
        for (const cluster of clusterData.clusters) {
          clusterNameMap[cluster.id] = cluster.display_name || `Cluster ${cluster.id.split('_').pop()}`;
        }

        // Now get the clustered unknowns
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        // Clear container
        container.innerHTML = '';

        const clusters = data.clusters;
        const unclustered = data.unclustered;

        // Check if there are any clusters or unclustered faces
        if (Object.keys(clusters).length === 0 && unclustered.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Display clusters
        if (Object.keys(clusters).length > 0) {
          const clusterSection = document.createElement('div');
          clusterSection.className = 'cluster-section';

          const clusterHeader = document.createElement('h3');
          clusterHeader.className = 'section-title';
          clusterHeader.innerHTML = `<i class="fas fa-object-group" style="animation: none;"></i> Clustered Faces`;
          clusterSection.appendChild(clusterHeader);

          // Sort clusters by size (number of faces)
          const sortedClusters = Object.entries(clusters).sort((a, b) => b[1].length - a[1].length);

          for (const [clusterId, faces] of sortedClusters) {
            const clusterCard = document.createElement('div');
            clusterCard.className = 'cluster-card';
            clusterCard.setAttribute('data-cluster-id', clusterId);

            // Create image section
            const imageSection = faces.map(face =>
              face.image_paths.map(path => {
                // Skip invalid paths
                if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
                  console.error('Invalid image path:', path);
                  return '';
                }

                // Sanitize path - replace backslashes with forward slashes
                const sanitizedPath = path.replace(/\\/g, '/');
                // Encode the path to prevent issues with special characters
                const encodedPath = sanitizedPath.replace(/'/g, "\\'");

                return `<div class="image-container">
                  <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                  <div class="image-actions">
                    <button class="move-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleMoveImageClick(this, event)">
                      <i class="fas fa-exchange-alt" style="animation: none;"></i>
                    </button>
                    <button class="delete-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleDeleteImageClick(this, event)">
                      <i class="fas fa-trash-alt" style="animation: none;"></i>
                    </button>
                  </div>
                </div>`;
              }).join('')
            ).join('');

            clusterCard.innerHTML = `
              <div class="cluster-header">
                <div class="cluster-title">
                  <i class="fas fa-users" style="animation: none;"></i>
                  ${clusterNameMap[clusterId] || `Cluster Group`}
                  <span class="cluster-count">${faces.length} faces</span>
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteCluster('${clusterId}')">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete Cluster
                </button>
              </div>
              <div class="cluster-faces">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-cluster" class="assign-form" id="cluster-form-${clusterId}">
                  <input type="hidden" name="cluster_id" value="${clusterId}">
                  <div class="form-group">
                    <label><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" name="username" class="form-control" placeholder="Enter username">
                  </div>
                  <div class="form-group">
                    <label><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" name="email" class="form-control" placeholder="Enter email address">
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign All to User
                    </button>
                    <small class="form-text text-muted" style="margin-top: 8px;">
                      <i class="fas fa-info-circle"></i> This will assign all ${faces.length} faces in this cluster to the same user.
                    </small>
                  </div>
                </form>
              </div>
            `;

            clusterSection.appendChild(clusterCard);

            // Add form submission handler
            setTimeout(() => {
              const form = document.getElementById(`cluster-form-${clusterId}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignCluster(this, clusterId);
                });
              }
            }, 0);
          }

          container.appendChild(clusterSection);
        }

        // Display unclustered faces
        if (unclustered.length > 0) {
          const unclusteredSection = document.createElement('div');
          unclusteredSection.className = 'unclustered-section';

          const unclusteredHeader = document.createElement('h3');
          unclusteredHeader.className = 'section-title';
          unclusteredHeader.innerHTML = `<i class="fas fa-user-question" style="animation: none;"></i> Unclustered Faces <span class="cluster-count">${unclustered.length} faces</span>`;
          unclusteredSection.appendChild(unclusteredHeader);

          // Create cards for each unclustered face
          unclustered.forEach(person => {
            const card = document.createElement('div');
            card.className = 'card';
            card.setAttribute('data-id', person.id);

            const imageSection = person.image_paths.map(path => {
              // Skip invalid paths
              if (!path || path === '' || path === '.jpg' || path.endsWith('/.jpg')) {
                console.error('Invalid image path:', path);
                return '';
              }

              // Sanitize path - replace backslashes with forward slashes
              const sanitizedPath = path.replace(/\\/g, '/');
              // Encode the path to prevent issues with special characters
              const encodedPath = sanitizedPath.replace(/'/g, "\\'");

              return `<div class="image-container">
                <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                <div class="image-actions">
                  <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                    <i class="fas fa-exchange-alt" style="animation: none;"></i>
                  </button>
                  <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </div>
              </div>`;
            }).join('');

            card.innerHTML = `
              <div class="card-header">
                <div class="card-title">
                  <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
                </button>
              </div>
              <div class="card-images">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                  <input type="hidden" name="unknown_id" value="${person.id}">

                  <!-- Mode selection buttons -->
                  <div class="mode-selection" style="margin-bottom: 15px;">
                    <div class="btn-group" role="group" style="width: 100%;">
                      <button type="button" class="btn btn-outline-primary mode-btn" data-mode="existing" onclick="switchMode(${person.id}, 'existing')" style="width: 50%;">
                        <i class="fas fa-user-check" style="animation: none;"></i> Existing User
                      </button>
                      <button type="button" class="btn btn-outline-primary mode-btn" data-mode="new" onclick="switchMode(${person.id}, 'new')" style="width: 50%;">
                        <i class="fas fa-user-plus" style="animation: none;"></i> New User
                      </button>
                    </div>
                  </div>

                  <!-- Loading indicator for potential matches -->
                  <div id="loading-matches-${person.id}" style="display: none; text-align: center; margin-bottom: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Checking for potential matches...
                  </div>

                  <!-- Potential matches section (initially hidden) -->
                  <div id="potential-matches-${person.id}" style="display: none; margin-bottom: 15px;">
                    <div class="alert alert-info">
                      <i class="fas fa-info-circle"></i> Potential matches found. Select a user from the dropdown or switch to "New User" mode.
                    </div>
                  </div>

                  <!-- Existing user selection (initially hidden) -->
                  <div id="existing-user-${person.id}" class="mode-container" style="display: none;">
                    <div class="form-group">
                      <label for="user-select-${person.id}"><i class="fas fa-users" style="animation: none;"></i> Select User</label>
                      <select id="user-select-${person.id}" class="form-control" onchange="userSelected(${person.id})">
                        <option value="">-- Select a user --</option>
                      </select>
                    </div>
                    <div class="selected-user-info" id="selected-user-info-${person.id}" style="display: none; margin-top: 10px;">
                      <div class="alert alert-success">
                        <strong>Selected User:</strong> <span id="selected-username-${person.id}"></span><br>
                        <strong>Email:</strong> <span id="selected-email-${person.id}"></span>
                        <input type="hidden" id="selected-username-input-${person.id}" name="username">
                        <input type="hidden" id="selected-email-input-${person.id}" name="email">
                      </div>
                    </div>
                  </div>

                  <!-- New user input (initially shown) -->
                  <div id="new-user-${person.id}" class="mode-container">
                    <div class="form-group">
                      <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                      <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username">
                    </div>
                    <div class="form-group">
                      <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                      <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address">
                    </div>
                  </div>

                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                    </button>
                  </div>
                </form>
              </div>
            `;

            unclusteredSection.appendChild(card);

            // Add form submission handler and initialize UI
            setTimeout(() => {
              const form = document.getElementById(`form-${person.id}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignUnknown(this, person.id);
                });

                // Initialize the UI - check for potential matches
                // Set the "New User" mode as default initially
                switchMode(person.id, 'new');

                // Check if this unknown person might match existing users
                checkUnknownMatches(person.id).then(result => {
                  if (result.is_likely_registered && result.matches.length > 0) {
                    // If there are potential matches, switch to existing user mode
                    switchMode(person.id, 'existing');
                  }
                });
              }
            }, 100);
          });

          container.appendChild(unclusteredSection);
        }
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load clustered faces. Please try again.</p>
            <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Function to confirm deletion of a cluster
    function confirmDeleteCluster(clusterId) {
      showConfirmDialog('Are you sure you want to delete all faces in this cluster?', clusterId, deleteCluster);
    }

    // Function to delete a cluster
    async function deleteCluster(clusterId) {
      try {
        showToast('Deleting cluster...', 'info');

        // Get all unknown IDs in this cluster
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        if (!data.clusters[clusterId]) {
          showToast('Cluster not found', 'error');
          return;
        }

        const unknownIds = data.clusters[clusterId].map(face => face.id);

        // Delete each unknown in the cluster
        let successCount = 0;

        for (const id of unknownIds) {
          try {
            const deleteResponse = await fetch(`/face_recognition/delete-unknown/${id}`, {
              method: 'DELETE'
            });

            if (deleteResponse.ok) {
              successCount++;
            }
          } catch (error) {
            console.error(`Error deleting unknown ID ${id}:`, error);
          }
        }

        if (successCount > 0) {
          showToast(`Successfully deleted ${successCount} of ${unknownIds.length} faces`, 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast('Failed to delete cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete cluster. Please try again.', 'error');
      }
    }

    // Function to assign a cluster to a user
    async function assignCluster(form, clusterId) {
      try {
        // Validate the form inputs
        const usernameInput = form.querySelector('input[name="username"]');
        const emailInput = form.querySelector('input[name="email"]');

        if (!usernameInput.value) {
          showToast('Please enter a username', 'error');
          usernameInput.focus();
          return;
        }

        if (!emailInput.value) {
          showToast('Please enter an email address', 'error');
          emailInput.focus();
          return;
        }

        // Create a new FormData object
        const formData = new FormData();

        // Add the cluster_id
        formData.append('cluster_id', clusterId);
        formData.append('username', usernameInput.value);
        formData.append('email', emailInput.value);

        console.log(`Assigning cluster ${clusterId} to user: ${usernameInput.value}, email: ${emailInput.value}`);

        showToast('Assigning cluster to user...', 'info');

        const response = await fetch('/face_recognition/assign-cluster', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Cluster assigned successfully!', 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign cluster. Please try again.', 'error');
      }
    }

    // Initialize the page
    // Create settings manager first to avoid reference error
    const settingsManager = setupSettingsManagement();

    // Setup other components
    loadUnknowns();
    setupTabs();
    setupCameraManagement();
    setupUserRegistration();
    setupUserManagement();
    setupClusteringFunctions();

    // Load settings data
    loadSettings();

    // Function to directly check and restore the active settings section
    function checkAndRestoreSettingsSection() {
      // Only run this if we're on the settings tab
      const settingsTab = document.getElementById('settings-tab');
      if (settingsTab && settingsTab.classList.contains('active')) {
        console.log('On settings tab, checking for saved section');
        const savedSection = localStorage.getItem('activeSettingsSection');
        if (savedSection) {
          console.log('Found saved section:', savedSection);
          // Get all settings tiles and sections
          const settingsTiles = document.querySelectorAll('.settings-tile');
          const settingsSections = document.querySelectorAll('.settings-section');

          // Remove active class from all tiles and sections
          settingsTiles.forEach(t => t.classList.remove('active'));
          settingsSections.forEach(s => s.classList.remove('active'));

          // Add active class to the tile with matching data-settings-section
          const targetTile = document.querySelector(`.settings-tile[data-settings-section="${savedSection}"]`);
          if (targetTile) {
            targetTile.classList.add('active');
          }

          // Show corresponding section
          const section = document.getElementById(`${savedSection}-settings`);
          if (section) {
            section.classList.add('active');
            console.log('Activated section:', savedSection);

            // If alert management section is activated, load appropriate data
            if (savedSection === 'alert-management') {
              // Check which subsection is active
              const savedSubsection = localStorage.getItem('activeSettingsSubsection');
              if (savedSubsection === 'webhook') {
                if (typeof window.loadWebhooks === 'function') {
                  window.loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    safeInitializeTemplateEditor();
                  }, 500);
                }
              } else if (savedSubsection === 'whatsapp') {
                if (typeof window.loadWhatsAppContacts === 'function') {
                  window.loadWhatsAppContacts();
                }
              } else if (savedSubsection === 'sms') {
                if (typeof window.loadSMSContacts === 'function') {
                  window.loadSMSContacts();
                }
              } else if (savedSubsection === 'email') {
                if (typeof window.loadEmailContacts === 'function') {
                  window.loadEmailContacts();
                }
              } else {
                // Default to webhooks if no subsection is saved
                if (typeof window.loadWebhooks === 'function') {
                  window.loadWebhooks();
                  // Initialize template editor when webhook subsection is activated
                  setTimeout(() => {
                    safeInitializeTemplateEditor();
                  }, 500);
                }
              }
            }
          }
        }
      }
    }

    // Run the check after a short delay to ensure everything is loaded
    setTimeout(checkAndRestoreSettingsSection, 100);

    // Function to confirm deletion of a single image
    function confirmDeleteImage(imagePath, unknownId, e) {
      console.log("confirmDeleteImage called with:", imagePath, unknownId);

      // Stop event propagation to prevent enlargeImage from being called
      if (e) {
        e.stopPropagation();
        e.preventDefault();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }

      // Fix backslashes in the path (replace \ with /)
      const fixedPath = imagePath.replace(/\\/g, '/');
      console.log("Fixed path:", fixedPath);

      showConfirmDialog('Are you sure you want to delete this image?', { path: fixedPath, id: unknownId }, deleteImage);
    }

    // Function to delete a single image
    async function deleteImage(data) {
      try {
        console.log("deleteImage called with data:", data);
        showToast('Deleting image...', 'info');

        const url = `/face_recognition/delete-unknown-image?image_path=${encodeURIComponent(data.path)}`;
        console.log("Sending DELETE request to:", url);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log("Response status:", response.status);
        const result = await response.json();
        console.log("Response data:", result);

        if (response.ok) {
          showToast(result.message, 'success');

          // Find and remove the image container from the UI
          const imageContainers = document.querySelectorAll('.image-container');
          for (const container of imageContainers) {
            if (container.querySelector('img').src.includes(data.path)) {
              container.remove();
              break;
            }
          }

          // If the record was deleted (no images left), remove the entire card
          if (result.record_deleted) {
            // In individual view
            const card = document.querySelector(`[data-id="${data.id}"]`);
            if (card) {
              card.remove();

              // Check if there are any cards left
              if (container.children.length === 0) {
                container.innerHTML = `
                  <div class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <h3>No Unknown Persons</h3>
                    <p>There are currently no unknown persons to manage.</p>
                    <button class="btn btn-primary" onclick="refreshView()">
                      <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                  </div>
                `;
              }
            }

            // In clustered view
            if (isClusteredView) {
              // Refresh the clustered view to reflect the changes
              loadClusteredUnknowns();
            }
          }
        } else {
          showToast(result.detail || 'Failed to delete image', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete image. Please try again.', 'error');
      }
    }

    // Function to handle delete image button click
    function handleDeleteImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleDeleteImageClick called with path:", path, "id:", id);

      // Call confirmDeleteImage with the path and id
      confirmDeleteImage(path, id);
    }

    // Function to refresh the current view based on the view mode
    function refreshView() {
      if (isClusteredView) {
        loadClusteredUnknowns();
      } else {
        loadUnknowns();
      }
    }

    // Make functions available globally
    window.loadUnknowns = loadUnknowns;
    window.loadClusteredUnknowns = loadClusteredUnknowns;
    window.confirmDeleteUnknown = confirmDeleteUnknown;
    window.deleteUnknown = deleteUnknown;
    window.confirmDeleteCluster = confirmDeleteCluster;
    window.deleteCluster = deleteCluster;
    window.assignCluster = assignCluster;
    window.enlargeImage = enlargeImage;
    window.confirmDeleteImage = confirmDeleteImage;
    window.deleteImage = deleteImage;
    window.handleDeleteImageClick = handleDeleteImageClick;
    window.refreshView = refreshView;
    window.loadCameras = loadCameras;
    window.confirmDeleteCamera = confirmDeleteCamera;
    window.deleteCamera = deleteCamera;
    window.loadUsers = loadUsers;
    window.confirmDeleteUser = confirmDeleteUser;
    window.deleteUser = deleteUser;
    window.viewUserDetails = viewUserDetails;

    // Functions for moving images between clusters

    // Function to handle move image button click
    function handleMoveImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleMoveImageClick called with path:", path, "id:", id);

      // Show the move image modal
      showMoveImageModal(id);
    }

    // Function to show the move image modal
    async function showMoveImageModal(unknownId) {
      // Get the modal and clusters list
      const modal = document.getElementById('moveImageModal');
      const clustersList = document.getElementById('clustersList');

      // Show loading state
      clustersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      `;

      // Show the modal
      modal.style.display = 'block';

      try {
        // Fetch available clusters
        const response = await fetch('/face_recognition/get-available-clusters');
        const data = await response.json();

        if (!response.ok) {
          throw new Error('Failed to fetch clusters');
        }

        // Get the current cluster for this unknown
        const unknownResponse = await fetch('/face_recognition/get-clustered-unknowns');
        const unknownData = await unknownResponse.json();

        let currentClusterId = null;

        // Find the current cluster for this unknown
        for (const clusterId in unknownData.clusters) {
          const unknowns = unknownData.clusters[clusterId];
          for (const unknown of unknowns) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = clusterId;
              break;
            }
          }
          if (currentClusterId) break;
        }

        // If not found in clusters, check unclustered
        if (!currentClusterId) {
          for (const unknown of unknownData.unclustered) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = 'unclustered';
              break;
            }
          }
        }

        // Build the clusters list
        let clustersHtml = '';

        // Add each cluster as a button
        for (const cluster of data.clusters) {
          // Skip the current cluster
          if (cluster.id === currentClusterId) continue;

          clustersHtml += `
            <button class="cluster-option" onclick="moveImageToCluster(${unknownId}, '${cluster.id}')">
              <i class="fas ${cluster.is_unclustered ? 'fa-users-slash' : 'fa-users'}"></i>
              ${cluster.display_name || cluster.name}
            </button>
          `;
        }

        if (clustersHtml === '') {
          clustersList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-info-circle"></i>
              <p>No other clusters available to move to.</p>
            </div>
          `;
        } else {
          clustersList.innerHTML = clustersHtml;
        }
      } catch (error) {
        console.error('Error fetching clusters:', error);
        clustersList.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Error loading clusters. Please try again.</p>
          </div>
        `;
      }
    }

    // Function to move an image to a different cluster
    async function moveImageToCluster(unknownId, targetClusterId) {
      try {
        showToast('Moving image...', 'info');

        // Close the modal
        document.getElementById('moveImageModal').style.display = 'none';

        // Send the request to move the image
        const response = await fetch(`/face_recognition/move-to-cluster?unknown_id=${unknownId}&target_cluster_id=${encodeURIComponent(targetClusterId)}`, {
          method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message, 'success');

          // Refresh the view to reflect the changes
          refreshView();
        } else {
          showToast(result.detail || 'Failed to move image', 'error');
        }
      } catch (error) {
        console.error('Error moving image:', error);
        showToast('Failed to move image. Please try again.', 'error');
      }
    }

    // Add the move image functions to the global scope
    window.handleMoveImageClick = handleMoveImageClick;
    window.showMoveImageModal = showMoveImageModal;
    window.moveImageToCluster = moveImageToCluster;
  </script>

  <!-- Move Image Modal -->
  <div id="moveImageModal" class="modal">
    <div class="modal-content" style="max-width: 500px;">
      <span class="close" onclick="document.getElementById('moveImageModal').style.display='none'">&times;</span>
      <h2>Move Image to Another Cluster</h2>
      <p>Select the target cluster to move this image to:</p>
      <div id="clustersList" class="clusters-list">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="document.getElementById('moveImageModal').style.display='none'">Cancel</button>
      </div>
    </div>
  </div>
</body>
</html>
