<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel - Face Recognition System</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #1abc9c;
      --danger-color: #e74c3c;
      --warning-color: #f39c12;
      --success-color: #2ecc71;
      --text-light: #ecf0f1;
      --text-dark: #2c3e50;
      --bg-light: #f5f7fa;
      --bg-dark: #34495e;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    .header {
      background-color: var(--primary-color);
      color: var(--text-light);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--shadow);
    }

    .header-title {
      font-size: 20px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #c0392b;
      transform: translateY(-2px);
    }

    .btn-success {
      background-color: var(--success-color);
      color: white;
    }

    .btn-success:hover {
      background-color: #27ae60;
      transform: translateY(-2px);
    }

    .page-title {
      text-align: center;
      margin: 30px 0;
      color: var(--primary-color);
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);
    }

    .page-title:after {
      content: '';
      position: absolute;
      width: 60px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px 40px;
    }

    .card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .card-images img {
      max-width: 140px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .card-images img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .form-container {
      background-color: var(--bg-light);
      padding: 20px;
      border-radius: 8px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: white;
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    .empty-state {
      text-align: center;
      padding: 50px 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .empty-state i {
      font-size: 48px;
      color: var(--secondary-color);
      margin-bottom: 20px;
    }

    .empty-state h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #666;
      margin-bottom: 20px;
    }

    .loading {
      text-align: center;
      padding: 30px;
      color: var(--primary-color);
    }

    /* Admin Tabs */
    .admin-tabs {
      display: flex;
      background-color: var(--bg-light);
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 25px;
      box-shadow: var(--shadow);
    }

    .tab-btn {
      flex: 1;
      padding: 15px;
      background-color: transparent;
      border: none;
      cursor: pointer;
      font-weight: 500;
      color: var(--text-dark);
      transition: var(--transition);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-bottom: 3px solid transparent;
    }

    .tab-btn i {
      font-size: 18px;
    }

    .tab-btn:hover {
      background-color: rgba(52, 152, 219, 0.1);
    }

    .tab-btn.active {
      background-color: white;
      border-bottom: 3px solid var(--secondary-color);
      color: var(--secondary-color);
      font-weight: 600;
    }

    .tab-content {
      position: relative;
    }

    .tab-pane {
      display: none;
    }

    .tab-pane.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    /* Form Styling */
    .form-group {
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
      background-color: var(--bg-light);
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
      outline: none;
    }

    .file-input-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .file-status {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }

    /* Camera List Section */
    .camera-list-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-header h3 {
      font-size: 18px;
      color: var(--primary-color);
      margin: 0;
    }

    .camera-list, .users-list {
      background-color: var(--bg-light);
      border-radius: 8px;
      padding: 15px;
      min-height: 100px;
    }

    .camera-table, .users-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }

    .camera-table th, .users-table th,
    .camera-table td, .users-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .camera-table th, .users-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .camera-table th:first-child, .users-table th:first-child {
      border-top-left-radius: 8px;
    }

    .camera-table th:last-child, .users-table th:last-child {
      border-top-right-radius: 8px;
    }

    .camera-table tr:last-child td, .users-table tr:last-child td {
      border-bottom: none;
    }

    .camera-table tr:hover, .users-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    .camera-url, .user-email {
      font-size: 12px;
      color: #666;
      word-break: break-all;
    }

    .btn-sm {
      padding: 6px 10px;
      font-size: 12px;
    }

    /* User Management Styles */
    .user-management-controls {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .search-box {
      display: flex;
      gap: 10px;
      width: 60%;
    }

    .search-box input {
      flex-grow: 1;
    }

    .user-actions {
      display: flex;
      gap: 5px;
    }

    .user-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #eee;
      transition: var(--transition);
    }

    .user-image:hover {
      transform: scale(1.1);
      border-color: var(--secondary-color);
    }

    /* Clustering Styles */
    .unknown-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      background-color: white;
      padding: 15px;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .control-buttons {
      display: flex;
      gap: 10px;
    }

    .cluster-settings {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .cluster-settings .form-group {
      margin-bottom: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .cluster-settings .form-control {
      width: 70px;
      padding: 8px;
    }

    .cluster-settings label {
      margin-bottom: 0;
      white-space: nowrap;
      cursor: help;
    }

    .cluster-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.85rem;
      color: #666;
      background-color: rgba(243, 156, 18, 0.1);
      padding: 6px 10px;
      border-radius: 4px;
      border-left: 3px solid var(--warning-color);
    }

    .cluster-card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
      border-left: 5px solid var(--secondary-color);
    }

    .cluster-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .cluster-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .cluster-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .cluster-count {
      background-color: var(--secondary-color);
      color: white;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .cluster-faces {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 20px;
    }

    .cluster-faces img {
      max-width: 120px;
      border-radius: 8px;
      border: 1px solid #eee;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .cluster-faces img:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Modal Styling */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(4px);
      animation: fadeIn 0.3s ease;
    }

    .modal-content {
      background-color: #fff;
      margin: 5% auto;
      padding: 30px;
      border: none;
      width: 500px;
      max-width: 90%;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      position: relative;
      animation: slideIn 0.3s ease;
    }

    .modal-content h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 25px;
      text-align: center;
      position: relative;
    }

    .modal-content h3:after {
      content: '';
      position: absolute;
      width: 50px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .modal-content button {
      margin-bottom: 10px;
    }

    /* Camera Container */
    #camera-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 2000;
      transition: opacity 0.3s ease, visibility 0.3s ease;
      display: none;
    }

    .camera-content {
      background-color: white;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      max-width: 500px;
      width: 90%;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .camera-content h3 {
      color: var(--primary-color);
      margin-bottom: 20px;
      font-size: 24px;
    }

    .camera-buttons {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 15px;
      margin-bottom: 15px;
    }

    #video, #image-preview {
      width: 100%;
      max-width: 400px;
      border-radius: 8px;
      box-shadow: var(--shadow);
      background-color: #f0f0f0;
      margin: 0 auto;
      display: block;
    }

    #video {
      height: 300px;
      object-fit: cover;
    }

    #image-preview {
      margin-top: 15px;
      border: 3px solid var(--success-color);
      max-height: 300px;
      object-fit: contain;
    }

    /* Only apply animation to the spinner icon */
    .fa-spinner.fa-spin {
      font-size: 40px;
      animation: spin 1s linear infinite;
    }

    /* Explicitly disable animation for all other icons */
    .fas:not(.fa-spin) {
      animation: none !important;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Confirmation Dialog */
    .confirm-dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1100;
      display: none;
      justify-content: center;
      align-items: center;
    }

    .confirm-dialog::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      backdrop-filter: blur(3px);
    }

    .confirm-dialog h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: var(--primary-color);
      position: relative;
      font-size: 24px;
    }

    .confirm-dialog p {
      margin-bottom: 20px;
      color: var(--text-dark);
      position: relative;
      font-size: 16px;
      line-height: 1.5;
    }

    .confirm-dialog-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      width: 400px;
      max-width: 90%;
      text-align: center;
      position: relative;
      z-index: 1101;
      animation: dialogFadeIn 0.3s ease;
    }

    @keyframes dialogFadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .confirm-dialog-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      position: relative;
      margin-top: 25px;
    }

    .confirm-dialog-buttons button {
      min-width: 100px;
      padding: 10px 15px;
      font-weight: 500;
    }

    /* Toast Notification */
    .toast {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      background-color: var(--primary-color);
      color: white;
      padding: 12px 25px;
      border-radius: 8px;
      z-index: 1200;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: opacity 0.3s, transform 0.3s;
      pointer-events: none;
    }

    .toast.show {
      opacity: 1;
      transform: translate(-50%, -10px);
    }

    .toast.success {
      background-color: var(--success-color);
    }

    .toast.error {
      background-color: var(--danger-color);
    }

    .toast.info {
      background-color: var(--secondary-color);
    }

    /* Responsive Design */
    /* Card Images and Image Container Styles */
    .card-images {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;
    }

    .image-container {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: var(--transition);
    }

    .image-container:hover {
      transform: scale(1.05);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .card-images img, .cluster-faces img, .image-container img {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #eee;
      transition: var(--transition);
    }

    .image-actions {
      position: absolute;
      top: 5px;
      right: 5px;
      display: flex;
      gap: 5px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .image-container:hover .image-actions {
      opacity: 1;
    }

    .delete-image-btn, .move-image-btn {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .delete-image-btn {
      background-color: rgba(220, 53, 69, 0.8);
    }

    .move-image-btn {
      background-color: rgba(0, 123, 255, 0.8);
    }

    .delete-image-btn i, .move-image-btn i {
      font-size: 12px;
    }

    .delete-image-btn:hover {
      background-color: rgba(220, 53, 69, 1);
    }

    .move-image-btn:hover {
      background-color: rgba(0, 123, 255, 1);
    }

    /* Cluster options in move modal */
    .clusters-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin: 15px 0;
      max-height: 300px;
      overflow-y: auto;
    }

    .cluster-option {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 15px;
      background-color: var(--light-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
      font-size: 14px;
    }

    .cluster-option:hover {
      background-color: var(--primary-color);
      color: white;
    }

    .cluster-option i {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    @media (max-width: 768px) {
      .form-actions {
        flex-direction: column;
      }

      .card-images img, .image-container, .cluster-faces img {
        max-width: 100px;
        max-height: 100px;
      }
    }
  </style>
</head>
<body data-role="admin">
  <!-- Header -->
  <div class="header">
    <div class="header-title">Face Recognition System</div>
    <div class="header-actions">
      <button class="btn btn-secondary" id="backBtn">
        <i class="fas fa-arrow-left" style="animation: none;"></i> Back to Dashboard
      </button>
      <button class="btn btn-primary" id="refreshBtn">
        <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh Data
      </button>
    </div>
  </div>

  <h1 class="page-title">Administrator Panel</h1>

  <div class="container">
    <!-- Admin Tabs -->
    <div class="admin-tabs">
      <button class="tab-btn active" data-tab="unknown-persons">
        <i class="fas fa-user-question" style="animation: none;"></i> Unknown Persons
      </button>
      <button class="tab-btn" data-tab="user-registration">
        <i class="fas fa-user-plus" style="animation: none;"></i> User Registration
      </button>
      <button class="tab-btn" data-tab="user-management">
        <i class="fas fa-users-cog" style="animation: none;"></i> User Management
      </button>
      <button class="tab-btn" data-tab="camera-management">
        <i class="fas fa-video" style="animation: none;"></i> Camera Management
      </button>
      <button class="tab-btn" data-tab="settings">
        <i class="fas fa-cog" style="animation: none;"></i> Settings
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Unknown Persons Tab -->
      <div class="tab-pane active" id="unknown-persons-tab">
        <div class="unknown-controls">
          <div class="control-buttons">
            <button id="clusterBtn" class="btn btn-primary">
              <i class="fas fa-object-group" style="animation: none;"></i> Auto-Cluster Similar Faces
            </button>
            <button id="viewModeBtn" class="btn btn-secondary">
              <i class="fas fa-th-large" style="animation: none;"></i> <span id="viewModeText">Switch to Clustered View</span>
            </button>
          </div>
          <div class="cluster-settings">
            <div class="form-group">
              <label for="minClusterSize" title="Minimum number of faces required to form a cluster. For small datasets, this will be automatically adjusted.">
                Min Cluster Size:
                <i class="fas fa-info-circle" style="animation: none;"></i>
              </label>
              <input type="number" id="minClusterSize" class="form-control" value="2" min="2" max="10">
            </div>
            <div class="cluster-info">
              <i class="fas fa-lightbulb" style="animation: none; color: var(--warning-color);"></i>
              <span>HDBSCAN automatically determines optimal clusters based on face similarity. For small datasets (2-3 faces), clustering may group all faces together.</span>
            </div>
          </div>
        </div>
        <div id="unknownContainer" class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      </div>

      <!-- User Registration Tab -->
      <div class="tab-pane" id="user-registration-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Register New User</h2>
            <p>Add a new user to the face recognition system</p>
          </div>

          <form id="registerForm" enctype="multipart/form-data">
            <div class="form-group">
              <label for="username"><i class="fas fa-user" style="animation: none;"></i> Username</label>
              <input type="text" id="username" name="username" class="form-control" placeholder="Enter username" required>
            </div>

            <div class="form-group">
              <label for="email"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
              <input type="email" id="email" name="email" class="form-control" placeholder="Enter email address" required>
            </div>

            <div class="form-group">
              <label for="profile_image"><i class="fas fa-camera" style="animation: none;"></i> Profile Image</label>
              <div class="file-input-group">
                <button type="button" id="choose-file-btn" class="btn btn-primary">
                  <i class="fas fa-image" style="animation: none;"></i> Select Profile Image
                </button>
                <input type="file" id="upload-input" name="image_file" accept="image/*" style="display: none;" required>
                <input type="hidden" id="captured_image" name="captured_image">
                <div id="file-status" class="file-status">No file chosen</div>
              </div>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-user-plus" style="animation: none;"></i> Register User
            </button>
          </form>
        </div>
      </div>

      <!-- User Management Tab -->
      <div class="tab-pane" id="user-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">User Management</h2>
            <p>View and manage registered users</p>
          </div>

          <div class="user-management-controls">
            <div class="search-box">
              <input type="text" id="userSearchInput" class="form-control" placeholder="Search users...">
              <button class="btn btn-primary" id="searchUsersBtn">
                <i class="fas fa-search" style="animation: none;"></i> Search
              </button>
            </div>

            <button class="btn btn-primary" id="refreshUsersBtn">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
            </button>
          </div>

          <div id="usersList" class="users-list">
            <div class="loading">
              <i class="fas fa-spinner fa-spin"></i>
              <p>Loading users...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Camera Management Tab -->
      <div class="tab-pane" id="camera-management-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Camera Management</h2>
            <p>Add and manage cameras for face recognition</p>
          </div>

          <form id="cameraForm">
            <div class="form-group">
              <label for="cameraName"><i class="fas fa-tag" style="animation: none;"></i> Camera Name</label>
              <input type="text" id="cameraName" name="cameraName" class="form-control" placeholder="Enter a unique camera name" required>
            </div>

            <div class="form-group">
              <label for="rtspUrl"><i class="fas fa-link" style="animation: none;"></i> RTSP URL</label>
              <input type="text" id="rtspUrl" name="rtspUrl" class="form-control" placeholder="rtsp://username:password@ip:port/path" required>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-plus-circle" style="animation: none;"></i> Add Camera
            </button>
          </form>

          <div class="camera-list-section">
            <div class="section-header">
              <h3>Configured Cameras</h3>
              <button class="btn btn-primary" id="refreshCamerasBtn">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh List
              </button>
            </div>

            <div id="cameraList" class="camera-list">
              <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading cameras...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Tab -->
      <div class="tab-pane" id="settings-tab">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">System Settings</h2>
            <p>Configure face recognition system parameters</p>
          </div>

          <form id="settingsForm">
            <div class="form-group">
              <label for="faceThreshold">
                <i class="fas fa-sliders-h" style="animation: none;"></i> Face Recognition Threshold
                <small class="text-muted">(Higher values require more similarity for a match)</small>
              </label>
              <div class="threshold-container" style="display: flex; align-items: center; gap: 15px;">
                <input type="range" id="faceThreshold" name="faceThreshold" class="form-control" min="0.1" max="0.9" step="0.05" value="0.7">
                <span id="thresholdValue" style="min-width: 40px; font-weight: bold;">0.7</span>
              </div>
            </div>

            <button type="submit" class="btn btn-success">
              <i class="fas fa-save" style="animation: none;"></i> Save Settings
            </button>
          </form>

          <div id="settingsStatus" class="mt-3" style="display: none;">
            <div class="alert alert-success">
              <i class="fas fa-check-circle" style="animation: none;"></i> Settings saved successfully!
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-dialog-content">
      <h3>Confirm Action</h3>
      <p id="confirmMessage">Are you sure you want to perform this action?</p>
      <div class="confirm-dialog-buttons">
        <button class="btn btn-danger" id="confirmYes">Delete</button>
        <button class="btn btn-primary" id="confirmNo">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Modal for image selection -->
  <div id="modal" class="modal" style="display: none;">
    <div class="modal-content">
      <h3>Select Profile Image Source</h3>
      <button type="button" id="upload-btn" class="btn btn-primary">
        <i class="fas fa-upload" style="animation: none;"></i> Upload from Device
      </button>
      <button type="button" id="capture-btn" class="btn btn-success">
        <i class="fas fa-camera" style="animation: none;"></i> Take Picture with Camera
      </button>
      <button type="button" id="close-modal-btn" class="btn btn-secondary">
        <i class="fas fa-times" style="animation: none;"></i> Cancel
      </button>
    </div>
  </div>

  <!-- Camera Section -->
  <div id="camera-container">
    <div class="camera-content">
      <h3>Take Profile Picture</h3>
      <video id="video" autoplay playsinline></video>
      <div class="camera-buttons">
        <button type="button" id="capture-image-btn" class="btn btn-success">
          <i class="fas fa-camera" style="animation: none;"></i> Capture Image
        </button>
        <button type="button" id="close-camera-btn" class="btn btn-danger">
          <i class="fas fa-times" style="animation: none;"></i> Close Camera
        </button>
      </div>
      <canvas id="canvas" style="display: none;"></canvas>
      <img id="image-preview" alt="Captured Image Preview" style="display: none;">
    </div>
  </div>

  <script>
    // DOM Elements
    const backBtn = document.getElementById('backBtn');
    const refreshBtn = document.getElementById('refreshBtn');
    const container = document.getElementById('unknownContainer');
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmYes = document.getElementById('confirmYes');
    const confirmNo = document.getElementById('confirmNo');
    const confirmMessage = document.getElementById('confirmMessage');

    // State variables
    let pendingAction = null;
    let pendingId = null;

    // Event Listeners
    backBtn.addEventListener('click', () => {
      // Navigate back to the face recognition page without stopping the monitoring
      window.location.href = '/face_recognition/face_recognition';
    });

    refreshBtn.addEventListener('click', () => {
      showToast('Refreshing data...', 'info');
      loadUnknowns();
    });

    // Function to show confirmation dialog
    function showConfirmDialog(message, id, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.style.display = 'flex';

      // Store the callback and ID for later use
      pendingAction = onConfirm;
      pendingId = id;

      // Set up event listeners for the buttons
      confirmYes.onclick = () => {
        confirmDialog.style.display = 'none';
        if (pendingAction) {
          if (pendingId !== null) {
            pendingAction(pendingId);
          } else {
            pendingAction();
          }
        }
        pendingAction = null;
        pendingId = null;
      };

      confirmNo.onclick = () => {
        confirmDialog.style.display = 'none';
        pendingAction = null;
        pendingId = null;
      };

      // Also close when clicking outside the dialog content
      confirmDialog.addEventListener('click', function(event) {
        if (event.target === confirmDialog) {
          confirmDialog.style.display = 'none';
          pendingAction = null;
          pendingId = null;
        }
      }, { once: true });
    }

    // Function to show toast notification
    function showToast(message, type = 'info') {
      // Create toast element if it doesn't exist
      let toast = document.getElementById('toast');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        document.body.appendChild(toast);
      }

      // Set toast content and style based on type
      toast.textContent = message;
      toast.className = `toast ${type}`;

      // Show the toast
      toast.classList.add('show');

      // Hide after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }

    // Function to load unknown persons
    async function loadUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading data...</p>
        </div>
      `;

      try {
        const res = await fetch('/face_recognition/get-unknowns');
        const data = await res.json();

        if (!data.unknowns || data.unknowns.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Clear container
        container.innerHTML = '';

        // Create cards for each unknown person
        data.unknowns.forEach(person => {
          const card = document.createElement('div');
          card.className = 'card';
          card.setAttribute('data-id', person.id);

          const imageSection = person.image_paths.map(path => {
            // Sanitize path - replace backslashes with forward slashes
            const sanitizedPath = path.replace(/\\/g, '/');
            // Encode the path to prevent issues with special characters
            const encodedPath = sanitizedPath.replace(/'/g, "\\'");

            return `<div class="image-container">
              <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
              <div class="image-actions">
                <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                  <i class="fas fa-exchange-alt" style="animation: none;"></i>
                </button>
                <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </div>`;
          }).join('');

          card.innerHTML = `
            <div class="card-header">
              <div class="card-title">
                <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
              </div>
              <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </div>
            <div class="card-images">${imageSection}</div>
            <div class="form-container">
              <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                <input type="hidden" name="unknown_id" value="${person.id}">
                <div class="form-group">
                  <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                  <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username" required>
                </div>
                <div class="form-group">
                  <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                  <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address" required>
                </div>
                <div class="form-actions">
                  <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                  </button>
                  <small class="form-text text-muted" style="margin-top: 8px;">
                    <i class="fas fa-info-circle"></i> You can assign multiple images to the same user to improve recognition.
                  </small>
                </div>
              </form>
            </div>
          `;

          container.appendChild(card);

          // Add form submission handler
          const form = document.getElementById(`form-${person.id}`);
          form.addEventListener('submit', function(e) {
            e.preventDefault();
            assignUnknown(this, person.id);
          });
        });
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load unknown persons. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Function to assign an unknown person to a new user
    async function assignUnknown(form, id) {
      const formData = new FormData(form);

      try {
        showToast('Assigning user...', 'info');

        const response = await fetch('/face_recognition/assign-unknown', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User assigned successfully!', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign user', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign user. Please try again.', 'error');
      }
    }

    // Function to confirm deletion of an unknown person
    function confirmDeleteUnknown(id) {
      showConfirmDialog('Are you sure you want to delete this unknown person?', id, deleteUnknown);
    }

    // Function to delete an unknown person
    async function deleteUnknown(id) {
      try {
        showToast('Deleting unknown person...', 'info');

        const response = await fetch(`/face_recognition/delete-unknown/${id}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Unknown person deleted successfully', 'success');
          // Remove the card from UI
          document.querySelector(`[data-id="${id}"]`).remove();

          // Check if there are any cards left
          if (container.children.length === 0) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="refreshView()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast(result.detail || 'Failed to delete unknown person', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete unknown person. Please try again.', 'error');
      }
    }

    // Function to enlarge an image when clicked
    function enlargeImage(src) {
      const modal = document.createElement('div');
      modal.style.position = 'fixed';
      modal.style.top = '0';
      modal.style.left = '0';
      modal.style.width = '100%';
      modal.style.height = '100%';
      modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
      modal.style.display = 'flex';
      modal.style.justifyContent = 'center';
      modal.style.alignItems = 'center';
      modal.style.zIndex = '2000';
      modal.style.cursor = 'pointer';

      const img = document.createElement('img');
      img.src = src;
      img.style.maxWidth = '90%';
      img.style.maxHeight = '90%';
      img.style.borderRadius = '8px';
      img.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';

      // Handle image loading error with fallbacks
      img.onerror = function() {
        // Try to determine the username from the src
        const srcParts = src.split('/');
        const filename = srcParts[srcParts.length - 1];
        // Decode the username to handle spaces and special characters
        const username = decodeURIComponent(filename.split('.')[0]);

        console.log(`Image failed to load: ${src}`);

        // Try fallbacks in order
        if (src.includes('/static/images/')) {
          // If original image failed, try cropped face
          console.log(`Trying cropped face for ${username}`);
          // Encode the username again for the URL
          const encodedUsername = encodeURIComponent(username);
          this.src = `/cropped_faces/${encodedUsername}.jpg`;

          // If cropped face also fails, show error
          this.onerror = function() {
            console.error(`Failed to load any image for ${username}`);
            this.src = 'https://via.placeholder.com/200?text=No+Image';
          };
        } else {
          // For other images, just show placeholder
          this.src = 'https://via.placeholder.com/200?text=No+Image';
        }
      };

      modal.appendChild(img);
      document.body.appendChild(modal);

      modal.addEventListener('click', () => {
        document.body.removeChild(modal);
      });
    }

    // Tab functionality
    function setupTabs() {
      const tabBtns = document.querySelectorAll('.tab-btn');

      // Function to switch to a specific tab
      function switchToTab(tabId) {
        // Remove active class from all buttons and panes
        tabBtns.forEach(b => b.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

        // Add active class to the button with the matching data-tab attribute
        const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabId}"]`);
        if (targetBtn) {
          targetBtn.classList.add('active');

          // Show corresponding tab pane
          const tabPane = document.getElementById(`${tabId}-tab`);

          if (tabPane) {
            tabPane.classList.add('active');

            // Load data for the tab if needed
            if (tabId === 'unknown-persons') {
              loadUnknowns();
            } else if (tabId === 'camera-management') {
              loadCameras();
            } else if (tabId === 'user-management') {
              loadUsers();
            } else if (tabId === 'settings') {
              settingsManager.loadSettings();
            }

            // Update URL hash
            window.location.hash = tabId;
          } else {
            console.error(`Tab pane with ID "${tabId}-tab" not found`);
          }
        } else {
          console.error(`Tab button with data-tab="${tabId}" not found`);
        }
      }

      // Add click event listeners to tab buttons
      tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          const tabId = btn.getAttribute('data-tab');
          switchToTab(tabId);
        });
      });

      // Check for hash in URL on page load
      if (window.location.hash) {
        const tabId = window.location.hash.substring(1); // Remove the # character
        switchToTab(tabId);
      }
    }

    // Camera Management Functions
    function setupCameraManagement() {
      const cameraForm = document.getElementById('cameraForm');
      const refreshCamerasBtn = document.getElementById('refreshCamerasBtn');

      // Load cameras initially
      loadCameras();

      // Refresh cameras button
      refreshCamerasBtn.addEventListener('click', () => {
        loadCameras();
      });

      // Handle form submission to add the camera
      cameraForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const cameraName = document.getElementById('cameraName').value;
        const rtspUrl = document.getElementById('rtspUrl').value;

        try {
          showToast('Adding camera...', 'info');

          // Send data to backend to save the camera information
          const response = await fetch('/face_recognition/add-camera', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cameraName, rtspUrl }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            showToast('Camera added successfully!', 'success');
            cameraForm.reset();
            loadCameras();
          } else if (result.status === 'error') {
            showToast(`Error: RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
          } else if (result.status === 'samename') {
            showToast('Camera with this name already exists', 'error');
          } else {
            showToast('Error adding camera.', 'error');
          }
        } catch (error) {
          console.error('Error adding camera:', error);
          showToast('Failed to add camera. Please check your connection.', 'error');
        }
      });
    }

    // Function to load and display the list of cameras
    async function loadCameras() {
      const cameraList = document.getElementById('cameraList');
      cameraList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading cameras...</p>
        </div>
      `;

      try {
        const response = await fetch('/face_recognition/get-cameras', {
          method: 'GET',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        cameraList.innerHTML = ''; // Clear previous list

        if (Object.keys(cameras).length === 0) {
          cameraList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-video-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No cameras configured.</p>
            </div>
          `;
          return;
        }

        // Create a table for cameras
        const table = document.createElement('table');
        table.className = 'camera-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Camera Name</th>
              <th>RTSP URL</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${cameraName}</td>
            <td><span class="camera-url">${Array.isArray(rtspUrl) ? rtspUrl[0] : rtspUrl}</span></td>
            <td>
              <button class="btn btn-danger btn-sm" onclick="confirmDeleteCamera('${cameraName}')">
                <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
              </button>
            </td>
          `;
          tbody.appendChild(row);
        }

        cameraList.appendChild(table);
      } catch (error) {
        console.error('Error loading cameras:', error);
        cameraList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load cameras. Please check your connection.</p>
            <button class="btn btn-primary" onclick="loadCameras()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
    }

    // Function to confirm camera deletion
    function confirmDeleteCamera(cameraName) {
      showConfirmDialog(`Are you sure you want to delete the camera "${cameraName}"?`, null, () => {
        deleteCamera(cameraName);
      });
    }

    // Function to delete a camera
    async function deleteCamera(cameraName) {
      try {
        showToast('Deleting camera...', 'info');

        const response = await fetch(`/face_recognition/delete-camera/${encodeURIComponent(cameraName)}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (result.status === 'success') {
          showToast(result.message, 'success');
          loadCameras(); // Refresh the list
        } else {
          showToast('Error deleting camera: ' + result.message, 'error');
        }
      } catch (error) {
        console.error('Error deleting camera:', error);
        showToast('Failed to delete camera. Please check your connection.', 'error');
      }
    }

    // User Registration Functions
    function setupUserRegistration() {
      const registerForm = document.getElementById('registerForm');
      const chooseFileBtn = document.getElementById('choose-file-btn');
      const uploadInput = document.getElementById('upload-input');
      const fileStatus = document.getElementById('file-status');
      const modal = document.getElementById('modal');
      const uploadBtn = document.getElementById('upload-btn');
      const captureBtn = document.getElementById('capture-btn');
      const closeModalBtn = document.getElementById('close-modal-btn');
      const cameraContainer = document.getElementById('camera-container');
      const video = document.getElementById('video');
      const captureImageBtn = document.getElementById('capture-image-btn');
      const closeCameraBtn = document.getElementById('close-camera-btn');
      const canvas = document.getElementById('canvas');
      const imagePreview = document.getElementById('image-preview');
      const capturedImageInput = document.getElementById('captured_image');

      // Handle file selection button
      chooseFileBtn.addEventListener('click', () => {
        modal.style.display = 'block';
      });

      // Close modal when the close button is clicked
      closeModalBtn.addEventListener('click', () => {
        modal.style.display = 'none';
      });

      // Close modal when clicking outside the modal
      window.addEventListener('click', (event) => {
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      });

      // Upload from device
      uploadBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        uploadInput.click();
      });

      // Update file status when a file is selected
      uploadInput.addEventListener('change', () => {
        if (uploadInput.files.length > 0) {
          fileStatus.textContent = `Selected: ${uploadInput.files[0].name}`;
        } else {
          fileStatus.textContent = 'No file chosen';
        }
      });

      // Take Picture with Camera
      captureBtn.addEventListener('click', async () => {
        modal.style.display = 'none';

        console.log("Opening camera...");

        // Show camera container immediately without animation first
        cameraContainer.style.display = 'block';
        cameraContainer.style.opacity = '1';
        cameraContainer.style.visibility = 'visible';

        try {
          console.log("Requesting camera access...");
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280 },
              height: { ideal: 720 },
              facingMode: "user"
            }
          });
          console.log("Camera access granted");
          video.srcObject = stream;

          // Make sure video is visible
          video.style.display = 'block';

          // Log when video is ready
          video.onloadedmetadata = () => {
            console.log("Video metadata loaded, dimensions:", video.videoWidth, "x", video.videoHeight);
          };
        } catch (err) {
          // Create toast notification for error
          showToast('Could not access the camera. Please check permissions.', 'error');
          console.error("Camera error:", err);
          hideCamera();
        }
      });

      // Function to hide camera with animation
      function hideCamera() {
        console.log("Hiding camera...");
        // Directly set styles instead of using classes
        cameraContainer.style.opacity = '0';
        cameraContainer.style.visibility = 'hidden';

        // Wait for transition to complete before hiding
        setTimeout(() => {
          cameraContainer.style.display = 'none';
          console.log("Camera hidden");
        }, 300); // Match this with the transition duration
      }

      // Capture Image
      captureImageBtn.addEventListener('click', () => {
        try {
          const context = canvas.getContext('2d');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          context.drawImage(video, 0, 0, canvas.width, canvas.height);

          const dataURL = canvas.toDataURL('image/png');
          imagePreview.src = dataURL;
          imagePreview.style.display = 'block';

          // Convert the base64 string to a Blob
          const byteString = atob(dataURL.split(',')[1]);
          const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          const blob = new Blob([ab], { type: mimeString });
          const file = new File([blob], 'captured-image.png', { type: mimeString });

          // Now append the file to the form input
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(file);
          uploadInput.files = dataTransfer.files;

          // Update file status text
          fileStatus.textContent = "Image successfully captured";

          // Automatically set the base64 value in the hidden input for reference (if needed)
          capturedImageInput.value = dataURL;

          // Stop the video stream
          video.srcObject.getTracks().forEach(track => track.stop());

          // Hide camera with animation
          hideCamera();

          // Show success message
          showToast('Image captured successfully!', 'success');
        } catch (err) {
          console.error('Error capturing image:', err);
          showToast('Failed to capture image. Please try again.', 'error');
        }
      });

      // Close Camera
      closeCameraBtn.addEventListener('click', () => {
        // Stop all video tracks
        if (video.srcObject) {
          video.srcObject.getTracks().forEach(track => track.stop());
        }

        // Hide camera with animation
        hideCamera();
      });

      // Form submission with AJAX
      registerForm.addEventListener('submit', function(event) {
        event.preventDefault(); // Always prevent default form submission

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const hasFile = uploadInput.files.length > 0 || document.getElementById('captured_image').value;

        if (!username || !email || !hasFile) {
          let message = 'Please fill in all fields';
          if (!hasFile) {
            message += ' and provide a profile image';
          }
          showToast(message + '.', 'error');
          return;
        }

        // Show loading message
        showToast('Registering user...', 'info');

        // Create FormData object from the form
        const formData = new FormData(registerForm);

        // Submit the form via AJAX
        fetch('/face_recognition/register', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.message) {
            // Show success message
            showToast(data.message, 'success');

            // Clear the form
            registerForm.reset();
            fileStatus.textContent = 'No file chosen';

            // Refresh the user list if we're on that tab
            if (document.querySelector('.tab-btn[data-tab="user-management"]').classList.contains('active')) {
              loadUsers();
            }
          } else if (data.detail) {
            // Show error message
            showToast(data.detail, 'error');
          }
        })
        .catch(error => {
          console.error('Error registering user:', error);
          showToast('Failed to register user. Please try again.', 'error');
        });
      });
    }

    // User Management Functions
    function setupUserManagement() {
      const refreshUsersBtn = document.getElementById('refreshUsersBtn');
      const searchUsersBtn = document.getElementById('searchUsersBtn');
      const userSearchInput = document.getElementById('userSearchInput');

      // Load users initially
      loadUsers();

      // Refresh users button
      refreshUsersBtn.addEventListener('click', () => {
        loadUsers();
      });

      // Search users button
      searchUsersBtn.addEventListener('click', () => {
        const searchTerm = userSearchInput.value.trim();
        loadUsers(searchTerm);
      });

      // Search on Enter key
      userSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const searchTerm = userSearchInput.value.trim();
          loadUsers(searchTerm);
        }
      });
    }

    // Function to load and display users
    async function loadUsers(searchTerm = '') {
      const usersList = document.getElementById('usersList');
      usersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading users...</p>
        </div>
      `;

      try {
        // Construct the URL with search parameter if provided
        let url = '/face_recognition/users';
        if (searchTerm) {
          url += `?search=${encodeURIComponent(searchTerm)}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const users = await response.json();

        if (users.length === 0) {
          usersList.innerHTML = `
            <div class="empty-state" style="padding: 20px; text-align: center;">
              <i class="fas fa-users-slash" style="font-size: 40px; color: #ccc; animation: none; margin-bottom: 15px;"></i>
              <p>No users found${searchTerm ? ' matching your search' : ''}.</p>
            </div>
          `;
          return;
        }

        // Create a table for users
        const table = document.createElement('table');
        table.className = 'users-table';
        table.innerHTML = `
          <thead>
            <tr>
              <th>Image</th>
              <th>User Info</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody></tbody>
        `;

        const tbody = table.querySelector('tbody');

        for (const user of users) {
          const row = document.createElement('tr');

          // Use the original uploaded image from images folder
          // Encode the username to handle spaces and special characters
          const encodedUsername = encodeURIComponent(user.username);
          let userImage = `/images/${encodedUsername}.jpg`;

          // Store fallback images in a data attribute
          let fallbackImages = [];

          // First fallback: images from database
          if (user.images && user.images.length > 0) {
            user.images.forEach(img => {
              fallbackImages.push(img.image_url.replace('./', '/'));
            });
          }

          // Second fallback: cropped face image
          fallbackImages.push(`/cropped_faces/${encodedUsername}.jpg`);

          // Store fallbacks as JSON in a data attribute
          row.setAttribute('data-fallback-images', JSON.stringify(fallbackImages));

          // Create the row HTML
          row.innerHTML = `
            <td>
              <img src="${userImage}" alt="${user.username}" class="user-image" onclick="enlargeImage('${userImage}')">
            </td>
            <td>
              <strong>${user.username}</strong><br>
              <span class="user-email">${user.email}</span>
            </td>
            <td>
              <div class="user-actions">
                <button class="btn btn-primary btn-sm" onclick="viewUserDetails(${user.id})">
                  <i class="fas fa-eye" style="animation: none;"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="confirmDeleteUser(${user.id}, '${user.username}')">
                  <i class="fas fa-trash-alt" style="animation: none;"></i>
                </button>
              </div>
            </td>
          `;

          // Add error handling for the image with multiple fallbacks
          const imgElement = row.querySelector('img');
          imgElement.onerror = function() {
            try {
              // Get the fallback images array
              const fallbackImagesStr = row.getAttribute('data-fallback-images');
              if (fallbackImagesStr) {
                const fallbacks = JSON.parse(fallbackImagesStr);

                if (fallbacks.length > 0) {
                  // Try the next fallback image
                  const nextFallback = fallbacks.shift();
                  console.log(`Trying fallback image for ${user.username}: ${nextFallback}`);
                  this.src = nextFallback;

                  // Update the data attribute with remaining fallbacks
                  row.setAttribute('data-fallback-images', JSON.stringify(fallbacks));

                  // If this is the last fallback, set up final error handler
                  if (fallbacks.length === 0) {
                    this.onerror = function() {
                      console.error(`All image fallbacks failed for ${user.username}`);
                      this.style.display = 'none';
                    };
                  }
                  return;
                }
              }

              // If we get here, no fallbacks worked
              this.style.display = 'none';
            } catch (e) {
              console.error('Error in image fallback handler:', e);
              this.style.display = 'none';
            }
          };

          tbody.appendChild(row);
        }

        usersList.innerHTML = '';
        usersList.appendChild(table);
      } catch (error) {
        console.error('Error loading users:', error);
        usersList.innerHTML = `
          <div class="empty-state" style="padding: 20px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="font-size: 40px; color: var(--danger-color); animation: none; margin-bottom: 15px;"></i>
            <p>Failed to load users. Please try again.</p>
            <button class="btn btn-primary" onclick="loadUsers()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
      }
    }

    // Function to view user details
    function viewUserDetails(userId) {
      // Redirect to the user details page
      window.location.href = `/face_recognition/user/${userId}/page`;
      console.log(`Navigating to user details page: /face_recognition/user/${userId}/page`);
    }

    // Function to confirm user deletion
    function confirmDeleteUser(userId, username) {
      showConfirmDialog(`Are you sure you want to delete the user "${username}"?`, null, () => {
        deleteUser(userId);
      });
    }

    // Function to delete a user
    async function deleteUser(userId) {
      try {
        showToast('Deleting user...', 'info');

        const response = await fetch(`/face_recognition/users/${userId}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User deleted successfully', 'success');
          loadUsers(); // Refresh the list
        } else {
          showToast(result.detail || 'Failed to delete user', 'error');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        showToast('Failed to delete user. Please try again.', 'error');
      }
    }

    // Settings Management Functions
    function setupSettingsManagement() {
      const settingsForm = document.getElementById('settingsForm');
      const faceThreshold = document.getElementById('faceThreshold');
      const thresholdValue = document.getElementById('thresholdValue');
      const settingsStatus = document.getElementById('settingsStatus');

      // Update the displayed threshold value when the slider changes
      faceThreshold.addEventListener('input', () => {
        thresholdValue.textContent = faceThreshold.value;
      });

      // Load current settings when the tab is shown
      async function loadSettings() {
        try {
          const response = await fetch('/face_recognition/get-settings');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const settings = await response.json();

          // Update the slider with the current threshold value
          if (settings.face_threshold) {
            faceThreshold.value = settings.face_threshold;
            thresholdValue.textContent = settings.face_threshold;
          }
        } catch (error) {
          console.error('Error loading settings:', error);
          showToast('Failed to load settings. Please try again.', 'error');
        }
      }

      // Handle form submission to save settings
      settingsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const threshold = faceThreshold.value;

        try {
          showToast('Saving settings...', 'info');

          const response = await fetch('/face_recognition/update-settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ face_threshold: parseFloat(threshold) }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          // Show success message
          showToast(result.message || 'Settings saved successfully!', 'success');

          // Show the settings status message
          settingsStatus.style.display = 'block';

          // Hide the status message after 3 seconds
          setTimeout(() => {
            settingsStatus.style.display = 'none';
          }, 3000);
        } catch (error) {
          console.error('Error saving settings:', error);
          showToast('Failed to save settings. Please try again.', 'error');
        }
      });

      // Make loadSettings available to call when the tab is activated
      return { loadSettings };
    }

    // Clustering Functions
    let isClusteredView = false;

    function setupClusteringFunctions() {
      const clusterBtn = document.getElementById('clusterBtn');
      const viewModeBtn = document.getElementById('viewModeBtn');
      const viewModeText = document.getElementById('viewModeText');
      const minClusterSizeInput = document.getElementById('minClusterSize');

      // Cluster button click handler
      clusterBtn.addEventListener('click', async () => {
        try {
          showToast('Auto-clustering faces...', 'info');

          const minClusterSize = parseInt(minClusterSizeInput.value) || 2;

          const response = await fetch('/face_recognition/cluster-unknowns', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              min_cluster_size: minClusterSize
            })
          });

          const result = await response.json();

          if (result.success) {
            // Show success message
            showToast(result.message, 'success');

            // If images were removed, show additional information
            if (result.removed_images && result.removed_images > 0) {
              console.log(`${result.removed_images} images without faces were automatically removed`);
            }

            // Switch to clustered view
            isClusteredView = true;
            viewModeText.textContent = 'Switch to Individual View';
            loadClusteredUnknowns();
          } else {
            // Show error message
            if (result.removed_images && result.removed_images > 0) {
              showToast(`Clustering failed but ${result.removed_images} images without faces were removed. ${result.message}`, 'warning');
            } else {
              showToast(result.message || 'Clustering failed', 'error');
            }
          }
        } catch (error) {
          console.error('Error clustering faces:', error);
          showToast('Failed to cluster faces. Please try again.', 'error');
        }
      });

      // View mode button click handler
      viewModeBtn.addEventListener('click', () => {
        isClusteredView = !isClusteredView;

        if (isClusteredView) {
          viewModeText.textContent = 'Switch to Individual View';
          loadClusteredUnknowns();
        } else {
          viewModeText.textContent = 'Switch to Clustered View';
          loadUnknowns();
        }
      });
    }

    // Function to load clustered unknown faces
    async function loadClusteredUnknowns() {
      container.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading clustered faces...</p>
        </div>
      `;

      try {
        // First, get the cluster name mapping
        const clusterRes = await fetch('/face_recognition/get-available-clusters');
        const clusterData = await clusterRes.json();

        // Create a mapping from cluster IDs to display names
        const clusterNameMap = {};
        for (const cluster of clusterData.clusters) {
          clusterNameMap[cluster.id] = cluster.display_name || `Cluster ${cluster.id.split('_').pop()}`;
        }

        // Now get the clustered unknowns
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        // Clear container
        container.innerHTML = '';

        const clusters = data.clusters;
        const unclustered = data.unclustered;

        // Check if there are any clusters or unclustered faces
        if (Object.keys(clusters).length === 0 && unclustered.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-user-slash" style="animation: none;"></i>
              <h3>No Unknown Persons</h3>
              <p>There are currently no unknown persons to manage.</p>
              <button class="btn btn-primary" onclick="loadUnknowns()">
                <i class="fas fa-sync-alt" style="animation: none;"></i> Refresh
              </button>
            </div>
          `;
          return;
        }

        // Display clusters
        if (Object.keys(clusters).length > 0) {
          const clusterSection = document.createElement('div');
          clusterSection.className = 'cluster-section';

          const clusterHeader = document.createElement('h3');
          clusterHeader.className = 'section-title';
          clusterHeader.innerHTML = `<i class="fas fa-object-group" style="animation: none;"></i> Clustered Faces`;
          clusterSection.appendChild(clusterHeader);

          // Sort clusters by size (number of faces)
          const sortedClusters = Object.entries(clusters).sort((a, b) => b[1].length - a[1].length);

          for (const [clusterId, faces] of sortedClusters) {
            const clusterCard = document.createElement('div');
            clusterCard.className = 'cluster-card';
            clusterCard.setAttribute('data-cluster-id', clusterId);

            // Create image section
            const imageSection = faces.map(face =>
              face.image_paths.map(path => {
                // Sanitize path - replace backslashes with forward slashes
                const sanitizedPath = path.replace(/\\/g, '/');
                // Encode the path to prevent issues with special characters
                const encodedPath = sanitizedPath.replace(/'/g, "\\'");

                return `<div class="image-container">
                  <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                  <div class="image-actions">
                    <button class="move-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleMoveImageClick(this, event)">
                      <i class="fas fa-exchange-alt" style="animation: none;"></i>
                    </button>
                    <button class="delete-image-btn" data-path="${encodedPath}" data-id="${face.id}" onclick="handleDeleteImageClick(this, event)">
                      <i class="fas fa-trash-alt" style="animation: none;"></i>
                    </button>
                  </div>
                </div>`;
              }).join('')
            ).join('');

            clusterCard.innerHTML = `
              <div class="cluster-header">
                <div class="cluster-title">
                  <i class="fas fa-users" style="animation: none;"></i>
                  ${clusterNameMap[clusterId] || `Cluster Group`}
                  <span class="cluster-count">${faces.length} faces</span>
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteCluster('${clusterId}')">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete Cluster
                </button>
              </div>
              <div class="cluster-faces">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-cluster" class="assign-form" id="cluster-form-${clusterId}">
                  <input type="hidden" name="cluster_id" value="${clusterId}">
                  <div class="form-group">
                    <label><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" name="username" class="form-control" placeholder="Enter username" required>
                  </div>
                  <div class="form-group">
                    <label><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" name="email" class="form-control" placeholder="Enter email address" required>
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign All to User
                    </button>
                    <small class="form-text text-muted" style="margin-top: 8px;">
                      <i class="fas fa-info-circle"></i> This will assign all ${faces.length} faces in this cluster to the same user.
                    </small>
                  </div>
                </form>
              </div>
            `;

            clusterSection.appendChild(clusterCard);

            // Add form submission handler
            setTimeout(() => {
              const form = document.getElementById(`cluster-form-${clusterId}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignCluster(this, clusterId);
                });
              }
            }, 0);
          }

          container.appendChild(clusterSection);
        }

        // Display unclustered faces
        if (unclustered.length > 0) {
          const unclusteredSection = document.createElement('div');
          unclusteredSection.className = 'unclustered-section';

          const unclusteredHeader = document.createElement('h3');
          unclusteredHeader.className = 'section-title';
          unclusteredHeader.innerHTML = `<i class="fas fa-user-question" style="animation: none;"></i> Unclustered Faces <span class="cluster-count">${unclustered.length} faces</span>`;
          unclusteredSection.appendChild(unclusteredHeader);

          // Create cards for each unclustered face
          unclustered.forEach(person => {
            const card = document.createElement('div');
            card.className = 'card';
            card.setAttribute('data-id', person.id);

            const imageSection = person.image_paths.map(path => {
              // Sanitize path - replace backslashes with forward slashes
              const sanitizedPath = path.replace(/\\/g, '/');
              // Encode the path to prevent issues with special characters
              const encodedPath = sanitizedPath.replace(/'/g, "\\'");

              return `<div class="image-container">
                <img src="${sanitizedPath}" alt="Unknown Face" onclick="enlargeImage('${encodedPath}')">
                <div class="image-actions">
                  <button class="move-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleMoveImageClick(this, event)">
                    <i class="fas fa-exchange-alt" style="animation: none;"></i>
                  </button>
                  <button class="delete-image-btn" data-path="${encodedPath}" data-id="${person.id}" onclick="handleDeleteImageClick(this, event)">
                    <i class="fas fa-trash-alt" style="animation: none;"></i>
                  </button>
                </div>
              </div>`;
            }).join('');

            card.innerHTML = `
              <div class="card-header">
                <div class="card-title">
                  <i class="fas fa-user-question" style="animation: none;"></i> Unknown Person #${person.id}
                </div>
                <button class="btn btn-danger" onclick="confirmDeleteUnknown(${person.id})">
                  <i class="fas fa-trash-alt" style="animation: none;"></i> Delete
                </button>
              </div>
              <div class="card-images">${imageSection}</div>
              <div class="form-container">
                <form method="post" action="/face_recognition/assign-unknown" class="assign-form" id="form-${person.id}">
                  <input type="hidden" name="unknown_id" value="${person.id}">
                  <div class="form-group">
                    <label for="username-${person.id}"><i class="fas fa-user" style="animation: none;"></i> Username</label>
                    <input type="text" id="username-${person.id}" name="username" class="form-control" placeholder="Enter username" required>
                  </div>
                  <div class="form-group">
                    <label for="email-${person.id}"><i class="fas fa-envelope" style="animation: none;"></i> Email</label>
                    <input type="email" id="email-${person.id}" name="email" class="form-control" placeholder="Enter email address" required>
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-user-plus" style="animation: none;"></i> Assign to User
                    </button>
                  </div>
                </form>
              </div>
            `;

            unclusteredSection.appendChild(card);

            // Add form submission handler
            setTimeout(() => {
              const form = document.getElementById(`form-${person.id}`);
              if (form) {
                form.addEventListener('submit', function(e) {
                  e.preventDefault();
                  assignUnknown(this, person.id);
                });
              }
            }, 0);
          });

          container.appendChild(unclusteredSection);
        }
      } catch (err) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle" style="color: var(--danger-color); animation: none;"></i>
            <h3>Error Loading Data</h3>
            <p>Failed to load clustered faces. Please try again.</p>
            <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
              <i class="fas fa-sync-alt" style="animation: none;"></i> Try Again
            </button>
          </div>
        `;
        console.error(err);
      }
    }

    // Function to confirm deletion of a cluster
    function confirmDeleteCluster(clusterId) {
      showConfirmDialog('Are you sure you want to delete all faces in this cluster?', clusterId, deleteCluster);
    }

    // Function to delete a cluster
    async function deleteCluster(clusterId) {
      try {
        showToast('Deleting cluster...', 'info');

        // Get all unknown IDs in this cluster
        const response = await fetch('/face_recognition/get-clustered-unknowns');
        const data = await response.json();

        if (!data.clusters[clusterId]) {
          showToast('Cluster not found', 'error');
          return;
        }

        const unknownIds = data.clusters[clusterId].map(face => face.id);

        // Delete each unknown in the cluster
        let successCount = 0;

        for (const id of unknownIds) {
          try {
            const deleteResponse = await fetch(`/face_recognition/delete-unknown/${id}`, {
              method: 'DELETE'
            });

            if (deleteResponse.ok) {
              successCount++;
            }
          } catch (error) {
            console.error(`Error deleting unknown ID ${id}:`, error);
          }
        }

        if (successCount > 0) {
          showToast(`Successfully deleted ${successCount} of ${unknownIds.length} faces`, 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }
        } else {
          showToast('Failed to delete cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete cluster. Please try again.', 'error');
      }
    }

    // Function to assign a cluster to a user
    async function assignCluster(form, clusterId) {
      const formData = new FormData(form);

      try {
        showToast('Assigning cluster to user...', 'info');

        const response = await fetch('/face_recognition/assign-cluster', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Cluster assigned successfully!', 'success');

          // Remove the cluster card from UI
          document.querySelector(`[data-cluster-id="${clusterId}"]`).remove();

          // Check if there are any clusters left
          if (container.querySelector('.cluster-card') === null && container.querySelector('.card') === null) {
            container.innerHTML = `
              <div class="empty-state">
                <i class="fas fa-user-slash"></i>
                <h3>No Unknown Persons</h3>
                <p>There are currently no unknown persons to manage.</p>
                <button class="btn btn-primary" onclick="loadClusteredUnknowns()">
                  <i class="fas fa-sync-alt"></i> Refresh
                </button>
              </div>
            `;
          }

          // Refresh the user management tab to show the new user
          loadUsers();
        } else {
          showToast(result.detail || 'Failed to assign cluster', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to assign cluster. Please try again.', 'error');
      }
    }

    // Initialize the page
    loadUnknowns();
    setupTabs();
    setupCameraManagement();
    setupUserRegistration();
    setupUserManagement();
    setupClusteringFunctions();
    const settingsManager = setupSettingsManagement();

    // Function to confirm deletion of a single image
    function confirmDeleteImage(imagePath, unknownId, e) {
      console.log("confirmDeleteImage called with:", imagePath, unknownId);

      // Stop event propagation to prevent enlargeImage from being called
      if (e) {
        e.stopPropagation();
        e.preventDefault();
      } else if (window.event) {
        window.event.cancelBubble = true;
      }

      // Fix backslashes in the path (replace \ with /)
      const fixedPath = imagePath.replace(/\\/g, '/');
      console.log("Fixed path:", fixedPath);

      showConfirmDialog('Are you sure you want to delete this image?', { path: fixedPath, id: unknownId }, deleteImage);
    }

    // Function to delete a single image
    async function deleteImage(data) {
      try {
        console.log("deleteImage called with data:", data);
        showToast('Deleting image...', 'info');

        const url = `/face_recognition/delete-unknown-image?image_path=${encodeURIComponent(data.path)}`;
        console.log("Sending DELETE request to:", url);

        const response = await fetch(url, {
          method: 'DELETE'
        });

        console.log("Response status:", response.status);
        const result = await response.json();
        console.log("Response data:", result);

        if (response.ok) {
          showToast(result.message, 'success');

          // Find and remove the image container from the UI
          const imageContainers = document.querySelectorAll('.image-container');
          for (const container of imageContainers) {
            if (container.querySelector('img').src.includes(data.path)) {
              container.remove();
              break;
            }
          }

          // If the record was deleted (no images left), remove the entire card
          if (result.record_deleted) {
            // In individual view
            const card = document.querySelector(`[data-id="${data.id}"]`);
            if (card) {
              card.remove();

              // Check if there are any cards left
              if (container.children.length === 0) {
                container.innerHTML = `
                  <div class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <h3>No Unknown Persons</h3>
                    <p>There are currently no unknown persons to manage.</p>
                    <button class="btn btn-primary" onclick="refreshView()">
                      <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                  </div>
                `;
              }
            }

            // In clustered view
            if (isClusteredView) {
              // Refresh the clustered view to reflect the changes
              loadClusteredUnknowns();
            }
          }
        } else {
          showToast(result.detail || 'Failed to delete image', 'error');
        }
      } catch (error) {
        console.error('Error:', error);
        showToast('Failed to delete image. Please try again.', 'error');
      }
    }

    // Function to handle delete image button click
    function handleDeleteImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleDeleteImageClick called with path:", path, "id:", id);

      // Call confirmDeleteImage with the path and id
      confirmDeleteImage(path, id);
    }

    // Function to refresh the current view based on the view mode
    function refreshView() {
      if (isClusteredView) {
        loadClusteredUnknowns();
      } else {
        loadUnknowns();
      }
    }

    // Make functions available globally
    window.loadUnknowns = loadUnknowns;
    window.loadClusteredUnknowns = loadClusteredUnknowns;
    window.confirmDeleteUnknown = confirmDeleteUnknown;
    window.deleteUnknown = deleteUnknown;
    window.confirmDeleteCluster = confirmDeleteCluster;
    window.deleteCluster = deleteCluster;
    window.assignCluster = assignCluster;
    window.enlargeImage = enlargeImage;
    window.confirmDeleteImage = confirmDeleteImage;
    window.deleteImage = deleteImage;
    window.handleDeleteImageClick = handleDeleteImageClick;
    window.refreshView = refreshView;
    window.loadCameras = loadCameras;
    window.confirmDeleteCamera = confirmDeleteCamera;
    window.deleteCamera = deleteCamera;
    window.loadUsers = loadUsers;
    window.confirmDeleteUser = confirmDeleteUser;
    window.deleteUser = deleteUser;
    window.viewUserDetails = viewUserDetails;

    // Functions for moving images between clusters

    // Function to handle move image button click
    function handleMoveImageClick(button, event) {
      // Stop event propagation
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }

      // Get path and id from data attributes
      const path = button.getAttribute('data-path');
      const id = button.getAttribute('data-id');

      console.log("handleMoveImageClick called with path:", path, "id:", id);

      // Show the move image modal
      showMoveImageModal(id);
    }

    // Function to show the move image modal
    async function showMoveImageModal(unknownId) {
      // Get the modal and clusters list
      const modal = document.getElementById('moveImageModal');
      const clustersList = document.getElementById('clustersList');

      // Show loading state
      clustersList.innerHTML = `
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      `;

      // Show the modal
      modal.style.display = 'block';

      try {
        // Fetch available clusters
        const response = await fetch('/face_recognition/get-available-clusters');
        const data = await response.json();

        if (!response.ok) {
          throw new Error('Failed to fetch clusters');
        }

        // Get the current cluster for this unknown
        const unknownResponse = await fetch('/face_recognition/get-clustered-unknowns');
        const unknownData = await unknownResponse.json();

        let currentClusterId = null;

        // Find the current cluster for this unknown
        for (const clusterId in unknownData.clusters) {
          const unknowns = unknownData.clusters[clusterId];
          for (const unknown of unknowns) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = clusterId;
              break;
            }
          }
          if (currentClusterId) break;
        }

        // If not found in clusters, check unclustered
        if (!currentClusterId) {
          for (const unknown of unknownData.unclustered) {
            if (unknown.id === parseInt(unknownId)) {
              currentClusterId = 'unclustered';
              break;
            }
          }
        }

        // Build the clusters list
        let clustersHtml = '';

        // Add each cluster as a button
        for (const cluster of data.clusters) {
          // Skip the current cluster
          if (cluster.id === currentClusterId) continue;

          clustersHtml += `
            <button class="cluster-option" onclick="moveImageToCluster(${unknownId}, '${cluster.id}')">
              <i class="fas ${cluster.is_unclustered ? 'fa-users-slash' : 'fa-users'}"></i>
              ${cluster.display_name || cluster.name}
            </button>
          `;
        }

        if (clustersHtml === '') {
          clustersList.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-info-circle"></i>
              <p>No other clusters available to move to.</p>
            </div>
          `;
        } else {
          clustersList.innerHTML = clustersHtml;
        }
      } catch (error) {
        console.error('Error fetching clusters:', error);
        clustersList.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Error loading clusters. Please try again.</p>
          </div>
        `;
      }
    }

    // Function to move an image to a different cluster
    async function moveImageToCluster(unknownId, targetClusterId) {
      try {
        showToast('Moving image...', 'info');

        // Close the modal
        document.getElementById('moveImageModal').style.display = 'none';

        // Send the request to move the image
        const response = await fetch(`/face_recognition/move-to-cluster?unknown_id=${unknownId}&target_cluster_id=${encodeURIComponent(targetClusterId)}`, {
          method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message, 'success');

          // Refresh the view to reflect the changes
          refreshView();
        } else {
          showToast(result.detail || 'Failed to move image', 'error');
        }
      } catch (error) {
        console.error('Error moving image:', error);
        showToast('Failed to move image. Please try again.', 'error');
      }
    }

    // Add the move image functions to the global scope
    window.handleMoveImageClick = handleMoveImageClick;
    window.showMoveImageModal = showMoveImageModal;
    window.moveImageToCluster = moveImageToCluster;
  </script>

  <!-- Move Image Modal -->
  <div id="moveImageModal" class="modal">
    <div class="modal-content" style="max-width: 500px;">
      <span class="close" onclick="document.getElementById('moveImageModal').style.display='none'">&times;</span>
      <h2>Move Image to Another Cluster</h2>
      <p>Select the target cluster to move this image to:</p>
      <div id="clustersList" class="clusters-list">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i> Loading clusters...
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" onclick="document.getElementById('moveImageModal').style.display='none'">Cancel</button>
      </div>
    </div>
  </div>
</body>
</html>
