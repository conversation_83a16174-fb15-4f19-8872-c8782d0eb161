FROM nvidia/cuda:12.4.1-runtime-ubuntu22.04

# Prevent prompts during build
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies, including the deadsnakes PPA for Python 3.10
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
    python3.10 \
    python3.10-venv \
    python3.10-dev \
    python3-pip \
    build-essential \
    cmake \
    pkg-config \
    libx11-dev \
    libatlas-base-dev \
    libgtk-3-dev \
    libboost-python-dev \
    libboost-all-dev \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libfontconfig1 \
    libxrender1 \
    libxtst6 \
    libxi6 \
    libxrandr2 \
    libasound2 \
    curl \
    wget \
    bzip2 \
    unzip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set Python aliases
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1

# Set working directory
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Upgrade pip and install Python dependencies in a virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

RUN pip install --upgrade pip setuptools wheel

# Install dlib dependencies first (they take longer to compile)
RUN pip install cmake
RUN pip install dlib==19.24.2

# Install other requirements
RUN pip install -r requirements.txt

# Copy all project files
COPY . .

# Create models directory if it doesn't exist
RUN mkdir -p /app/app/models

# Download dlib face landmarks model if it doesn't exist
RUN if [ ! -f /app/app/models/shape_predictor_68_face_landmarks.dat ]; then \
    echo "Downloading dlib face landmarks model..." && \
    curl -L "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2" \
    -o /app/app/models/shape_predictor_68_face_landmarks.dat.bz2 && \
    bzip2 -d /app/app/models/shape_predictor_68_face_landmarks.dat.bz2; \
    else \
    echo "Face landmarks model already exists"; \
    fi

# Verify the model file exists
RUN ls -la /app/app/models/shape_predictor_68_face_landmarks.dat || echo "Warning: Face landmarks model not found"

# Set environment variables for face recognition
ENV FACE_RECOGNITION_MODELS_PATH=/app/app/models
ENV DLIB_MODEL_PATH=/app/app/models/shape_predictor_68_face_landmarks.dat

# Expose FastAPI port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run startup script which includes all checks and starts the app
CMD ["python", "startup.py"]