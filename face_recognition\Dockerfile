# Use CUDA-enabled image compatible with PyTorch
FROM nvidia/cuda:12.2.0-cudnn8-devel-ubuntu22.04

ENV DEBIAN_FRONTEND=noninteractive

# Install Python and system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3.10 python3.10-venv python3-pip \
    build-essential cmake \
    libgl1 libglib2.0-0 libsm6 libxext6 libxrender-dev curl git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set Python 3.10 as default
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.10 1 && \
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1

# Set workdir
WORKDIR /app

# Copy app files
COPY . .

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install packages
RUN pip install --upgrade pip setuptools wheel

# Install PyTorch with CUDA 12.2 support
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu122

# Install rest of the dependencies
RUN pip install -r requirements.txt

# Optional: Verify GPU access (for debug only)
# RUN python -c "import torch; print('CUDA available:', torch.cuda.is_available())"

# Expose FastAPI port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
