fastapi[standard]

# Computer Vision and Face Recognition
opencv-python==*********
dlib==19.24.2
face-recognition==1.3.0
ultralytics==8.3.133
facenet-pytorch
imutils==0.5.4

# Deep Learning and ML
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0
torch>=1.9.0
torchvision>=0.10.0

# Database and API
pydantic==2.11.4
PyMySQL==1.1.1
SQLAlchemy==2.0.40
alembic==1.15.2

# Authentication and Security
python-jose==3.4.0
passlib==1.7.4
cryptography==44.0.3
bcrypt==4.3.0

# Configuration and Environment
python-dotenv==1.0.1
pydantic-settings==2.9.1

# Object Tracking and Detection
# deep-sort-realtime==1.3.2

# Vector Database
qdrant-client>=1.8.0

# Model Hub
huggingface-hub==0.31.2

# Optional dependencies (uncomment if needed)
# deepface==0.0.93
# tf-keras==2.19.0
# keras-facenet==0.3.2
# hdbscan