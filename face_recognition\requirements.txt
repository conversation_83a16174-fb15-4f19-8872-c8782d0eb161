fastapi[standard]

opencv-python==*********
ultralytics==8.3.133

pydantic==2.11.4
PyMySQL==1.1.1
# deepface==0.0.93
# tf-keras==2.19.0
# keras-facenet==0.3.2
deep-sort-realtime==1.3.2
python-jose==3.4.0
passlib==1.7.4
alembic==1.15.2
SQLAlchemy==2.0.40
python-dotenv==1.0.1
pydantic-settings==2.9.1
huggingface-hub==0.31.2
cryptography==44.0.3
bcrypt==4.3.0
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126
# hdbscan
qdrant-client>=1.8.0
facenet-pytorch