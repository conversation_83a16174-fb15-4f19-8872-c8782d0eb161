import asyncio
import httpx
from models import WebhookPayload
from sqlalchemy import Table, MetaData, select
from sqlalchemy.ext.asyncio import AsyncSession

metadata = MetaData()

async def get_camera_permission_table(engine):
    async with engine.begin() as conn:
        return await conn.run_sync(
            lambda sync_conn: Table('camera_permissions', metadata, autoload_with=sync_conn)
        )

async def get_all_active_webhook_urls(session: AsyncSession, engine):
    async with engine.begin() as conn:
        webhooks_table = await conn.run_sync(
            lambda sync_conn: Table('webhooks', metadata, autoload_with=sync_conn)
        )

    result = await session.execute(
        select(webhooks_table.c.url).where(webhooks_table.c.is_active == True)
    )

    return [row[0] for row in result.all()]

async def send_to_webhook(url: str, payload: dict):
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload, timeout=5)
            print(f"Webhook to {url} responded with status {response.status_code}")
            return {"url": url, "status": response.status_code}
    except Exception as e:
        print(f"Failed to send to {url}: {e}")
        return {"url": url, "error": str(e)}

async def process_webhook_data(payload: WebhookPayload, session: AsyncSession, engine):
    camera_permissions = await get_camera_permission_table(engine)

    result = await session.execute(
        select(camera_permissions).where(
            camera_permissions.c.user_id == payload.user_id,
            camera_permissions.c.camera_id == payload.camera_id
        )
    )

    permission = result.first()
    webhook_urls = await get_all_active_webhook_urls(session, engine)

    if not permission:
        trigger_payload = {
            "event": "unauthorized_access",
            "user_id": payload.user_id,
            "camera_id": payload.camera_id,
        }

        tasks = [send_to_webhook(url, trigger_payload) for url in webhook_urls]
        webhook_results = await asyncio.gather(*tasks)

        return {
            "status": "error",
            "message": "User is NOT permitted",
            "webhook_results": webhook_results
        }
    else:
        return {
            "status": "success",
            "message": "User is permitted"
        }
