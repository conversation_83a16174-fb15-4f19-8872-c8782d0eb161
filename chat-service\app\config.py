from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    # Database settings
    DB_USER: str = "root" 
    DB_PASSWORD: str = "Navaneeth@2002"  
    DB_HOST: str = "db"
    DB_NAME: str = "surveillance_system"
    DB_PORT: int = 3307
    
    # Environment variables from docker-compose (same as above but with aliases)
    MYSQL_HOST: str = "db"
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = "Navaneeth@2002"
    MYSQL_DB: str = "surveillance_system"
    
    # OpenAI API key
    OPEN_API_KEY: str = "***********************************************************************************************"

    model_config = SettingsConfigDict(
        env_file=".env",
        extra="ignore"  # Use ignore instead of allow
    )

settings = Settings()
