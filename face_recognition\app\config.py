import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """
    Face Recognition Application Settings

    All settings can be overridden using environment variables.
    Environment variable names should be prefixed with 'FR_' (Face Recognition).

    Example:
        FR_DB_HOST=localhost
        FR_DB_PORT=3306
        FR_DEBUG=true
    """

    # Application Settings
    APP_NAME: str = Field(default="Face Recognition System", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    DEBUG: bool = Field(default=False, description="Enable debug mode")
    TESTING: bool = Field(default=False, description="Enable testing mode")

    # Server Settings
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    RELOAD: bool = Field(default=False, description="Enable auto-reload in development")

    # Database Settings (matching Docker service configuration)
    DB_USER: str = Field(default="root", description="Database username")
    DB_PASSWORD: str = Field(default="root", description="Database password (matches Docker MySQL)")
    DB_HOST: str = Field(default="mysql", description="Database host (Docker service name)")
    DB_NAME: str = Field(default="face_recognition_db", description="Face recognition database name")
    DB_PORT: int = Field(default=3306, description="Database port (internal Docker port)")
    DB_DRIVER: str = Field(default="mysql+pymysql", description="Database driver")
    DB_ECHO: bool = Field(default=False, description="Enable SQL query logging")

    # Authentication Database Settings (surveillance_system database)
    AUTH_DB_USER: str = Field(default="root", description="Authentication database username")
    AUTH_DB_PASSWORD: str = Field(default="root", description="Authentication database password")
    AUTH_DB_HOST: str = Field(default="mysql", description="Authentication database host")
    AUTH_DB_NAME: str = Field(default="surveillance_system", description="Main surveillance system database")
    AUTH_DB_PORT: int = Field(default=3306, description="Authentication database port")

    # Face Recognition Settings
    FACE_THRESHOLD: float = Field(default=0.7, ge=0.0, le=1.0, description="Face recognition similarity threshold")
    FACE_MODEL_PATH: Optional[str] = Field(default=None, description="Path to face detection model")
    DLIB_MODEL_PATH: str = Field(default="app/models/shape_predictor_68_face_landmarks.dat", description="Path to dlib face landmarks model")
    FACE_ENCODING_MODEL: str = Field(default="facenet", description="Face encoding model to use")
    MAX_FACE_SIZE: int = Field(default=500, description="Maximum face image size in pixels")
    MIN_FACE_SIZE: int = Field(default=50, description="Minimum face image size in pixels")

    # Image Processing Settings
    IMAGE_UPLOAD_MAX_SIZE: int = Field(default=5 * 1024 * 1024, description="Maximum image upload size in bytes (5MB)")
    ALLOWED_IMAGE_EXTENSIONS: List[str] = Field(default=[".jpg", ".jpeg", ".png", ".bmp"], description="Allowed image file extensions")
    IMAGE_QUALITY: int = Field(default=95, ge=1, le=100, description="Image compression quality")

    # Storage Settings
    STATIC_DIR: str = Field(default="static", description="Static files directory")
    IMAGES_DIR: str = Field(default="static/images", description="Images storage directory")
    CROPPED_FACES_DIR: str = Field(default="cropped_faces", description="Cropped faces storage directory")
    DATASET_DIR: str = Field(default="Dataset", description="Dataset storage directory")
    UNKNOWN_DIR: str = Field(default="Dataset/unknown", description="Unknown faces storage directory")

    # Camera Settings
    DEFAULT_CAMERA_TIMEOUT: int = Field(default=30, description="Default camera connection timeout in seconds")
    CAMERA_RETRY_ATTEMPTS: int = Field(default=3, description="Number of camera connection retry attempts")
    FRAME_RATE: int = Field(default=30, description="Camera frame rate")

    # Qdrant Vector Database Settings (matching Docker service configuration)
    QDRANT_HOST: str = Field(default="qdrant", description="Qdrant server host (Docker service name)")
    QDRANT_PORT: int = Field(default=6333, description="Qdrant server port")
    QDRANT_COLLECTION_NAME: str = Field(default="face_embeddings", description="Qdrant collection name")
    QDRANT_VECTOR_SIZE: int = Field(default=512, description="Face embedding vector size")
    QDRANT_ENABLED: bool = Field(default=True, description="Enable Qdrant vector database")

    # Security Settings
    SECRET_KEY: str = Field(default="your-secret-key-change-in-production", description="Secret key for security")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Access token expiration time in minutes")
    CORS_ORIGINS: List[str] = Field(default=["*"], description="CORS allowed origins")

    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FILE: Optional[str] = Field(default=None, description="Log file path")
    LOG_FORMAT: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="Log format")

    # Performance Settings
    MAX_WORKERS: int = Field(default=4, description="Maximum number of worker threads")
    BATCH_SIZE: int = Field(default=32, description="Batch size for processing")
    CACHE_TTL: int = Field(default=3600, description="Cache time-to-live in seconds")

    # Attendance Settings
    ATTENDANCE_TIME_WINDOW: int = Field(default=60, description="Attendance logging time window in seconds")
    DUPLICATE_ATTENDANCE_PREVENTION: bool = Field(default=True, description="Prevent duplicate attendance entries")

    # Model Settings
    DEVICE: str = Field(default="auto", description="Device to use for inference (auto, cpu, cuda)")
    MODEL_CACHE_DIR: str = Field(default="models", description="Model cache directory")
    DOWNLOAD_MODELS: bool = Field(default=True, description="Automatically download required models")

    @property
    def database_url(self) -> str:
        """Generate face recognition database URL from individual components."""
        return f"{self.DB_DRIVER}://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

    @property
    def auth_database_url(self) -> str:
        """Generate authentication/surveillance system database URL."""
        return f"{self.DB_DRIVER}://{self.AUTH_DB_USER}:{self.AUTH_DB_PASSWORD}@{self.AUTH_DB_HOST}:{self.AUTH_DB_PORT}/{self.AUTH_DB_NAME}"

    @property
    def qdrant_url(self) -> str:
        """Generate Qdrant URL."""
        return f"http://{self.QDRANT_HOST}:{self.QDRANT_PORT}"

    def get_image_upload_path(self, filename: str) -> str:
        """Get full path for image upload."""
        return os.path.join(self.IMAGES_DIR, filename)

    def get_cropped_face_path(self, filename: str) -> str:
        """Get full path for cropped face."""
        return os.path.join(self.CROPPED_FACES_DIR, filename)

    def get_unknown_face_path(self, filename: str) -> str:
        """Get full path for unknown face."""
        return os.path.join(self.UNKNOWN_DIR, filename)

    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        directories = [
            self.STATIC_DIR,
            self.IMAGES_DIR,
            self.CROPPED_FACES_DIR,
            self.DATASET_DIR,
            self.UNKNOWN_DIR,
            self.MODEL_CACHE_DIR
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    class Config:
        env_file = ".env"
        env_prefix = "FR_"  # All environment variables should start with FR_
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


# Create global settings instance
settings = Settings()