* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #1abc9c;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --success-color: #2ecc71;
  --text-light: #ecf0f1;
  --text-dark: #2c3e50;
  --bg-light: #f5f7fa;
  --bg-dark: #34495e;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--bg-light);
  color: var(--text-dark);
}

.container {
  display: grid;
  grid-template-columns: 280px 2fr 1fr;
  grid-template-rows: auto;
  gap: 15px;
  width: 100vw;
  height: 100vh;
  max-width: 100%;
  max-height: 100%;
  background: #fff;
  box-shadow: var(--shadow);
  box-sizing: border-box;
  overflow: hidden;
}

.sidebar {
  background-color: var(--primary-color);
  color: var(--text-light);
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 280px;
  overflow: hidden;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.logo {
  margin-top: 20px;
  text-align: center;
  width: 100%;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo img {
  width: 100%;
  max-width: 140px;
  height: auto;
  transition: var(--transition);
}

.logo img:hover {
  transform: scale(1.05);
}

.buttons {
  width: 100%;
  margin-top: 20px;
}

.btn-group {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 15px;
}

.btn-group-title {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10px;
  padding-left: 10px;
}

.menu-btn, .setting-btn, .homepage-btn {
  margin: 8px 0;
  padding: 12px 15px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-radius: 6px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  font-weight: 500;
}

.menu-btn i, .setting-btn i, .homepage-btn i {
  margin-right: 10px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.menu-btn:hover, .setting-btn:hover, .homepage-btn:hover {
  background-color: var(--secondary-color);
  transform: translateX(5px);
}

.menu-btn.active, .setting-btn.active {
  background-color: var(--secondary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.main {
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-height: 100vh;
  overflow-y: auto;
  background-color: var(--bg-light);
  border-radius: 8px;
  box-shadow: var(--shadow);
  margin: 15px 0;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  margin-bottom: 20px;
}

.app-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.active {
  background-color: var(--success-color);
  box-shadow: 0 0 8px var(--success-color);
  animation: pulse 2s infinite;
}

.status-dot.inactive {
  background-color: var(--danger-color);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(46, 204, 113, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
  }
}

.webcam-section {
  text-align: center;
  padding: 20px;
  background-color: white;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.webcam-section h2 {
  font-size: 20px;
  color: var(--primary-color);
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.webcam-section h2:after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: var(--secondary-color);
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
}

.camera-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.camera-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.camera-container h3 {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  padding: 15px;
  background-color: var(--primary-color);
  color: white;
  margin: 0;
}

.camera-container img {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 0 0 8px 8px;
  display: block;
}

.live-observations {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--shadow);
  height: calc(100vh - 30px);
  overflow-y: auto;
  margin: 15px 0;
}

.live-observations h2 {
  margin-bottom: 20px;
  text-align: center;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.live-observations h2:after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: var(--secondary-color);
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}

.live-observations ul {
  list-style: none;
  margin-top: 20px;
}

.live-observations li {
  margin-bottom: 15px;
  padding: 15px;
  background: var(--bg-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  border-left: 4px solid var(--secondary-color);
}

.live-observations li:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.camera-feed {
  width: 400px;
  max-width: 80%;
  height: auto;
  max-height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.camera-feed img {
  object-fit: contain;
  height: 100%;
  width: 100%;
  transition: var(--transition);
}

/* Modal Styling */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 30px;
  border: none;
  width: 500px;
  max-width: 90%;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content h2 {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 25px;
  text-align: center;
  position: relative;
}

.modal-content h2:after {
  content: '';
  position: absolute;
  width: 50px;
  height: 3px;
  background-color: var(--secondary-color);
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.close-btn {
  color: var(--text-dark);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  top: 15px;
  right: 20px;
  transition: var(--transition);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  color: var(--danger-color);
  background-color: rgba(231, 76, 60, 0.1);
  transform: rotate(90deg);
}

.camera-list {
  margin-top: 25px;
  padding: 15px;
  background-color: var(--bg-light);
  border-radius: 8px;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
}

.camera-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.camera-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.camera-item:last-child {
  border-bottom: none;
}

.camera-item p {
  margin: 0;
  flex-grow: 1;
}

.camera-item button {
  background-color: var(--danger-color);
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.camera-item button i {
  margin-right: 5px;
}

.camera-item button:hover {
  background-color: #c0392b;
  transform: scale(1.05);
}

#cameraForm {
  display: flex;
  flex-direction: column;
}

#cameraForm label {
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--text-dark);
}

#cameraForm input {
  width: 100%;
  padding: 12px 15px;
  margin: 8px 0 20px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 16px;
  transition: var(--transition);
  background-color: var(--bg-light);
}

#cameraForm input:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

#cameraForm button {
  padding: 12px 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

#cameraForm button i {
  margin-right: 8px;
}

#cameraForm button[type="submit"] {
  background-color: var(--secondary-color);
}

#cameraForm button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#cameraForm button[type="submit"]:hover {
  background-color: #2980b9;
}

#cameraForm button[type="button"] {
  background-color: var(--primary-color);
}

#cameraForm button[type="button"]:hover {
  background-color: #1e2b38;
}

#cameraList {
  margin-top: 25px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--bg-light);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
  max-height: 300px;
  overflow-y: auto;
}

/* Confirmation Dialog */
.confirm-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1100;
  width: 400px;
  max-width: 90%;
  text-align: center;
  display: none;
}

.confirm-dialog h3 {
  margin-bottom: 15px;
  color: var(--primary-color);
}

.confirm-dialog p {
  margin-bottom: 20px;
  color: var(--text-dark);
}

.confirm-dialog-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.confirm-dialog-buttons button {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.confirm-dialog-buttons .confirm-yes {
  background-color: var(--danger-color);
  color: white;
}

.confirm-dialog-buttons .confirm-no {
  background-color: var(--bg-light);
  color: var(--text-dark);
}

.confirm-dialog-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Toast Notification Styling */
.toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-color);
  color: white;
  padding: 12px 25px;
  border-radius: 8px;
  z-index: 9999; /* Increased z-index to ensure it's above everything */
  font-weight: 500;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
  pointer-events: none;
  width: auto;
  min-width: 250px;
  max-width: 80%;
  text-align: center;
  margin-left: 0; /* Reset any margin */
}

.toast.show {
  opacity: 1;
  transform: translateX(-50%) translateY(-10px); /* Move up slightly when shown */
}

.toast.success {
  background-color: var(--success-color);
}

.toast.error {
  background-color: var(--danger-color);
}

.toast.info {
  background-color: var(--secondary-color);
}

/* Main Content Area */
.main-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* No Cameras Fullscreen Styling */
.no-cameras-fullscreen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 40px 20px;
}

.no-cameras-fullscreen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f3f4' fill-opacity='0.2'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.4;
  z-index: 1;
}

.no-cameras-fullscreen .no-cameras-content {
  text-align: center;
  padding: 80px 60px;
  color: var(--text-dark);
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  max-width: 600px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.no-cameras-fullscreen .no-cameras-content i {
  font-size: 120px;
  color: #6c757d;
  margin-bottom: 32px;
  display: block;
  opacity: 0.7;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.no-cameras-fullscreen .no-cameras-content h2 {
  font-size: 36px;
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 700;
  letter-spacing: -0.8px;
}

.no-cameras-fullscreen .no-cameras-content p {
  font-size: 18px;
  color: #6c757d;
  margin-bottom: 40px;
  line-height: 1.7;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
}

.no-cameras-fullscreen .admin-contact-message {
  margin-top: 24px !important;
  font-size: 16px !important;
  color: #95a5a6 !important;
  font-style: italic;
}

.no-cameras-fullscreen .no-cameras-content .btn-primary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
  color: white;
  padding: 16px 32px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 200px;
  justify-content: center;
}

.no-cameras-fullscreen .no-cameras-content .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.no-cameras-fullscreen .no-cameras-content .btn-primary:hover::before {
  left: 100%;
}

.no-cameras-fullscreen .no-cameras-content .btn-primary:hover {
  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(52, 152, 219, 0.4);
}

.no-cameras-fullscreen .no-cameras-content .btn-primary:active {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.no-cameras-fullscreen .no-cameras-content .btn-primary i {
  font-size: 20px;
  margin: 0;
}

/* Status badge for fullscreen version */
.no-cameras-fullscreen .status-badge {
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(231, 76, 60, 0.15);
  color: var(--danger-color);
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(231, 76, 60, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.no-cameras-fullscreen .status-badge i {
  font-size: 12px;
}

/* Responsive improvements for fullscreen no-cameras */
@media (max-width: 768px) {
  .no-cameras-fullscreen {
    min-height: calc(100vh - 150px);
    padding: 30px 15px;
  }

  .no-cameras-fullscreen .no-cameras-content {
    padding: 60px 30px;
    max-width: 100%;
    margin: 0 10px;
  }

  .no-cameras-fullscreen .no-cameras-content i {
    font-size: 90px;
    margin-bottom: 28px;
  }

  .no-cameras-fullscreen .no-cameras-content h2 {
    font-size: 28px;
    margin-bottom: 18px;
  }

  .no-cameras-fullscreen .no-cameras-content p {
    font-size: 16px;
    margin-bottom: 36px;
    max-width: 100%;
  }

  .no-cameras-fullscreen .no-cameras-content .btn-primary {
    padding: 14px 28px;
    font-size: 16px;
    width: 100%;
    max-width: 300px;
  }

  .no-cameras-fullscreen .status-badge {
    top: 20px;
    right: 20px;
    font-size: 12px;
    padding: 6px 12px;
  }

  .video-grid {
    margin-top: 25px;
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .no-cameras-fullscreen {
    min-height: calc(100vh - 120px);
    padding: 20px 10px;
  }

  .no-cameras-fullscreen .no-cameras-content {
    padding: 40px 20px;
    margin: 0 5px;
  }

  .no-cameras-fullscreen .no-cameras-content i {
    font-size: 80px;
    margin-bottom: 24px;
  }

  .no-cameras-fullscreen .no-cameras-content h2 {
    font-size: 24px;
    margin-bottom: 16px;
  }

  .no-cameras-fullscreen .no-cameras-content p {
    font-size: 15px;
    margin-bottom: 32px;
  }

  .no-cameras-fullscreen .no-cameras-content .btn-primary {
    padding: 12px 24px;
    font-size: 15px;
    width: 100%;
    max-width: 280px;
  }

  .no-cameras-fullscreen .status-badge {
    top: 15px;
    right: 15px;
    font-size: 11px;
    padding: 5px 10px;
  }

  .video-grid {
    margin-top: 20px;
    margin-bottom: 10px;
  }
}