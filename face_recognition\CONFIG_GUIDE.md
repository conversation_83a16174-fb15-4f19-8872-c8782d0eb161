# Face Recognition System Configuration Guide

## Overview

The Face Recognition System uses environment variables for configuration, making it easy to deploy across different environments (development, staging, production) without changing code.

## Environment Variable Prefix

All environment variables for this application use the prefix `FR_` (Face Recognition). This prevents conflicts with other applications and makes it clear which variables belong to this system.

## Configuration Methods

### 1. Environment Variables (Recommended)
Set environment variables directly in your system:

```bash
export FR_DB_HOST=localhost
export FR_DB_PORT=3306
export FR_DEBUG=true
```

### 2. .env File
Create a `.env` file in the `face_recognition/` directory:

```bash
# Copy the example file
cp .env.example .env

# Edit the .env file with your values
nano .env
```

### 3. Docker Environment
When using Docker, pass environment variables:

```bash
docker run -e FR_DB_HOST=db -e FR_DEBUG=false your-app
```

Or use a docker-compose.yml file:

```yaml
version: '3.8'
services:
  face-recognition:
    image: your-app
    environment:
      - FR_DB_HOST=db
      - FR_DEBUG=false
      - FR_QDRANT_HOST=qdrant
```

## Configuration Categories

### Application Settings
- `FR_APP_NAME`: Application name (default: "Face Recognition System")
- `FR_APP_VERSION`: Application version (default: "1.0.0")
- `FR_DEBUG`: Enable debug mode (default: false)
- `FR_TESTING`: Enable testing mode (default: false)

### Server Settings
- `FR_HOST`: Server host (default: "0.0.0.0")
- `FR_PORT`: Server port (default: 8000)
- `FR_RELOAD`: Enable auto-reload in development (default: false)

### Database Settings
- `FR_DB_USER`: Database username (default: "root")
- `FR_DB_PASSWORD`: Database password (default: "Vis%40123hnu")
- `FR_DB_HOST`: Database host (default: "localhost")
- `FR_DB_NAME`: Database name (default: "face_recognition_db")
- `FR_DB_PORT`: Database port (default: 3306)
- `FR_DB_DRIVER`: Database driver (default: "mysql+pymysql")
- `FR_DB_ECHO`: Enable SQL query logging (default: false)

### Face Recognition Settings
- `FR_FACE_THRESHOLD`: Similarity threshold (0.0-1.0, default: 0.7)
- `FR_FACE_MODEL_PATH`: Path to face detection model
- `FR_FACE_ENCODING_MODEL`: Face encoding model (default: "facenet")
- `FR_MAX_FACE_SIZE`: Maximum face size in pixels (default: 500)
- `FR_MIN_FACE_SIZE`: Minimum face size in pixels (default: 50)

### Image Processing Settings
- `FR_IMAGE_UPLOAD_MAX_SIZE`: Max upload size in bytes (default: 5MB)
- `FR_ALLOWED_IMAGE_EXTENSIONS`: Allowed file extensions
- `FR_IMAGE_QUALITY`: Image compression quality (1-100, default: 95)

### Storage Settings
- `FR_STATIC_DIR`: Static files directory (default: "static")
- `FR_IMAGES_DIR`: Images storage directory (default: "static/images")
- `FR_CROPPED_FACES_DIR`: Cropped faces directory (default: "cropped_faces")
- `FR_DATASET_DIR`: Dataset directory (default: "Dataset")
- `FR_UNKNOWN_DIR`: Unknown faces directory (default: "Dataset/unknown")

### Camera Settings
- `FR_DEFAULT_CAMERA_TIMEOUT`: Connection timeout in seconds (default: 30)
- `FR_CAMERA_RETRY_ATTEMPTS`: Retry attempts (default: 3)
- `FR_FRAME_RATE`: Camera frame rate (default: 30)

### Qdrant Vector Database Settings
- `FR_QDRANT_HOST`: Qdrant server host (default: "localhost")
- `FR_QDRANT_PORT`: Qdrant server port (default: 6333)
- `FR_QDRANT_COLLECTION_NAME`: Collection name (default: "face_embeddings")
- `FR_QDRANT_VECTOR_SIZE`: Vector size (default: 512)
- `FR_QDRANT_ENABLED`: Enable Qdrant (default: true)

### Security Settings
- `FR_SECRET_KEY`: Secret key for security (change in production!)
- `FR_ACCESS_TOKEN_EXPIRE_MINUTES`: Token expiration (default: 30)
- `FR_CORS_ORIGINS`: CORS allowed origins (default: ["*"])

### Logging Settings
- `FR_LOG_LEVEL`: Logging level (default: "INFO")
- `FR_LOG_FILE`: Log file path (optional)
- `FR_LOG_FORMAT`: Log message format

### Performance Settings
- `FR_MAX_WORKERS`: Maximum worker threads (default: 4)
- `FR_BATCH_SIZE`: Processing batch size (default: 32)
- `FR_CACHE_TTL`: Cache time-to-live in seconds (default: 3600)

### Attendance Settings
- `FR_ATTENDANCE_TIME_WINDOW`: Time window in seconds (default: 60)
- `FR_DUPLICATE_ATTENDANCE_PREVENTION`: Prevent duplicates (default: true)

### Model Settings
- `FR_DEVICE`: Device for inference (auto/cpu/cuda, default: "auto")
- `FR_MODEL_CACHE_DIR`: Model cache directory (default: "models")
- `FR_DOWNLOAD_MODELS`: Auto-download models (default: true)

## Environment-Specific Configurations

### Development Environment
```bash
FR_DEBUG=true
FR_RELOAD=true
FR_LOG_LEVEL=DEBUG
FR_DB_ECHO=true
```

### Production Environment
```bash
FR_DEBUG=false
FR_RELOAD=false
FR_LOG_LEVEL=WARNING
FR_SECRET_KEY=your-super-secure-production-key
FR_DB_HOST=production-db-server
FR_DB_PASSWORD=secure-production-password
```

### Docker Environment
```bash
FR_DB_HOST=db
FR_QDRANT_HOST=qdrant
FR_HOST=0.0.0.0
```

### High Performance Environment
```bash
FR_MAX_WORKERS=8
FR_BATCH_SIZE=64
FR_DEVICE=cuda
FR_CACHE_TTL=7200
```

## Usage in Code

Access configuration values through the settings object:

```python
from app.config import settings

# Database URL
db_url = settings.database_url

# Face recognition threshold
threshold = settings.FACE_THRESHOLD

# Check if debug mode is enabled
if settings.DEBUG:
    print("Debug mode is enabled")

# Ensure directories exist
settings.ensure_directories()
```

## Validation

The configuration system includes validation:
- Type checking (string, int, float, bool)
- Range validation (e.g., FACE_THRESHOLD must be 0.0-1.0)
- Required fields validation
- Format validation for complex types

## Security Best Practices

1. **Never commit .env files** to version control
2. **Use strong secret keys** in production
3. **Restrict database access** with proper credentials
4. **Use HTTPS** in production environments
5. **Limit CORS origins** to specific domains in production

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Check the `.env` file exists in the correct directory
   - Verify the `FR_` prefix is used
   - Ensure no spaces around the `=` sign

2. **Database connection fails**
   - Verify `FR_DB_HOST`, `FR_DB_PORT`, `FR_DB_USER`, `FR_DB_PASSWORD`
   - Check database server is running
   - Verify network connectivity

3. **Invalid configuration values**
   - Check the data types match expected types
   - Verify ranges for numeric values
   - Check boolean values are "true"/"false"

### Debug Configuration

To see current configuration values:

```python
from app.config import settings
import json

# Print all settings (be careful with sensitive data)
print(json.dumps(settings.dict(), indent=2, default=str))
```

## Migration from Old Config

If you have an old configuration file, here's how to migrate:

1. **Identify current values** in your old config
2. **Map to new environment variables** using the `FR_` prefix
3. **Create .env file** with your values
4. **Test the configuration** in a development environment
5. **Deploy to production** with environment-specific values

## Examples

### Basic Setup
```bash
# .env file
FR_DB_HOST=localhost
FR_DB_USER=myuser
FR_DB_PASSWORD=mypassword
FR_DEBUG=true
```

### Production Setup
```bash
# Production environment variables
FR_DB_HOST=prod-db.company.com
FR_DB_USER=prod_user
FR_DB_PASSWORD=secure_prod_password
FR_SECRET_KEY=very-long-random-production-key
FR_DEBUG=false
FR_LOG_LEVEL=ERROR
FR_CORS_ORIGINS=["https://myapp.company.com"]
```

This configuration system provides flexibility, security, and ease of deployment across different environments.
