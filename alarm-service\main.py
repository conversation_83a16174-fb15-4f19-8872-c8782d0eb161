from fastapi import FastAP<PERSON>, Depends, HTTPException
from models import WebhookPayload
from modules.face_recognition.processor import process_webhook_data
from modules.face_recognition.database import get_db, engine
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import inspect
from modules.face_recognition.face_recognition import FaceRecognitionModule


app = FastAPI()

mapping = {
    "face_recognition": FaceRecognitionModule()
}

@app.on_event("startup")
async def startup_event():
    async with engine.connect() as conn:
        tables = await conn.run_sync(lambda sync_conn: inspect(sync_conn).get_table_names())
        print("Tables available in DB:", tables)

@app.post("/webhook")
async def receive_webhook(
    payload: WebhookPayload,
    session: AsyncSession = Depends(get_db),
):
    section = payload.section
    
    print("webhook activated")
    alert = mapping[section]
    await alert.handle_event(payload, db=session)
    return {"messege": f"alarm handled for section {section}"}
    # if section == "face_recognition":
    #     face_recognition_module = FaceRecognitionModule()
    #     await face_recognition_module.handle_event(payload, db=session)
    #     return {"messsege":"alert sent for face recognition."}
   
