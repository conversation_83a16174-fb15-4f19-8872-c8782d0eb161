<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard</title>
  <link rel="stylesheet" href="{{ url_for('static', path='crowd_detection.css') }}">
</head>
<body>
  <div class="container">
    <div class="sidebar">

      <div>
         <div class="logo">
             <img src="/static/logo.png" alt="Logo"><br><br><br><br><br>
         
         </div>
 
         <div class="buttons">
             <button class="menu-btn" id="startStreamBtn">Start Monitoring</button>
             <button class="menu-btn" id="stopStreamBtn">Stop Monitoring</button>
             <button class="menu-btn" id="addroiBtn">Set Focus Area</button>
             <button class="setting-btn" id="managecamera">Camera Management</button>
             <button class="setting-btn" id="managealert">Alert Management</button>
 
         </div>
     
      </div>
 
        <button class="homepage-btn" id="homepageBtn">Go To Homepage</button>
 
     </div>

    <div class="main">
      <div class="webcam-section" id="webcamSection">
        <h2>Webcam View</h2>
        <!-- <video id="webcam" width="100%" height="auto" autoplay></video> -->
      </div>
      <div class="video-grid" id="feeds">
        <!-- Video feeds will be added dynamically -->
      </div>
    </div>

    <div class="live-observations">
      <h2>Live Observations</h2>
      <ul id="observationsList">
        <!-- Observations will be dynamically added here -->
      </ul>
    </div>
  </div>


 <!-- Popup window for managing cameras -->
 <div id="manageCameraModal" class="modal">
  <div class="modal-content">
    <span class="close-btn" id="closeModalBtn">&times;</span>
    <h2>Add RTSP Camera</h2>
    <form id="cameraForm">
      <label for="cameraName">Camera Name (Unique):</label>
      <input type="text" id="cameraName" name="cameraName" required><br><br>
      <label for="rtspUrl">RTSP URL:</label>
      <input type="text" id="rtspUrl" name="rtspUrl" required><br><br>
      <button type="submit">Add Camera</button>
      <button type="button" onclick="toggleList()">Show Cameras</button>
      <div style="display: none;" id="cameraList">
        <!-- Dynamically populate cameras and their delete buttons -->
      </div>
    </form>
  </div>
</div>



<!-- Popup window for managing alerts -->
<div id="manageAlertModal" class="modal">
  <div class="modal-content">
    <span class="close-btn" id="closeAlertModalBtn">&times;</span>
    <h2>Alert Management</h2>
    <div id="alertStatusContainer">
      <p>Alert Status: <span id="alertStatus" style="color: #f44336;">Inactive</span></p>
      <div class="alert-buttons">
        <button type="button" id="startAlertBtn">Start Alerts</button>
        <button type="button" id="stopAlertBtn">Stop Alerts</button>
      </div>
    </div>
    <form id="alertForm">
      <h3>Alert Schedule</h3>
      <div class="schedule-container">
        <div class="weekdays">
          <p>Active Days:</p>
          <div class="checkbox-group">
            <label><input type="checkbox" name="weekday" value="Monday"> Monday</label>
            <label><input type="checkbox" name="weekday" value="Tuesday"> Tuesday</label>
            <label><input type="checkbox" name="weekday" value="Wednesday"> Wednesday</label>
            <label><input type="checkbox" name="weekday" value="Thursday"> Thursday</label>
            <label><input type="checkbox" name="weekday" value="Friday"> Friday</label>
            <label><input type="checkbox" name="weekday" value="Saturday"> Saturday</label>
            <label><input type="checkbox" name="weekday" value="Sunday"> Sunday</label>
          </div>
        </div>
        <div class="time-settings">
          <div class="time-input">
            <label for="startTime">Start Time:</label>
            <input type="time" id="startTime" name="startTime">
          </div>
          <div class="time-input">
            <label for="endTime">End Time:</label>
            <input type="time" id="endTime" name="endTime">
          </div>
        </div>
      </div>
      
      <h3>Email Settings</h3>
      <div class="notification-settings">
        <div class="email-settings" id="emailSettings">
          <div class="input-group">
            <input type="email" id="newEmail" placeholder="Enter email address">
            <button type="button" id="addEmailBtn">Add</button>
          </div>
          <div id="emailList" class="recipient-list"></div>
        </div>
      </div>
      
      <h3>Alert Frequency</h3>
      <div class="frequency-settings">
        <label for="alertFrequency">Minimum time between alerts (minutes):</label>
        <input type="number" id="alertFrequency" name="alertFrequency" min="1" value="5">
      </div>
      
      <button type="submit" class="save-btn">Save Settings</button>
    </form>
  </div>
</div>





  <script>
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const feedsDiv = document.getElementById("feeds");
    const liveObservations = document.querySelector(".live-observations ul");
    const homepageBtn = document.getElementById("homepageBtn");
    const addroiBtn = document.getElementById("addroiBtn")

    let websocket = null;



    // Event listener for the "Go to Homepage" button
    homepageBtn.addEventListener("click", () => {
      // Redirect to the homepage
      window.location.href = "/";
    });

    // Event listener for the "Add Region of INTEREST" button
    addroiBtn.addEventListener("click", () => {
          // Redirect to the homepage
          window.location.href = "/crowd_detection/addroi";
    });



    // Function to start the video stream
    async function startStream() {
      try {
        const response = await fetch('/crowd_detection/start-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream started successfully");
          startWebSocketFeed();
        } else {
          console.error("Failed to start stream");
        }
      } catch (error) {
        console.error("Error starting stream:", error);
      }
    }

    // Function to stop the video stream
    async function stopStream() {
      try {
        if (websocket) {
          websocket.onmessage = null; // Remove old message handler
          // websocket.close(); // Close the WebSocket connection
          // websocket = null; // Clear the WebSocket reference
        }
        const response = await fetch('/crowd_detection/stop-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream stopped successfully");
          feedsDiv.innerHTML = ""; // Clear video feeds
          liveObservations.innerHTML = ""; // Clear observations
        } else {
          console.error("Failed to stop stream");
        }
      } catch (error) {
        console.error("Error stopping stream:", error);
      }
    }

    // Function to start the WebSocket connection and handle incoming frames and counts
  //  function startWebSocketFeed() {
  //     // if (websocket) {
  //     //   websocket.close(); // Close existing WebSocket connection if any
  //     // }


  //     websocket = new WebSocket("ws://localhost:8000/crowd_detection/ws");

  //     websocket.onmessage = (event) => {
  //       const message = event.data;
  //       const [cameraId, cam_name, data, count, roiCountsStr] = message.split(':');

  //       let roiCounts = []
  //       try {
  //         roiCounts = JSON.parse(roiCountsStr); // Safely parse ROI counts if JSON is valid
  //           } catch (error) {
  //         console.warn("Failed to parse ROI counts:", error);
  //         }


  //     // Update video feed
  //     let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
  //     if (!cameraContainer) {
  //       // Create a new container for this camera
  //       cameraContainer = document.createElement("div");
  //       cameraContainer.id = `camera-container-${cameraId}`;
  //       cameraContainer.classList.add("camera-container");

  //       // Add camera label
  //       const cameraLabel = document.createElement("h3");
  //       // cameraLabel.textContent = `Camera ${parseInt(cameraId) + 1}`;
  //       cameraLabel.textContent = cam_name;
  //       cameraContainer.appendChild(cameraLabel);

  //       // Add the video feed
  //       const imgElem = document.createElement("img");
  //       imgElem.id = `feed-${cameraId}`;
  //       imgElem.alt = "Processed Video Feed";
  //       cameraContainer.appendChild(imgElem);

  //       // Append the camera container to the feeds
  //       feedsDiv.appendChild(cameraContainer);
  //     }

  //     // Update the video feed image
  //     const imgElem = document.querySelector(`#feed-${cameraId}`);
  //     imgElem.src = `data:image/jpeg;base64,${data}`;

  //     // Update live observations
  //     let observationElem = document.querySelector(`#observation-${cameraId}`);
  //     if (!observationElem) {
  //       // Create a new list item if it doesn't exist
  //       observationElem = document.createElement("li");
  //       observationElem.id = `observation-${cameraId}`;
  //       liveObservations.appendChild(observationElem);
  //     }

  //     // Format observations
  //     // let observationText = `
  //     //   Camera ${parseInt(cameraId) + 1}<br>
  //     //     Total People Count: ${count}<br>
  //     // `;

      
  //     let observationText = `
  //        ${cam_name}<br><br>
  //         Total People Count: ${count}<br>
  //     `;

  //     // Add ROI details if they exist
  // if (roiCounts && roiCounts.length > 0) {
  //   roiCounts.forEach((roiCount, idx) => {
  //     observationText += `  Region ${idx + 1}: ${roiCount}<br>`;
  //     });
  //   } else {
  //   observationText += `No ROI data available.<br>`;
  //   }

  //   // Set the HTML content to include line breaks
  //   observationElem.innerHTML = observationText.trim();
  //   };

  //   websocket.onerror = (error) => {
  //     console.error("WebSocket error:", error);
  //   };

  //   websocket.onclose = () => {
  //     console.log("WebSocket closed");
  //   };
  // }


  function startWebSocketFeed() {
    // Initialize WebSocket connection
    websocket = new WebSocket("ws://localhost:8000/crowd_detection/ws");
    websocket.binaryType = 'arraybuffer'; // Set WebSocket to receive binary data

    let lastMetadata = null; // Store metadata for the next binary frame

    websocket.onopen = () => {
        console.log("WebSocket connection established.");
    };

    websocket.onmessage = (event) => {
        if (typeof event.data === 'string') {
            // Handle metadata (text message)
            lastMetadata = event.data.split(':');
        } else if (event.data instanceof ArrayBuffer) {
            // Handle binary frame (image data)
            if (lastMetadata) {
                const [cameraId, cam_name, count, roiCountsStr] = lastMetadata;
                let roiCounts = [];
                try {
                    roiCounts = JSON.parse(roiCountsStr); // Parse ROI counts
                } catch (error) {
                    console.warn("Failed to parse ROI counts:", error);
                }

                // Convert ArrayBuffer to Blob and create URL
                const blob = new Blob([event.data], { type: 'image/jpeg' });
                const url = URL.createObjectURL(blob);

                // Update video feed
                let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
                if (!cameraContainer) {
                    // Create a new container for this camera
                    cameraContainer = document.createElement("div");
                    cameraContainer.id = `camera-container-${cameraId}`;
                    cameraContainer.classList.add("camera-container");

                    // Add camera label
                    const cameraLabel = document.createElement("h3");
                    cameraLabel.textContent = cam_name;
                    cameraContainer.appendChild(cameraLabel);

                    // Add the video feed
                    const imgElem = document.createElement("img");
                    imgElem.id = `feed-${cameraId}`;
                    imgElem.alt = "Processed Video Feed";
                    cameraContainer.appendChild(imgElem);

                    // Append the camera container to the feeds
                    feedsDiv.appendChild(cameraContainer);
                }

                // Update the video feed image
                const imgElem = document.querySelector(`#feed-${cameraId}`);
                imgElem.src = url;

                // Update live observations
                let observationElem = document.querySelector(`#observation-${cameraId}`);
                if (!observationElem) {
                    // Create a new list item if it doesn't exist
                    observationElem = document.createElement("li");
                    observationElem.id = `observation-${cameraId}`;
                    liveObservations.appendChild(observationElem);
                }

                // Format observations
                let observationText = `
                    ${cam_name}<br><br>
                    Total People Count: ${count}<br>
                `;

                // Add ROI details if they exist
                if (roiCounts && roiCounts.length > 0) {
                    roiCounts.forEach((roiCount, idx) => {
                        observationText += `Region ${idx + 1}: ${roiCount}<br>`;
                    });
                } else {
                    observationText += `No ROI data available.<br>`;
                }

                // Set the HTML content to include line breaks
                observationElem.innerHTML = observationText.trim();

                // Reset metadata after processing
                lastMetadata = null;
            }
        }
    };

    websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
    };

    websocket.onclose = () => {
        console.log("WebSocket connection closed.");
    };
}

    // Event listeners for start and stop buttons
    startStreamBtn.addEventListener('click', startStream);
    stopStreamBtn.addEventListener('click', stopStream);









    // MANAGE CAMERA BUTTON POPUP 
    let showList = false
    const managecameraBtn = document.getElementById("managecamera");
    const modal = document.getElementById("manageCameraModal");
    const closeModalBtn = document.getElementById("closeModalBtn");
    const cameraForm = document.getElementById("cameraForm");

    function toggleList(){
      showList = !showList
      const cameraList = document.getElementById('cameraList')
      if(showList){
        cameraList.style.display = 'block'
      }else{
        cameraList.style.display = 'none'
      }
    }
    // Show modal when 'Manage Camera' button is clicked
    managecameraBtn.addEventListener("click", () => {
      modal.style.display = "block";
    });

    // Close modal when the close button is clicked
    closeModalBtn.addEventListener("click", () => {
      modal.style.display = "none";
    });

    // Close modal when clicking outside the modal
    window.addEventListener("click", (event) => {
      if (event.target === modal) {
        modal.style.display = "none";
      }
    });

    // Handle form submission to add the camera
    cameraForm.addEventListener("submit", async (e) => {
    e.preventDefault();
    
    const cameraName = document.getElementById("cameraName").value;
    const rtspUrl = document.getElementById("rtspUrl").value;
    
    // Send data to backend to save the camera information
    const response = await fetch('/crowd_detection/add-camera', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ cameraName, rtspUrl }),
    });

    const result = await response.json();
    
    if (result.status === 'success') {
      alert('Camera added successfully!');
      cameraForm.reset()
      loadCameras()
    } else if (result.status === 'error') {
      // Show the existing camera name in the alert message
      alert(`Error: RTSP URL already exists for camera: ${result.message.split(': ')[1]}`);
    } 
    else if (result.status === 'samename') {
      alert(`Same Name Already Exist`);
    }else {
      alert('Error adding camera.');
    }
  });

  // Function to load and display the list of cameras
  async function loadCameras() {
    const response = await fetch("/crowd_detection/get-cameras", {
      method: "GET",
    });
    const cameras = await response.json();

    const cameraList = document.getElementById("cameraList");
    cameraList.innerHTML = ""; // Clear previous list

    for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
      const cameraItem = document.createElement("div");
      cameraItem.innerHTML = `
        <p>
          <strong>${cameraName}</strong>:
          <button onclick="deleteCamera('${cameraName}')">Delete</button>
        </p>
      `;
      cameraList.appendChild(cameraItem);
    }
  }

  // Function to delete a camera
  async function deleteCamera(cameraName) {
    const confirmed = confirm(`Are you sure you want to delete the camera "${cameraName}"?`);
    if (!confirmed) return;

    const response = await fetch(`/crowd_detection/delete-camera/${cameraName}`, {
      method: "DELETE",
    });

    const result = await response.json();

    if (result.status === "success") {
      alert(result.message);
      loadCameras(); // Refresh the list
    } else {
      alert("Error deleting camera: " + result.message);
    }
  }

  // Load the camera list when the modal is opened
  managecameraBtn.addEventListener("click", () => {
    modal.style.display = "block";
    loadCameras(); // Populate the camera list
  });



// Alert Management Modal Elements
const managealertBtn = document.getElementById("managealert");
const alertModal = document.getElementById("manageAlertModal");
const closeAlertModalBtn = document.getElementById("closeAlertModalBtn");
const alertForm = document.getElementById("alertForm");
const startAlertBtn = document.getElementById("startAlertBtn");
const stopAlertBtn = document.getElementById("stopAlertBtn");
const alertStatus = document.getElementById("alertStatus");
const addEmailBtn = document.getElementById("addEmailBtn");
const emailList = document.getElementById("emailList");

// Variable to track alert status
let isAlertActive = false;
let emailRecipients = [];

// Show modal when 'Alert Management' button is clicked
managealertBtn.addEventListener("click", () => {
  alertModal.style.display = "block";
  loadAlertSettings(); // Load existing settings
});

// Close modal when the close button is clicked
closeAlertModalBtn.addEventListener("click", () => {
  alertModal.style.display = "none";
});

// Close modal when clicking outside the modal
window.addEventListener("click", (event) => {
  if (event.target === alertModal) {
    alertModal.style.display = "none";
  }
});

// Start Alert Button Click Handler
startAlertBtn.addEventListener("click", async () => {
  try {
    const response = await fetch('/crowd_detection/start-alerts', {
      method: 'POST',
    });
    
    if (response.ok) {
      isAlertActive = true;
      alertStatus.textContent = "Active";
      alertStatus.style.color = "#4CAF50";
      console.log("Alerts started successfully");
    } else {
      console.error("Failed to start alerts");
    }
  } catch (error) {
    console.error("Error starting alerts:", error);
  }
});

// Stop Alert Button Click Handler
stopAlertBtn.addEventListener("click", async () => {
  try {
    const response = await fetch('/crowd_detection/stop-alerts', {
      method: 'POST',
    });
    
    if (response.ok) {
      isAlertActive = false;
      alertStatus.textContent = "Inactive";
      alertStatus.style.color = "#f44336";
      console.log("Alerts stopped successfully");
    } else {
      console.error("Failed to stop alerts");
    }
  } catch (error) {
    console.error("Error stopping alerts:", error);
  }
});

// Add email recipient
addEmailBtn.addEventListener("click", function() {
  const emailInput = document.getElementById("newEmail");
  const email = emailInput.value.trim();
  
  if (validateEmail(email)) {
    if (!emailRecipients.includes(email)) {
      emailRecipients.push(email);
      renderEmailList();
      emailInput.value = "";
    } else {
      alert("This email address is already added.");
    }
  } else {
    alert("Please enter a valid email address.");
  }
});

// Render email list
function renderEmailList() {
  emailList.innerHTML = "";
  emailRecipients.forEach((email, index) => {
    const item = document.createElement("div");
    item.className = "recipient-item";
    item.innerHTML = `
      <span>${email}</span>
      <button type="button" class="remove-btn" data-index="${index}">Remove</button>
    `;
    emailList.appendChild(item);
  });
  
  // Add event listeners to remove buttons
  addRemoveListeners();
}

// Add listeners to remove buttons
function addRemoveListeners() {
  document.querySelectorAll(".remove-btn").forEach(button => {
    button.addEventListener("click", function() {
      const index = parseInt(this.getAttribute("data-index"));
      emailRecipients.splice(index, 1);
      renderEmailList();
    });
  });
}

// Validate email format
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

// Handle form submission to save alert settings
alertForm.addEventListener("submit", async (e) => {
  e.preventDefault();
  
  // Collect selected days
  const selectedDays = [];
  document.querySelectorAll('input[name="weekday"]:checked').forEach(checkbox => {
    selectedDays.push(checkbox.value);
  });
  
  const startTime = document.getElementById("startTime").value;
  const endTime = document.getElementById("endTime").value;
  const alertFrequency = document.getElementById("alertFrequency").value;
  
  // Create settings object
  const settings = {
    active: isAlertActive,
    days: selectedDays,
    startTime: startTime,
    endTime: endTime,
    alertFrequency: parseInt(alertFrequency),
    recipients: emailRecipients
  };
  
  try {
    // Send settings to backend
    const response = await fetch('/crowd_detection/save-alert-settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(settings),
      
    });

    const result = await response.json();
    
    if (result.status === 'success') {
      alert('Alert settings saved successfully!');
    } else {
      alert('Error saving alert settings: ' + (result.message || ''));
    }
  } catch (error) {
    console.error("Error saving alert settings:", error);
    alert('Error saving alert settings.');
  }
});

// Function to load existing alert settings
async function loadAlertSettings() {
  try {
    const response = await fetch("/crowd_detection/get-alert-settings", {
      method: "GET",
    });
    
    if (response.ok) {
      const settings = await response.json();
      
      // Update form with saved settings
      isAlertActive = settings.active;
      alertStatus.textContent = settings.active ? "Active" : "Inactive";
      alertStatus.style.color = settings.active ? "#4CAF50" : "#f44336";
      
      // Check the appropriate day checkboxes
      document.querySelectorAll('input[name="weekday"]').forEach(checkbox => {
        checkbox.checked = settings.days.includes(checkbox.value);
      });
      
      // Set time values
      document.getElementById("startTime").value = settings.startTime || "";
      document.getElementById("endTime").value = settings.endTime || "";
      document.getElementById("alertFrequency").value = settings.alertFrequency || 5;
      
      // Load email recipients
      emailRecipients = settings.recipients || [];
      renderEmailList();
      
    } else {
      console.error("Failed to load alert settings");
    }
  } catch (error) {
    console.error("Error loading alert settings:", error);
  }
}



  </script>
</body>
</html> 