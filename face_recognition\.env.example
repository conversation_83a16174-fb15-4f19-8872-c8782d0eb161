# Face Recognition System Environment Variables
# Copy this file to .env and modify the values as needed
# All variables are prefixed with FR_ (Face Recognition)

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
FR_APP_NAME=Face Recognition System
FR_APP_VERSION=1.0.0
FR_DEBUG=false
FR_TESTING=false

# =============================================================================
# SERVER SETTINGS
# =============================================================================
FR_HOST=0.0.0.0
FR_PORT=8000
FR_RELOAD=false

# =============================================================================
# DATABASE SETTINGS
# =============================================================================
FR_DB_USER=root
FR_DB_PASSWORD=Vis%40123hnu
FR_DB_HOST=localhost
FR_DB_NAME=face_recognition_db
FR_DB_PORT=3306
FR_DB_DRIVER=mysql+pymysql
FR_DB_ECHO=false

# =============================================================================
# FACE RECOGNITION SETTINGS
# =============================================================================
FR_FACE_THRESHOLD=0.7
FR_FACE_MODEL_PATH=
FR_FACE_ENCODING_MODEL=facenet
FR_MAX_FACE_SIZE=500
FR_MIN_FACE_SIZE=50

# =============================================================================
# IMAGE PROCESSING SETTINGS
# =============================================================================
FR_IMAGE_UPLOAD_MAX_SIZE=5242880
FR_ALLOWED_IMAGE_EXTENSIONS=[".jpg", ".jpeg", ".png", ".bmp"]
FR_IMAGE_QUALITY=95

# =============================================================================
# STORAGE SETTINGS
# =============================================================================
FR_STATIC_DIR=static
FR_IMAGES_DIR=static/images
FR_CROPPED_FACES_DIR=cropped_faces
FR_DATASET_DIR=Dataset
FR_UNKNOWN_DIR=Dataset/unknown

# =============================================================================
# CAMERA SETTINGS
# =============================================================================
FR_DEFAULT_CAMERA_TIMEOUT=30
FR_CAMERA_RETRY_ATTEMPTS=3
FR_FRAME_RATE=30

# =============================================================================
# QDRANT VECTOR DATABASE SETTINGS
# =============================================================================
FR_QDRANT_HOST=localhost
FR_QDRANT_PORT=6333
FR_QDRANT_COLLECTION_NAME=face_embeddings
FR_QDRANT_VECTOR_SIZE=512
FR_QDRANT_ENABLED=true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
FR_SECRET_KEY=your-secret-key-change-in-production-please-use-a-strong-random-key
FR_ACCESS_TOKEN_EXPIRE_MINUTES=30
FR_CORS_ORIGINS=["*"]

# =============================================================================
# LOGGING SETTINGS
# =============================================================================
FR_LOG_LEVEL=INFO
FR_LOG_FILE=
FR_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
FR_MAX_WORKERS=4
FR_BATCH_SIZE=32
FR_CACHE_TTL=3600

# =============================================================================
# ATTENDANCE SETTINGS
# =============================================================================
FR_ATTENDANCE_TIME_WINDOW=60
FR_DUPLICATE_ATTENDANCE_PREVENTION=true

# =============================================================================
# MODEL SETTINGS
# =============================================================================
FR_DEVICE=auto
FR_MODEL_CACHE_DIR=models
FR_DOWNLOAD_MODELS=true

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# Development Environment
# FR_DEBUG=true
# FR_RELOAD=true
# FR_LOG_LEVEL=DEBUG
# FR_DB_ECHO=true

# Production Environment
# FR_DEBUG=false
# FR_RELOAD=false
# FR_LOG_LEVEL=WARNING
# FR_SECRET_KEY=your-super-secure-production-key
# FR_DB_HOST=production-db-server
# FR_DB_PASSWORD=secure-production-password

# Docker Environment
# FR_DB_HOST=db
# FR_QDRANT_HOST=qdrant
# FR_HOST=0.0.0.0

# High Performance Environment
# FR_MAX_WORKERS=8
# FR_BATCH_SIZE=64
# FR_DEVICE=cuda
# FR_CACHE_TTL=7200
