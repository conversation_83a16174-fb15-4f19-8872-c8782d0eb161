import aiosmtplib
from email.message import EmailMessage

async def send_email(subject: str, body: str, to: str, from_email: str, smtp_host: str, smtp_port: int, username: str, password: str):
    message = EmailMessage()
    message["From"] = from_email
    message["To"] = to
    message["Subject"] = subject
    message.set_content(body)

    await aiosmtplib.send(
        message,
        hostname=smtp_host,
        port=smtp_port,
        username=username,
        password=password,
        use_tls=True,
    )
