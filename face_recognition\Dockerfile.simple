FROM python:3.10-slim

# Prevent prompts during build
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies for dlib and OpenCV
RUN apt-get update && \
    apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libx11-dev \
    libatlas-base-dev \
    libgtk-3-dev \
    libboost-python-dev \
    libboost-all-dev \
    libopenblas-dev \
    liblapack-dev \
    libhdf5-dev \
    libprotobuf-dev \
    protobuf-compiler \
    libgoogle-glog-dev \
    libgflags-dev \
    libgphoto2-dev \
    libeigen3-dev \
    libhdf5-dev \
    doxygen \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libfontconfig1 \
    libxrender1 \
    libxtst6 \
    libxi6 \
    libxrandr2 \
    libasound2 \
    curl \
    wget \
    bzip2 \
    unzip \
    git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Upgrade pip and install Python dependencies
RUN pip install --upgrade pip setuptools wheel

# Install dlib using a more reliable method
RUN pip install --no-cache-dir cmake && \
    pip install --no-cache-dir --verbose dlib

# Install face_recognition which depends on dlib
RUN pip install --no-cache-dir face_recognition

# Install other requirements (excluding dlib and face_recognition since we installed them separately)
RUN grep -v "^dlib" requirements.txt | grep -v "^face-recognition" > requirements_filtered.txt && \
    pip install --no-cache-dir -r requirements_filtered.txt

# Copy all project files
COPY . .

# Create models directory if it doesn't exist
RUN mkdir -p /app/app/models

# Download dlib face landmarks model if it doesn't exist
RUN if [ ! -f /app/app/models/shape_predictor_68_face_landmarks.dat ]; then \
    echo "Downloading dlib face landmarks model..." && \
    curl -L "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2" \
    -o /app/app/models/shape_predictor_68_face_landmarks.dat.bz2 && \
    bzip2 -d /app/app/models/shape_predictor_68_face_landmarks.dat.bz2 && \
    echo "Model downloaded and extracted successfully"; \
    else \
    echo "Face landmarks model already exists"; \
    fi

# Verify the model file exists and show its size
RUN ls -la /app/app/models/shape_predictor_68_face_landmarks.dat && \
    echo "Model file size: $(du -h /app/app/models/shape_predictor_68_face_landmarks.dat)"

# Set environment variables for face recognition
ENV FACE_RECOGNITION_MODELS_PATH=/app/app/models
ENV DLIB_MODEL_PATH=/app/app/models/shape_predictor_68_face_landmarks.dat

# Test dlib installation
RUN python -c "import dlib; print(f'dlib version: {dlib.DLIB_VERSION}'); detector = dlib.get_frontal_face_detector(); print('dlib face detector works!')"

# Test face_recognition installation
RUN python -c "import face_recognition; print('face_recognition library works!')"

# Expose FastAPI port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run startup script which includes all checks and starts the app
CMD ["python", "startup.py"]
