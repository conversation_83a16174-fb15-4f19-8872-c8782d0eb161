import base64
import os
import cv2
import numpy as np
import logging
from fastapi import APIRouter, Depends, HTTPException, Request, Form, File, UploadFile, Query, WebSocket, Body
from sqlalchemy.orm import Session
from starlette.responses import HTMLResponse, JSONResponse
from starlette.templating import Jinja2Templates
from typing import List, Optional
from sqlalchemy import or_
from .utils import crop_face, align_face, generate_encoding
from . import models
from .models import User, Image, Encoding
from .database import get_db

# Set up logging
logger = logging.getLogger(__name__)
from fastapi.datastructures import UploadFile as FastAPIUploadFile
from starlette.datastructures import FormData
from app.tester import FaceDetection
from app.services.qdrant_service import QdrantService
from fastapi import Request, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, Integer, cast
from typing import Optional, Dict, Any, List
from datetime import timezone
from pydantic import BaseModel


try:
    qdrant_service = QdrantService()
    if not qdrant_service.available:
        logger.warning("Qdrant service is not available. Vector search functionality will be disabled.")
except Exception as e:
    logger.error(f"Failed to initialize Qdrant service: {e}")
    qdrant_service = None

logging.basicConfig(level=logging.DEBUG)

# Camera configuration is now stored in the database

router = APIRouter()

templates = Jinja2Templates(directory="templates")

def get_db():
    db = database.SessionLocal()
    try:
        yield db
    finally:
        db.close()


@router.get("/")
async def show_face_recognition_page(request: Request):
    # Get user role from request state (set by middleware)
    user_role = getattr(request.state, "user", {}).get("role", "user")
    return templates.TemplateResponse(
        "face_recognition.html",
        {"request": request, "user_role": user_role}
    )

@router.get("/registration")
async def show_registration_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})


@router.post("/register")
async def register(
    request: Request,
    db: Session = Depends(get_db)
):
    try:
        # Manually parse form with increased size limit (5MB)
        form_data = await request.form()

        # Extract form fields
        username = form_data.get("username")
        email = form_data.get("email")
        image_file = form_data.get("image_file")
        captured_image = form_data.get("captured_image")

        if not username or not email:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Username and email are required"}
            )

        if not image_file and not captured_image:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Please provide an image"}
            )

        # Check if user exists
        db_user = db.query(models.User).filter(models.User.username == username).first()
        if db_user:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Username already taken"}
            )

        db_email = db.query(models.User).filter(models.User.email == email).first()
        if db_email:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Email already registered"}
            )

        # Process the image
        if image_file and hasattr(image_file, "file"):
            # Read the image file content
            contents = await image_file.read()
            nparr = np.frombuffer(contents, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        elif captured_image:
            # Process base64 image
            try:
                encoded_data = captured_image.split(',')[1]
                nparr = np.frombuffer(base64.b64decode(encoded_data), np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                return JSONResponse(
                    status_code=400,
                    content={"status": "error", "message": f"Invalid image data: {str(e)}"}
                )
        else:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No valid image provided"}
            )

        # Crop the face
        cropped_face = crop_face(img)
        if cropped_face is None:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No face detected in the image"}
            )

        # Save the original image
        image_filename = f"{username}.jpg"
        image_path = f"static/images/{image_filename}"
        os.makedirs(os.path.dirname(image_path), exist_ok=True)
        cv2.imwrite(image_path, img)

        # Save the cropped face
        cropped_image_path = f"static/images/cropped_{image_filename}"
        cv2.imwrite(cropped_image_path, cropped_face)

        # Save user to the database
        db_user = models.User(username=username, email=email)
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # Generate face encoding
        encoding = generate_encoding(face_image=cropped_face)

        # Save the image details to the database
        db_image = models.Image(filename=image_filename, image_url=cropped_image_path, user_id=db_user.user_id)
        db.add(db_image)
        db.commit()
        db.refresh(db_image)

        # Save the encoding details to the database
        encoding_list = encoding.tolist()
        db_encoding = models.Encoding(encoding=encoding_list, user_id=db_user.user_id, image_id=db_image.id)
        db.add(db_encoding)
        db.commit()

        # Return success response
        return JSONResponse(
            content={
                "status": "success",
                "message": "User registered successfully",
                "user": username,
                "image_path": cropped_image_path
            }
        )
    except Exception as e:
        print(f"Registration error: {str(e)}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Registration failed: {str(e)}"}
        )

@router.get("/available_cameras")
async def available_cameras():
    available_cameras = get_cameras()
    print
    return {"cameras": available_cameras}

@router.get("/get_attendance", response_class=JSONResponse)
async def get_attendance(db: Session = Depends(get_db)):
    """Fetch attendance records"""
    attendance_records = db.query(models.Attendance).all()
    return [
        {
            "id": record.id,
            "user_id": record.user_id,
            "timestamp": record.timestamp,
            "camera_name": record.camera_name or "System Camera"
        }
        for record in attendance_records
    ]


@router.get("/attendance")
def render_attendance(
    request: Request,
    db: Session = Depends(get_db),
    username: Optional[str] = Query(None, alias="username"),  # Filter by username
    start_date: Optional[str] = Query(None, alias="start_date"),  # Filter by start date
    end_date: Optional[str] = Query(None, alias="end_date")  # Filter by end date
):
    """Fetch attendance and render as HTML table with usernames, filtered by query parameters."""
    try:
        # Get statistics first (before applying filters)
        total_records = db.query(models.Attendance).count()
        unique_users = db.query(models.Attendance.user_id).distinct().count()

        # Get today's attendance count
        today = datetime.datetime.now().date()
        today_start = datetime.datetime.combine(today, datetime.time.min)
        today_end = datetime.datetime.combine(today, datetime.time.max)
        today_attendance = db.query(models.Attendance).filter(
            models.Attendance.timestamp >= today_start,
            models.Attendance.timestamp <= today_end
        ).count()

        # Get the most recent attendance record
        last_record = db.query(models.Attendance).order_by(models.Attendance.timestamp.desc()).first()
        last_record_time = "None"
        if last_record:
            # Format the time as a relative string (e.g., "2 hours ago")
            time_diff = datetime.datetime.now() - last_record.timestamp
            if time_diff.days > 0:
                last_record_time = f"{time_diff.days}d ago"
            elif time_diff.seconds // 3600 > 0:
                last_record_time = f"{time_diff.seconds // 3600}h ago"
            elif time_diff.seconds // 60 > 0:
                last_record_time = f"{time_diff.seconds // 60}m ago"
            else:
                last_record_time = "Just now"

        # Get all attendance records
        all_attendance = db.query(models.Attendance).order_by(models.Attendance.timestamp.desc()).all()

        # Separate regular users and unknown users
        regular_user_ids = []
        unknown_user_ids = []

        for record in all_attendance:
            if isinstance(record.user_id, str) and record.user_id.startswith('unknown_'):
                unknown_user_ids.append(record.user_id)
            else:
                try:
                    # Convert to int for regular users
                    user_id = int(record.user_id)
                    regular_user_ids.append(user_id)
                except (ValueError, TypeError):
                    # If conversion fails, treat as unknown
                    unknown_user_ids.append(record.user_id)

        # Start building the query for regular users
        query = db.query(models.Attendance, models.User).join(
            models.User,
            cast(models.Attendance.user_id, Integer) == models.User.user_id
        ).filter(
            ~models.Attendance.user_id.startswith('unknown_')
        )

        # Apply filters if provided
        if username:
            query = query.filter(models.User.username.ilike(f"%{username}%"))  # Filter by username (case-insensitive)

        # Parse date strings to datetime objects if provided
        parsed_start_date = None
        parsed_end_date = None
        today = datetime.datetime.now().date()

        # Handle end date
        if end_date:
            try:
                parsed_end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                # If date parsing fails, ignore this filter
                print(f"Invalid end date format: {end_date}")
                parsed_end_date = today

        # Handle start date
        if start_date:
            try:
                parsed_start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            except ValueError:
                # If date parsing fails, ignore this filter
                print(f"Invalid start date format: {start_date}")
                # If we have an end date, set start date to 30 days before
                if parsed_end_date:
                    parsed_start_date = parsed_end_date - datetime.timedelta(days=30)
        elif parsed_end_date:
            # If only end date is provided, set start date to 30 days before
            parsed_start_date = parsed_end_date - datetime.timedelta(days=30)

        # If only start date is provided, set end date to today
        if parsed_start_date and not parsed_end_date:
            parsed_end_date = today

        # Apply date filters if we have them
        if parsed_start_date:
            query = query.filter(models.Attendance.timestamp >= datetime.datetime.combine(parsed_start_date, datetime.time.min))

        if parsed_end_date:
            query = query.filter(models.Attendance.timestamp <= datetime.datetime.combine(parsed_end_date, datetime.time.max))

        # Order by timestamp (most recent first)
        query = query.order_by(models.Attendance.timestamp.desc())

        # Execute the query for regular users
        regular_attendance_records = query.all()

        # Prepare the data for regular users
        rendered_data = [
            {
                "user_id": record.Attendance.user_id,
                "username": record.User.username,
                "timestamp": record.Attendance.timestamp,
                "camera_name": record.Attendance.camera_name or "System Camera",  # Use actual camera name or default
                "is_unknown": False
            }
            for record in regular_attendance_records
        ]

        # Now handle unknown users
        unknown_attendance_query = db.query(models.UnknownAttendance, models.Unknown).join(
            models.Unknown,
            models.UnknownAttendance.unknown_id == models.Unknown.id
        )

        # Apply date filters to unknown records too
        if parsed_start_date:
            unknown_attendance_query = unknown_attendance_query.filter(
                models.UnknownAttendance.timestamp >= datetime.datetime.combine(parsed_start_date, datetime.time.min)
            )

        if parsed_end_date:
            unknown_attendance_query = unknown_attendance_query.filter(
                models.UnknownAttendance.timestamp <= datetime.datetime.combine(parsed_end_date, datetime.time.max)
            )

        # Order by timestamp
        unknown_attendance_query = unknown_attendance_query.order_by(models.UnknownAttendance.timestamp.desc())

        # Execute the query for unknown users
        unknown_attendance_records = unknown_attendance_query.all()

        # Add unknown user data
        for record in unknown_attendance_records:
            # Get image path from either UnknownAttendance or Unknown model
            image_path = record.UnknownAttendance.image_path

            # If no image path in attendance record, use the one from Unknown model
            if not image_path and record.Unknown.image_path:
                image_path = record.Unknown.image_path

            # Add to rendered data
            rendered_data.append({
                "user_id": f"unknown_{record.Unknown.id}",
                "username": f"Unknown Person #{record.Unknown.id}",
                "timestamp": record.UnknownAttendance.timestamp,
                "camera_name": record.UnknownAttendance.camera_name or "System Camera",
                "image_path": image_path,  # Include the image path from either source
                "is_unknown": True
            })

        # Sort all records by timestamp (most recent first)
        rendered_data.sort(key=lambda x: x["timestamp"], reverse=True)

        # Count filtered records
        filtered_count = len(rendered_data)

        return templates.TemplateResponse(
            "attendance.html",
            {
                "request": request,
                "attendance_records": rendered_data,
                "total_records": total_records,
                "unique_users": unique_users,
                "today_attendance": today_attendance,
                "last_record_time": last_record_time,
                "filtered_count": filtered_count
            },
        )
    except Exception as e:
        # Log the error
        print(f"Error in render_attendance: {str(e)}")

        # Return a basic response with error information
        return templates.TemplateResponse(
            "attendance.html",
            {
                "request": request,
                "attendance_records": [],
                "total_records": 0,
                "unique_users": 0,
                "today_attendance": 0,
                "last_record_time": "Error",
                "filtered_count": 0,
                "error_message": f"An error occurred: {str(e)}"
            },
        )

@router.get("/users")
async def get_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return [{"id": user.user_id, "username": user.username, "email": user.email} for user in users]

# This endpoint has been replaced by the one at line ~1445
# Keeping this comment for reference

@router.get("/users_page")
async def users_page(request:Request):
    return templates.TemplateResponse("users.html", {"request": request})



from huggingface_hub import hf_hub_download
import asyncio
from .models import CameraData

# Define a model for settings
class Settings(BaseModel):
    face_threshold: float




detection = None  # Global instance of Face_Detection
detection_lock = asyncio.Lock()

@router.post('/start')
async def start_detection(db: Session = Depends(get_db)):
    global detection

    # Check if cameras are configured before starting detection
    cameras = load_cameras(db)
    if not cameras or len(cameras) == 0:
        return {
            "status": "error",
            "message": "No cameras configured. Please add cameras in the admin panel before starting face recognition.",
            "error_type": "no_cameras"
        }

    # Get current threshold setting
    current_threshold = 0.7  # Default value

    if detection is None:
        model_path = hf_hub_download(repo_id="arnabdhar/YOLOv8-Face-Detection", filename="model.pt")
        detection = FaceDetection(model_path=model_path, db=db)  # Inject db session at runtime
    else:
        # If detection exists, get its current threshold setting (if any)
        current_threshold = detection.threshold if detection.threshold is not None else 0.7
        # Always refresh the database session to ensure we have the latest data
        detection.db = db
        logger.info("Database session refreshed for face detection")

    # If detection is not running, start it with the current threshold
    if not detection.running:
        detection.start(threshold=current_threshold)

    return {"status": "Detection started", "threshold": current_threshold, "cameras_count": len(cameras)}

# Improved stop detection endpoint with better error handling
@router.post('/stop')
async def stop_detection():
    global detection

    if detection is None:
        return {"status": "Detection was not running"}

    try:
        if detection.running:
            detection.stop()
            return {"status": "Detection stopped"}
        else:
            return {"status": "Detection was already stopped"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error stopping detection: {str(e)}")

# WebSocket endpoint to stream frames
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    global detection

    # Track if this websocket is closed
    is_closed = False

    await websocket.accept()
    try:
        while not is_closed:
            async with detection_lock:
                if detection is None or not detection.running:
                    await asyncio.sleep(0.1)  # If detection is stopped, wait and check again
                    continue

                for idx, frame in enumerate(detection.frames):
                    if is_closed:
                        break

                    if frame and idx < len(detection.cameraname):  # Ensure index is valid
                        try:
                            camera_name = detection.cameraname[idx]
                            # Send message with camera index, name, and frame data
                            message = f"{idx}:{camera_name}:{frame}"
                            await websocket.send_text(message)
                            # Debug log
                            print(f"Sent frame for camera {idx}:{camera_name}, data length: {len(frame) if frame else 0}")
                        except WebSocketDisconnect:
                            print("WebSocket disconnected during frame send")
                            is_closed = True
                            break
                        except Exception as e:
                            print(f"Error sending frame: {e}")
                            # If we get a "Cannot call send once a close message has been sent" error,
                            # mark the connection as closed
                            if "close message has been sent" in str(e):
                                is_closed = True
                                break

                if not is_closed:
                    await asyncio.sleep(0.033)  # 30 FPS (1/30 ≈ 0.033)
    except WebSocketDisconnect:
        print("WebSocket disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        print("WebSocket connection closed")


# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: models.CameraData, db: Session = Depends(get_db)):
    # Check if camera name already exists
    existing_camera = db.query(models.Camera).filter(models.Camera.name == camera_data.cameraName).first()
    if existing_camera:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}

    # Check if RTSP URL already exists
    existing_url = db.query(models.Camera).filter(models.Camera.rtsp_url == camera_data.rtspUrl).first()
    if existing_url:
        return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_url.name}"}

    # Create new camera record
    new_camera = models.Camera(
        name=camera_data.cameraName,
        rtsp_url=camera_data.rtspUrl,
    )

    db.add(new_camera)
    db.commit()

    return {"status": "success", "message": "Camera added successfully"}


# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str, db: Session = Depends(get_db)):
    camera = db.query(models.Camera).filter(models.Camera.name == camera_name).first()

    if not camera:
        raise HTTPException(status_code=404, detail="Camera not found")

    db.delete(camera)
    db.commit()

    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}


@router.get("/admin")
async def admin_page(request: Request):
    # Always set admin role, no authentication check
    user_role = "admin"
    
    # Return admin template with admin role
    return templates.TemplateResponse("admin.html", {"request": request, "user_role": user_role})

@router.get("/get-cameras")
async def get_cameras(db: Session = Depends(get_db)):
    print("getting cameras..")
    cameras = db.query(models.Camera).all()
    result = {}
    for camera in cameras:
        result[camera.name] = [camera.rtsp_url]
    print("cameras:", result)
    return result



from .models import Unknown, User, Encoding, Image, ClusteringParams
from fastapi import Form
from .services.face_clustering import FaceClusteringService

@router.post("/assign-unknown")
async def assign_unknown(
    unknown_id: int = Form(...),
    username: str = Form(...),
    email: str = Form(...),
    db: Session = Depends(get_db)
):
    # Log the received data for debugging
    print(f"[DEBUG] Assigning unknown: ID={unknown_id}, Username={username}, Email={email}")

    # Check if the unknown record exists
    unknown = db.query(Unknown).filter(Unknown.id == unknown_id).first()
    if not unknown:
        raise HTTPException(status_code=404, detail="Unknown ID not found.")

    # Find the associated unknown image files
    image_dir = "Dataset/unknown"
    unknown_files = [f for f in os.listdir(image_dir) if f"unknown_{unknown.persistent_id}_" in f]

    if not unknown_files:
        raise HTTPException(status_code=404, detail="No image found for the given unknown.")

    # Check if the user already exists
    existing_user = db.query(User).filter(User.username == username).first()

    if existing_user:
        # User exists, get the count of images for this user to create a serial number
        image_count = db.query(Image).filter(Image.user_id == existing_user.user_id).count()
        serial_number = image_count + 1
        user_id = existing_user.user_id
        print(f"[DEBUG] Using existing user: ID={user_id}, Username={username}, Email={email}")

        # Update email if it's different (to handle cases where username matches but email doesn't)
        if existing_user.email != email:
            print(f"[DEBUG] Updating email for user {username} from {existing_user.email} to {email}")
            existing_user.email = email
            db.commit()
    else:
        # Create new user
        new_user = User(username=username, email=email)
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        user_id = new_user.user_id
        serial_number = 1
        print(f"[DEBUG] Created new user: ID={user_id}, Username={username}, Email={email}")

    # Process all images for this unknown person
    first_image_path = None

    for i, image_filename in enumerate(unknown_files):
        image_path = os.path.join(image_dir, image_filename)

        # Save the first image path for profile photo
        if i == 0:
            first_image_path = image_path

        # Create a new filename with serial number
        file_extension = os.path.splitext(image_filename)[1]
        new_filename = f"{username}_{serial_number}{file_extension}"

        # Create user directory if it doesn't exist
        new_path = os.path.join("Dataset", username)
        os.makedirs(new_path, exist_ok=True)

        # Copy the image to the user's directory (using shutil.copy to keep the original)
        import shutil
        new_image_path = os.path.join(new_path, new_filename)
        shutil.copy(image_path, new_image_path)

        # Add image entry to database
        db_image = Image(filename=new_filename, image_url=new_image_path, user_id=user_id)
        db.add(db_image)
        db.commit()
        db.refresh(db_image)

        # For existing users, we still want to save the encoding in the MySQL database
        # This helps with future face recognition but doesn't affect Qdrant
        db_encoding = Encoding(encoding=unknown.encoding, user_id=user_id, image_id=db_image.id)
        db.add(db_encoding)
        db.commit()
        print(f"[INFO] Saved encoding in MySQL database for {'existing' if existing_user else 'new'} user {username} (ID: {user_id})")

        # For existing users, we don't want to add new embeddings to Qdrant
        # Only add embeddings for new users
        if not existing_user:
            try:
                # Make sure encoding is in the right format
                encoding_list = unknown.encoding
                if not isinstance(encoding_list, list):
                    try:
                        encoding_list = list(encoding_list)
                    except:
                        print(f"[WARNING] Failed to convert encoding to list, using as is")

                # Add the face embedding to Qdrant only for new users
                success = qdrant_service.add_face_embedding(
                    user_id=str(user_id),  # Always convert to string
                    username=username,
                    encoding=encoding_list
                )

                if success:
                    print(f"[INFO] Added face embedding to Qdrant for new user {username} (ID: {user_id})")
                else:
                    print(f"[WARNING] Failed to add face embedding to Qdrant for new user {username} (ID: {user_id})")

                    # Try again with a simpler approach
                    retry_success = qdrant_service.add_face_embedding(
                        user_id=str(user_id),
                        username=username,
                        encoding=encoding_list
                    )

                    if retry_success:
                        print(f"[INFO] Successfully added face embedding to Qdrant on retry for new user {username} (ID: {user_id})")
                    else:
                        print(f"[ERROR] Failed to add face embedding to Qdrant even after retry for new user {username} (ID: {user_id})")
            except Exception as e:
                print(f"[ERROR] Failed to add face embedding to Qdrant: {e}")

                # Try again with a simpler approach
                try:
                    # Make sure encoding is in the right format
                    encoding_list = unknown.encoding
                    if not isinstance(encoding_list, list):
                        try:
                            encoding_list = list(encoding_list)
                        except:
                            print(f"[WARNING] Failed to convert encoding to list, using as is")

                    # Try again with explicit conversion
                    retry_success = qdrant_service.add_face_embedding(
                        user_id=str(user_id),
                        username=username,
                        encoding=encoding_list
                    )

                    if retry_success:
                        print(f"[INFO] Successfully added face embedding to Qdrant on exception retry for new user {username} (ID: {user_id})")
                    else:
                        print(f"[ERROR] Failed to add face embedding to Qdrant even after exception retry for new user {username} (ID: {user_id})")
                except Exception as retry_error:
                    print(f"[ERROR] Failed to add face embedding to Qdrant on retry: {retry_error}")
        else:
            print(f"[INFO] Skipping adding face embedding to Qdrant for existing user {username} (ID: {user_id})")

        # Increment serial number for next image
        serial_number += 1

    # Save the first image as the profile photo in static/images directory, but only if the user doesn't already have one
    if first_image_path:
        # Ensure the static/images directory exists
        profile_dir = "static/images"
        os.makedirs(profile_dir, exist_ok=True)

        # Check if the user already has a profile photo
        profile_filename = f"{username}.jpg"
        profile_path = os.path.join(profile_dir, profile_filename)

        # Only create a new profile photo if one doesn't already exist or if this is a new user
        if not os.path.exists(profile_path) or not existing_user:
            print(f"[INFO] Creating new profile photo for {username}")

            try:
                # Read the image and resize it to a standard size for profile photos
                import cv2
                import numpy as np

                # Read the image
                img = cv2.imread(first_image_path)
                if img is not None:
                    # Resize to a standard size (e.g., 200x200) while maintaining aspect ratio
                    height, width = img.shape[:2]
                    max_dim = 200

                    # Calculate the scaling factor
                    scale = max_dim / max(height, width)
                    new_width = int(width * scale)
                    new_height = int(height * scale)

                    # Resize the image
                    resized_img = cv2.resize(img, (new_width, new_height))

                    # Create a square canvas with white background
                    canvas = np.ones((max_dim, max_dim, 3), dtype=np.uint8) * 255

                    # Calculate position to paste the resized image (center it)
                    y_offset = (max_dim - new_height) // 2
                    x_offset = (max_dim - new_width) // 2

                    # Paste the resized image onto the canvas
                    canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized_img

                    # Save the profile photo
                    cv2.imwrite(profile_path, canvas)
                    print(f"[INFO] Saved profile photo for {username} at {profile_path}")
                else:
                    # If reading with OpenCV fails, just copy the file
                    shutil.copy(first_image_path, profile_path)
                    print(f"[INFO] Copied profile photo for {username} to {profile_path}")
            except Exception as e:
                print(f"[ERROR] Failed to save profile photo: {e}")
                # Fallback to simple copy if image processing fails
                try:
                    shutil.copy(first_image_path, profile_path)
                    print(f"[INFO] Copied profile photo for {username} to {profile_path} (fallback)")
                except Exception as copy_error:
                    print(f"[ERROR] Failed to copy profile photo: {copy_error}")
        else:
            print(f"[INFO] Keeping existing profile photo for {username} at {profile_path}")

    # Create attendance records for the unknown person based on image timestamps and unknown attendance records
    attendance_records_created = 0

    # First, get all unknown attendance records for this unknown person
    unknown_attendance_records = db.query(models.UnknownAttendance).filter(
        models.UnknownAttendance.unknown_id == unknown.id
    ).all()

    print(f"[DEBUG] Found {len(unknown_attendance_records)} unknown attendance records for unknown ID {unknown.id}")

    # Create a list to store the attendance records we'll create
    new_attendance_records = []

    # First, collect all the timestamps from unknown attendance records
    for unknown_attendance in unknown_attendance_records:
        try:
            # Check if an attendance record already exists for this user within a minute of this timestamp
            time_window_start = unknown_attendance.timestamp - datetime.timedelta(minutes=1)
            time_window_end = unknown_attendance.timestamp + datetime.timedelta(minutes=1)

            existing_attendance = db.query(models.Attendance).filter(
                models.Attendance.user_id == user_id,
                models.Attendance.timestamp >= time_window_start,
                models.Attendance.timestamp <= time_window_end
            ).first()

            # Only create a new attendance record if one doesn't already exist
            if not existing_attendance:
                # Ensure user_id is an integer
                try:
                    user_id_int = int(user_id)
                    new_attendance_records.append({
                        "user_id": user_id_int,
                        "timestamp": unknown_attendance.timestamp,
                        "camera_name": unknown_attendance.camera_name or "Unknown Camera"
                    })
                    attendance_records_created += 1
                    print(f"[DEBUG] Added attendance record from unknown attendance: user_id={user_id_int}, timestamp={unknown_attendance.timestamp}")
                except (ValueError, TypeError) as e:
                    print(f"[ERROR] Invalid user_id format: {user_id}, error: {e}")
        except Exception as e:
            print(f"[ERROR] Failed to create attendance record from unknown attendance: {e}")

    # Also extract timestamps from image filenames as a fallback
    for image_filename in unknown_files:
        try:
            # Format of unknown image filename: unknown_{persistent_id}_{timestamp}.jpg
            parts = image_filename.split('_')
            if len(parts) >= 3:
                # Extract timestamp from filename
                timestamp_str = parts[2].split('.')[0]  # Remove file extension
                timestamp = int(timestamp_str)

                # Convert Unix timestamp to datetime with proper timezone handling
                # Use the local timezone instead of UTC to match the system time
                attendance_date = datetime.datetime.fromtimestamp(timestamp)

                # Check if an attendance record already exists for this user within a minute of this timestamp
                time_window_start = attendance_date - datetime.timedelta(minutes=1)
                time_window_end = attendance_date + datetime.timedelta(minutes=1)

                existing_attendance = db.query(models.Attendance).filter(
                    models.Attendance.user_id == user_id,
                    models.Attendance.timestamp >= time_window_start,
                    models.Attendance.timestamp <= time_window_end
                ).first()

                # Only create a new attendance record if one doesn't already exist
                if not existing_attendance:
                    # Extract camera name from the image filename if available
                    # Format: unknown_{persistent_id}_{timestamp}_{camera_name}.jpg
                    camera_name = "Unknown Camera"  # Default value
                    if len(parts) >= 4:  # If camera name is included in filename
                        camera_name = parts[3].split('.')[0]  # Remove file extension

                    # Check if we already have this timestamp in our new records
                    duplicate = False
                    for record in new_attendance_records:
                        if abs((record["timestamp"] - attendance_date).total_seconds()) < 60:
                            duplicate = True
                            break

                    if not duplicate:
                        # Ensure user_id is an integer
                        try:
                            user_id_int = int(user_id)
                            new_attendance_records.append({
                                "user_id": user_id_int,
                                "timestamp": attendance_date,
                                "camera_name": camera_name
                            })
                            attendance_records_created += 1
                            print(f"[DEBUG] Added attendance record from image filename: user_id={user_id_int}, timestamp={attendance_date}")
                        except (ValueError, TypeError) as e:
                            print(f"[ERROR] Invalid user_id format: {user_id}, error: {e}")
        except Exception as e:
            print(f"[ERROR] Failed to create attendance record from image {image_filename}: {e}")

    # Delete associated unknown attendance records first
    for record in unknown_attendance_records:
        db.delete(record)

    # Commit the deletion of unknown attendance records
    db.commit()
    print(f"[INFO] Deleted {len(unknown_attendance_records)} unknown attendance records")

    # Now create the new attendance records
    print(f"[DEBUG] Creating {len(new_attendance_records)} new attendance records for user {username} (ID: {user_id})")

    for record_data in new_attendance_records:
        try:
            attendance_record = models.Attendance(
                user_id=user_id,  # Explicitly use user_id to ensure it's correct
                timestamp=record_data["timestamp"],
                camera_name=record_data["camera_name"]
            )
            db.add(attendance_record)
            print(f"[DEBUG] Added attendance record: user_id={user_id}, timestamp={record_data['timestamp']}, camera={record_data['camera_name']}")
        except Exception as e:
            print(f"[ERROR] Failed to create attendance record: {e}")

    # Commit the new attendance records
    if new_attendance_records:
        try:
            db.commit()
            print(f"[INFO] Created {len(new_attendance_records)} attendance records for user {username}")
        except Exception as e:
            print(f"[ERROR] Failed to commit attendance records: {e}")
            db.rollback()

    # Delete the unknown entry from Qdrant if it exists
    if qdrant_service:
        try:
            # The user_id in Qdrant for unknown persons is the persistent_id
            # Make sure we're passing a string to delete_face_embedding
            qdrant_service.delete_face_embedding(str(unknown.persistent_id))
            print(f"[INFO] Deleted all unknown embeddings from Qdrant for persistent_id: {unknown.persistent_id}")
        except Exception as e:
            print(f"[ERROR] Failed to delete unknown embeddings from Qdrant: {e}")
            import traceback
            print(f"[ERROR] Traceback: {traceback.format_exc()}")

    # Delete the unknown entry and its images after they've been copied
    for image_filename in unknown_files:
        image_path = os.path.join(image_dir, image_filename)
        if os.path.exists(image_path):
            try:
                os.remove(image_path)
                print(f"[INFO] Deleted image file: {image_path}")
            except Exception as e:
                print(f"[ERROR] Failed to delete image file {image_path}: {e}")

    # Delete the unknown record from the database
    db.delete(unknown)
    db.commit()
    print(f"[INFO] Deleted unknown record with ID: {unknown_id} and persistent_id: {unknown.persistent_id}")

    # Reload detection cache if running
    if detection is not None:
        detection.reload_encodings_and_users()
        print("[INFO] Reloaded detection cache")

    return {
        "message": f"Unknown assigned to user '{username}' successfully with {len(unknown_files)} images and {attendance_records_created} attendance records."
    }


@router.get("/get-unknowns")
async def get_unknowns(db: Session = Depends(get_db)):
    unknown_list = []
    folder_path = "Dataset/unknown"

    # First, get all unknown records from the database
    unknown_records = db.query(Unknown).all()
    print(f"Found {len(unknown_records)} unknown records in database")

    # Create a set of all persistent IDs from the database
    db_persistent_ids = {record.persistent_id for record in unknown_records}

    # Get all unique persistent IDs from image files
    file_persistent_ids = set()
    if os.path.exists(folder_path):
        for file in os.listdir(folder_path):
            if file.startswith("unknown_"):
                parts = file.split("_")
                if len(parts) >= 3:
                    # Extract persistent ID from filename (format: unknown_<persistent_id>_<timestamp>.jpg)
                    persistent_id = parts[1]
                    file_persistent_ids.add(persistent_id)

    print(f"Found {len(file_persistent_ids)} unique persistent IDs in image files")

    # Find persistent IDs that are in files but not in database
    missing_ids = file_persistent_ids - db_persistent_ids

    # Add missing persistent IDs to the database
    for persistent_id in missing_ids:
        print(f"Adding missing persistent ID to database: {persistent_id}")
        # Create a new Unknown record with a default empty encoding array
        # This is needed because the encoding column is non-nullable
        new_unknown = Unknown(persistent_id=persistent_id, encoding=[])
        db.add(new_unknown)

    # Commit changes if any new records were added
    if missing_ids:
        db.commit()
        # Refresh the unknown records query to include the newly added records
        unknown_records = db.query(Unknown).all()

    # Now process all unknown records
    for record in unknown_records:
        persistent_id = record.persistent_id

        images = []  # Changed from set() to list to avoid duplicate removal
        if os.path.exists(folder_path):
            for file in os.listdir(folder_path):
                if file.startswith(f"unknown_{persistent_id}_"):
                    # Add each file only once
                    # Use forward slashes for consistency in paths
                    image_path = "/" + folder_path + "/" + file
                    if image_path not in images:  # Explicit check for duplicates
                        images.append(image_path)

        # Get attendance records for this unknown person
        attendance_records = db.query(models.UnknownAttendance).filter(
            models.UnknownAttendance.unknown_id == record.id
        ).order_by(models.UnknownAttendance.timestamp.desc()).all()

        # Format attendance records
        attendance_data = []
        for attendance in attendance_records:
            attendance_data.append({
                "timestamp": attendance.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                "camera_name": attendance.camera_name or "System Camera"
            })

        # Only add to the list if there are images for this unknown person
        if images:
            unknown_list.append({
                "id": record.id,
                "persistent_id": persistent_id,
                "image_paths": images,
                "attendance_records": attendance_data
            })

    print(f"Returning {len(unknown_list)} unknown persons with images")
    return JSONResponse(content={"unknowns": unknown_list})



@router.delete("/delete-unknown/{unknown_id}")
async def delete_unknown(unknown_id: int, db: Session = Depends(get_db)):
    unknown = db.query(Unknown).filter(Unknown.id == unknown_id).first()
    if not unknown:
        raise HTTPException(status_code=404, detail="Unknown record not found.")

    # Remove associated image files
    folder_path = "Dataset/unknown"
    deleted_files = 0
    for file in os.listdir(folder_path):
        if f"unknown_{unknown.persistent_id}_" in file:
            os.remove(os.path.join(folder_path, file))
            deleted_files += 1

    # Delete associated unknown attendance records
    unknown_attendance_records = db.query(models.UnknownAttendance).filter(
        models.UnknownAttendance.unknown_id == unknown.id
    ).all()

    for record in unknown_attendance_records:
        db.delete(record)

    # Delete the unknown entry from Qdrant if it exists
    if qdrant_service:
        try:
            # The user_id in Qdrant for unknown persons is the persistent_id
            # Make sure we're passing a string to delete_face_embedding
            qdrant_service.delete_face_embedding(str(unknown.persistent_id))
            print(f"[INFO] Deleted all unknown embeddings from Qdrant for persistent_id: {unknown.persistent_id}")
        except Exception as e:
            print(f"[ERROR] Failed to delete unknown embeddings from Qdrant: {e}")
            import traceback
            print(f"[ERROR] Traceback: {traceback.format_exc()}")

    # Delete the unknown record
    db.delete(unknown)
    db.commit()
    print(f"[INFO] Deleted unknown record with ID: {unknown_id} and persistent_id: {unknown.persistent_id}")

    # Refresh detection cache if running
    if detection is not None and detection.running:
        detection.reload_encodings_and_users()

    return {"message":"User deleted successfully and cache refreshed."}

@router.delete("/delete-unknown-image")
async def delete_unknown_image(
    image_path: str = Query(..., description="Path to the image file to delete"),
    db: Session = Depends(get_db)
):
    """Delete a single image from an unknown person without deleting the entire record."""

    # Validate the image path
    if not image_path.startswith("/Dataset/unknown/unknown_"):
        raise HTTPException(status_code=400, detail="Invalid image path format")

    # Remove the leading slash for file operations
    file_path = image_path[1:] if image_path.startswith("/") else image_path

    # Check if the file exists
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Image file not found")

    # Extract the persistent_id from the filename
    # Format: /Dataset/unknown/unknown_{persistent_id}_{timestamp}.jpg
    try:
        filename = os.path.basename(file_path)
        parts = filename.split("_")
        if len(parts) < 3:
            raise HTTPException(status_code=400, detail="Invalid filename format")

        persistent_id = parts[1]

        # Find the unknown record
        unknown = db.query(Unknown).filter(Unknown.persistent_id == persistent_id).first()
        if not unknown:
            raise HTTPException(status_code=404, detail="Unknown record not found")

        # Delete the file
        os.remove(file_path)

        # Check if this was the last image for this unknown person
        remaining_files = [f for f in os.listdir("Dataset/unknown") if f"unknown_{persistent_id}_" in f]

        if not remaining_files:
            # If no images left, delete the unknown attendance records
            unknown_attendance_records = db.query(models.UnknownAttendance).filter(
                models.UnknownAttendance.unknown_id == unknown.id
            ).all()

            for record in unknown_attendance_records:
                db.delete(record)

            # Delete the unknown entry from Qdrant if it exists
            if qdrant_service:
                try:
                    # The user_id in Qdrant for unknown persons is the persistent_id
                    # Make sure we're passing a string to delete_face_embedding
                    qdrant_service.delete_face_embedding(str(unknown.persistent_id))
                    print(f"[INFO] Deleted all unknown embeddings from Qdrant for persistent_id: {unknown.persistent_id}")
                except Exception as e:
                    print(f"[ERROR] Failed to delete unknown embeddings from Qdrant: {e}")
                    import traceback
                    print(f"[ERROR] Traceback: {traceback.format_exc()}")

            # Delete the unknown record
            db.delete(unknown)
            db.commit()
            print(f"[INFO] Deleted unknown record with ID: {unknown.id} and persistent_id: {unknown.persistent_id}")

            # Refresh detection cache if running
            if detection is not None and detection.running:
                detection.reload_encodings_and_users()

            return {
                "message": "Image deleted successfully. No images remain, so the unknown record was also deleted.",
                "record_deleted": True
            }
        else:
            # Refresh detection cache if running
            if detection is not None and detection.running:
                detection.reload_encodings_and_users()

            return {
                "message": "Image deleted successfully.",
                "record_deleted": False,
                "remaining_images": len(remaining_files)
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting image: {str(e)}")


@router.post("/reload-cache")
async def reload_detection_cache():
    global detection
    if detection is None:
        raise HTTPException(status_code=400, detail="Detection program not running.")

    detection.reload_encodings_and_users()
    return {"message": "Encodings and users cache reloaded successfully."}

@router.post("/sync-database-to-qdrant")
async def sync_database_to_qdrant(db: Session = Depends(get_db)):
    """Sync all users from MySQL database to Qdrant vector database"""
    if not qdrant_service:
        raise HTTPException(status_code=500, detail="Qdrant service not available")

    try:
        # Get all users from the database
        users = db.query(User).all()
        success_count = 0
        failure_count = 0

        print(f"Starting synchronization of {len(users)} users to Qdrant...")

        for user in users:
            # Get the corresponding encoding
            encoding = db.query(Encoding).filter(Encoding.user_id == user.user_id).first()
            if not encoding:
                print(f"No encoding found for user {user.username} (ID: {user.user_id})")
                continue

            try:
                # First delete all existing embeddings for this user
                try:
                    qdrant_service.delete_face_embedding(str(user.user_id))
                    print(f"[INFO] Deleted all existing embeddings for user {user.username} (ID: {user.user_id}) from Qdrant")
                except Exception as delete_error:
                    print(f"[WARNING] Error deleting existing embeddings: {delete_error}")

                # Then add the new embedding
                success = qdrant_service.add_face_embedding(
                    user_id=str(user.user_id),
                    username=user.username,
                    encoding=encoding.encoding
                )

                if success:
                    success_count += 1
                    print(f"Successfully synced user {user.username} (ID: {user.user_id}) to Qdrant")
                else:
                    failure_count += 1
                    print(f"Failed to sync user {user.username} (ID: {user.user_id}) to Qdrant")
            except Exception as e:
                failure_count += 1
                print(f"Error syncing user {user.username} (ID: {user.user_id}) to Qdrant: {str(e)}")

        # Also sync unknown persons
        unknowns = db.query(Unknown).all()
        unknown_success = 0
        unknown_failure = 0

        print(f"Starting synchronization of {len(unknowns)} unknown persons to Qdrant...")

        for unknown in unknowns:
            try:
                # First delete all existing embeddings for this unknown
                try:
                    qdrant_service.delete_face_embedding(str(unknown.persistent_id))
                    print(f"[INFO] Deleted all existing embeddings for unknown (ID: {unknown.persistent_id}) from Qdrant")
                except Exception as delete_error:
                    print(f"[WARNING] Error deleting existing embeddings: {delete_error}")

                # Then add the new embedding
                success = qdrant_service.add_face_embedding(
                    user_id=str(unknown.persistent_id),
                    username=f"unknown_{unknown.persistent_id[:8]}",
                    encoding=unknown.encoding
                )

                if success:
                    unknown_success += 1
                    print(f"Successfully synced unknown person (ID: {unknown.persistent_id}) to Qdrant")
                else:
                    unknown_failure += 1
                    print(f"Failed to sync unknown person (ID: {unknown.persistent_id}) to Qdrant")
            except Exception as e:
                unknown_failure += 1
                print(f"Error syncing unknown person (ID: {unknown.persistent_id}) to Qdrant: {str(e)}")

        # Reload detection cache if running
        if detection is not None:
            detection.reload_encodings_and_users()
            print("Reloaded detection cache")

        return {
            "message": f"Database synchronized with Qdrant. Users: {success_count} successes, {failure_count} failures. Unknowns: {unknown_success} successes, {unknown_failure} failures."
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error syncing database to Qdrant: {str(e)}")

# User Management Routes
@router.get("/users")
async def get_users(
    search: str = Query(None, description="Search term for username or email"),
    db: Session = Depends(get_db)
):
    """Get all registered users with optional search filter"""
    query = db.query(models.User)

    if search:
        # Case-insensitive search on username or email
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                models.User.username.ilike(search_term),
                models.User.email.ilike(search_term)
            )
        )

    users = query.all()

    # Format the results with user images
    result = []
    for user in users:
        # Get user images from database
        images = db.query(models.Image).filter(models.Image.user_id == user.user_id).all()

        user_images = []
        for img in images:
            user_images.append({
                "id": img.id,
                "image_url": img.image_url
            })

        # Add user to result
        result.append({
            "id": user.user_id,
            "username": user.username,
            "email": user.email,
            "images": user_images
        })

    return result

@router.get("/check-unknown-matches/{unknown_id}")
async def check_unknown_matches(
    unknown_id: int,
    threshold: float = Query(0.6, description="Similarity threshold for matching"),
    db: Session = Depends(get_db)
):
    """
    Check if an unknown person matches any registered users.
    Returns a list of potential matches with similarity scores.
    """
    # Get the unknown person's encoding
    unknown = db.query(Unknown).filter(Unknown.id == unknown_id).first()
    if not unknown:
        raise HTTPException(status_code=404, detail="Unknown ID not found.")

    unknown_encoding = np.array(unknown.encoding)

    # Get all registered users with their encodings
    users = db.query(models.User).all()

    matches = []
    for user in users:
        # Get the user's encodings
        encodings = db.query(models.Encoding).filter(models.Encoding.user_id == user.user_id).all()

        if not encodings:
            continue

        # Calculate similarity with each encoding and keep the highest
        highest_similarity = -1
        for encoding in encodings:
            user_encoding = np.array(encoding.encoding)
            similarity = calculate_cosine_similarity(unknown_encoding, user_encoding)
            highest_similarity = max(highest_similarity, similarity)

        # If similarity is above threshold, add to matches
        if highest_similarity >= threshold:
            matches.append({
                "id": user.user_id,
                "username": user.username,
                "email": user.email,
                "similarity": float(highest_similarity)
            })

    # Sort matches by similarity (highest first)
    matches.sort(key=lambda x: x["similarity"], reverse=True)

    return {
        "unknown_id": unknown_id,
        "matches": matches,
        "is_likely_registered": len(matches) > 0
    }

@router.get("/user/{user_id}")
async def get_user_details(
    user_id: int,
    db: Session = Depends(get_db)
):
    """Get details for a specific user"""
    user = db.query(models.User).filter(models.User.user_id == user_id).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get user images
    images = db.query(models.Image).filter(models.Image.user_id == user.user_id).all()

    user_images = []
    for img in images:
        user_images.append({
            "id": img.id,
            "image_url": img.image_url
        })

    # Get user attendance records
    attendance = db.query(models.Attendance).filter(
        models.Attendance.user_id == user.user_id
    ).order_by(
        models.Attendance.timestamp.desc()
    ).limit(10).all()

    attendance_records = []
    for record in attendance:
        # Use the actual camera_name from the Attendance model
        camera_name = record.camera_name or "System Camera"

        attendance_records.append({
            "id": record.id,
            "timestamp": record.timestamp.isoformat(),
            "camera_name": camera_name
        })

    return {
        "id": user.user_id,
        "username": user.username,
        "email": user.email,
        "employee_id": user.employee_id,
        "department": user.department,
        "dob": user.dob,
        "address": user.address,
        "phone_number": user.phone_number,
        "images": user_images,
        "attendance": attendance_records
    }

@router.get("/user/{user_id}/page")
async def user_details_page(
    user_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """Render the user details page"""
    # Check if user exists
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return templates.TemplateResponse("user_details.html", {"request": request})

@router.get("/users/{user_id}/camera-permissions")
async def get_user_camera_permissions(
    user_id: int,
    db: Session = Depends(get_db)
):
    """Get camera permissions for a specific user"""
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get all cameras
    all_cameras = db.query(models.Camera).all()

    # Get user's camera permissions
    user_permissions = db.query(models.CameraPermission).filter(
        models.CameraPermission.user_id == user_id
    ).all()

    # Create a set of camera IDs the user has access to
    permitted_camera_ids = {perm.camera_id for perm in user_permissions}

    # Build response with all cameras and their permission status
    cameras_with_permissions = []
    for camera in all_cameras:
        cameras_with_permissions.append({
            "camera_id": camera.id,
            "camera_name": camera.name,
            "rtsp_url": camera.rtsp_url,
            "has_permission": camera.id in permitted_camera_ids
        })

    return {
        "user_id": user_id,
        "username": user.username,
        "cameras": cameras_with_permissions
    }

@router.post("/users/{user_id}/camera-permissions")
async def update_user_camera_permissions(
    user_id: int,
    camera_ids: List[int] = Body(..., embed=True),
    db: Session = Depends(get_db)
):
    """Update camera permissions for a specific user"""
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Validate that all camera IDs exist
    existing_cameras = db.query(models.Camera).filter(models.Camera.id.in_(camera_ids)).all()
    if len(existing_cameras) != len(camera_ids):
        raise HTTPException(status_code=400, detail="One or more camera IDs are invalid")

    # Delete existing permissions for this user
    db.query(models.CameraPermission).filter(
        models.CameraPermission.user_id == user_id
    ).delete()

    # Add new permissions
    for camera_id in camera_ids:
        permission = models.CameraPermission(
            user_id=user_id,
            camera_id=camera_id
        )
        db.add(permission)

    db.commit()

    return {
        "message": "Camera permissions updated successfully",
        "user_id": user_id,
        "permitted_cameras": camera_ids
    }

@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """Delete a user and all associated data"""
    user = db.query(models.User).filter(models.User.user_id == user_id).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        # Get the username for logging
        username = user.username
        print(f"[INFO] Deleting user: {username} (ID: {user_id})")

        # Delete the user's embedding from Qdrant if it exists
        if qdrant_service:
            try:
                # Make sure to convert user_id to string for Qdrant
                user_id_str = str(user_id)

                # First try to delete by user_id as string
                qdrant_service.delete_face_embedding(user_id_str)
                print(f"[INFO] Deleted all user embeddings from Qdrant for user_id: {user_id} (String ID: {user_id_str}), username: {username}")

                # Also try to delete by user_id as integer (for backward compatibility)
                try:
                    qdrant_service.delete_face_embedding(user_id)
                    print(f"[INFO] Also deleted any user embeddings from Qdrant for user_id as integer: {user_id}, username: {username}")
                except Exception as int_error:
                    print(f"[INFO] No embeddings found with integer user_id: {user_id}")
            except Exception as e:
                print(f"[ERROR] Failed to delete user embeddings from Qdrant: {e}")
                import traceback
                print(f"[ERROR] Traceback: {traceback.format_exc()}")

        # Delete user images from the database
        images = db.query(models.Image).filter(models.Image.user_id == user.user_id).all()

        for img in images:
            # Delete the image file if it exists
            image_path = os.path.join(os.getcwd(), img.image_url)
            if os.path.exists(image_path):
                os.remove(image_path)
                print(f"[INFO] Deleted image file: {image_path}")

            # Delete the database record
            db.delete(img)

        # Delete cropped face image if it exists
        cropped_face_path = os.path.join(os.getcwd(), f"cropped_faces/{user.username}.jpg")
        if os.path.exists(cropped_face_path):
            os.remove(cropped_face_path)
            print(f"[INFO] Deleted cropped face image: {cropped_face_path}")

        # Delete attendance records
        attendance_records = db.query(models.Attendance).filter(
            models.Attendance.user_id == user.user_id
        ).all()

        for record in attendance_records:
            db.delete(record)
        print(f"[INFO] Deleted {len(attendance_records)} attendance records for user: {username}")

        # Delete the user's encodings from the database
        encodings = db.query(models.Encoding).filter(models.Encoding.user_id == user.user_id).all()
        for encoding in encodings:
            db.delete(encoding)
        print(f"[INFO] Deleted {len(encodings)} encodings for user: {username}")

        # Delete the user
        db.delete(user)
        db.commit()
        print(f"[INFO] Deleted user with ID: {user_id}, username: {username}")

        # Reload the detection cache to update the encodings
        if detection is not None:
            try:
                detection.reload_encodings_and_users()
                print("[INFO] Reloaded detection cache")
            except Exception as e:
                print(f"[ERROR] Failed to reload detection cache: {e}")

        return {"message": f"User '{username}' deleted successfully"}
    except Exception as e:
        db.rollback()
        print(f"[ERROR] Failed to delete user: {e}")
        import traceback
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error deleting user: {str(e)}")

# Route to serve cropped face images
@router.get("/cropped_faces/{filename}")
async def get_cropped_face(filename: str):
    """Serve cropped face images"""
    from fastapi.responses import FileResponse

    file_path = f"cropped_faces/{filename}"

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Image not found")

    return FileResponse(file_path)

# Settings routes
@router.get("/get-settings")
async def get_settings():
    """Get the current face recognition settings"""
    global detection

    if detection is None:
        # Return default settings if detection is not initialized
        return {"face_threshold": 0.7}

    # Return the current threshold from the detection instance
    # If threshold is None (not yet initialized), return default value
    threshold = detection.threshold if detection.threshold is not None else 0.7
    return {"face_threshold": threshold}

@router.post("/update-settings")
async def update_settings(settings: Settings):
    """Update face recognition settings"""
    global detection

    if detection is None:
        # Initialize detection with the new threshold if it doesn't exist
        model_path = hf_hub_download(repo_id="arnabdhar/YOLOv8-Face-Detection", filename="model.pt")
        db = database.SessionLocal()
        detection = FaceDetection(model_path=model_path, db=db)
        # The threshold will be set when start is called
        db.close()
    else:
        # Update the threshold in the detection instance
        detection.threshold = settings.face_threshold
        print(f"Updated threshold to: {settings.face_threshold}")

        # If detection is running, reload the encodings to apply the new threshold
        if detection.running:
            detection.reload_encodings_and_users()

    return {"message": "Settings updated successfully"}

@router.post("/cluster-unknowns")
async def cluster_unknowns(params: ClusteringParams = Body(ClusteringParams()), db: Session = Depends(get_db)):
    """
    Cluster unknown faces using HDBSCAN algorithm.

    This endpoint also checks all images for faces and removes those without faces
    before performing the clustering.
    """
    clustering_service = FaceClusteringService(db)
    result = clustering_service.cluster_faces(
        min_cluster_size=params.min_cluster_size,
        min_samples=params.min_samples
    )

    # Add a more user-friendly message if images were removed
    if result.get('removed_images', 0) > 0:
        removed_count = result.get('removed_images')
        result['message'] = f"Successfully clustered faces into {len(result.get('clusters', {}))} groups. {removed_count} images without faces were automatically removed."

    return result

@router.get("/get-available-clusters")
async def get_available_clusters(db: Session = Depends(get_db)):
    """Get all available clusters for moving images between clusters."""
    # Get all distinct cluster_ids
    result = db.query(Unknown.cluster_id).distinct().all()

    # Extract cluster_ids and filter out None values
    cluster_ids = [r[0] for r in result if r[0] is not None]

    # Add a special option for unclustered
    clusters = [
        {"id": "unclustered", "name": "Unclustered", "is_unclustered": True}
    ]

    # Create a mapping of cluster IDs to simple numeric names
    # First, sort the cluster_ids to ensure consistent numbering
    sorted_cluster_ids = sorted(cluster_ids)
    cluster_name_map = {cluster_id: f"Cluster {i+1}" for i, cluster_id in enumerate(sorted_cluster_ids)}

    # Add all other clusters with user-friendly names
    for cluster_id in sorted_cluster_ids:
        # Count faces in this cluster
        count = db.query(Unknown).filter(Unknown.cluster_id == cluster_id).count()
        clusters.append({
            "id": cluster_id,
            "name": f"{cluster_name_map[cluster_id]} ({count} faces)",
            "display_name": cluster_name_map[cluster_id],
            "is_unclustered": False,
            "face_count": count
        })

    return {"clusters": clusters}

@router.post("/move-to-cluster")
async def move_to_cluster(
    unknown_id: int = Query(..., description="ID of the unknown face to move"),
    target_cluster_id: str = Query(..., description="ID of the target cluster"),
    db: Session = Depends(get_db)
):
    """Move an unknown face from its current cluster to a different cluster."""
    # Find the unknown record
    unknown = db.query(Unknown).filter(Unknown.id == unknown_id).first()
    if not unknown:
        raise HTTPException(status_code=404, detail="Unknown face not found")

    # Check if the target cluster exists (at least one face with this cluster_id)
    target_exists = db.query(Unknown).filter(Unknown.cluster_id == target_cluster_id).first() is not None

    # If moving to "unclustered", set cluster_id to None
    if target_cluster_id.lower() == "none" or target_cluster_id.lower() == "unclustered":
        unknown.cluster_id = None
        db.commit()
        return {"message": f"Successfully moved unknown face to unclustered group"}

    # If the target doesn't exist and it's not "unclustered", check if it's a valid format
    if not target_exists and not target_cluster_id.startswith("cluster_"):
        raise HTTPException(status_code=400, detail="Invalid target cluster ID format")

    # Update the cluster_id
    old_cluster_id = unknown.cluster_id
    unknown.cluster_id = target_cluster_id
    db.commit()

    return {
        "message": f"Successfully moved unknown face from {old_cluster_id or 'unclustered'} to {target_cluster_id}",
        "old_cluster_id": old_cluster_id,
        "new_cluster_id": target_cluster_id
    }

@router.get("/get-clustered-unknowns")
async def get_clustered_unknowns(db: Session = Depends(get_db)):
    """Get unknown faces grouped by clusters."""
    clustering_service = FaceClusteringService(db)
    result = clustering_service.get_clustered_unknowns()
    return result

@router.get("/get-registered-users")
async def get_registered_users(db: Session = Depends(get_db)):
    """Get all registered users for dropdown selection."""
    users = db.query(models.User).all()

    # Format the results with minimal information needed for dropdown
    result = []
    for user in users:
        result.append({
            "id": user.user_id,
            "username": user.username,
            "email": user.email
        })

    return {"users": result}

@router.post("/assign-cluster")
async def assign_cluster(
    cluster_id: str = Form(...),
    username: str = Form(...),
    email: str = Form(...),
    db: Session = Depends(get_db)
):
    """Assign all unknown faces in a cluster to a user."""
    # Get all unknowns in the cluster
    unknowns = db.query(Unknown).filter(Unknown.cluster_id == cluster_id).all()

    if not unknowns:
        raise HTTPException(status_code=404, detail="No unknowns found in this cluster.")

    # Check if the user already exists
    existing_user = db.query(User).filter(User.username == username).first()

    if existing_user:
        # User exists, get the count of images for this user to create a serial number
        image_count = db.query(Image).filter(Image.user_id == existing_user.user_id).count()
        serial_number = image_count + 1
        user_id = existing_user.user_id
    else:
        # Create new user
        new_user = User(username=username, email=email)
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        user_id = new_user.user_id
        serial_number = 1

    # Process all images for all unknowns in this cluster
    for unknown in unknowns:
        # Find the associated unknown image files
        image_dir = "Dataset/unknown"
        unknown_files = [f for f in os.listdir(image_dir) if f"unknown_{unknown.persistent_id}_" in f]

        for i, image_filename in enumerate(unknown_files):
            # Source path
            src_path = os.path.join(image_dir, image_filename)

            # Create user directory if it doesn't exist
            user_dir = os.path.join("Dataset", username)
            os.makedirs(user_dir, exist_ok=True)

            # Destination path with serial number
            dest_filename = f"{username}_{serial_number + i}.jpg"
            dest_path = os.path.join(user_dir, dest_filename)

            # Copy the file
            import shutil
            shutil.copy2(src_path, dest_path)

            # Load the image for encoding
            img = cv2.imread(src_path)
            if img is not None:
                # Generate encoding
                try:
                    face_encoding = generate_encoding(img)

                    # Save image to database
                    db_image = Image(filename=dest_filename, image_url=dest_path, user_id=user_id)
                    db.add(db_image)
                    db.commit()
                    db.refresh(db_image)

                    # Save encoding to database
                    face_encoding_list = face_encoding.tolist()
                    db_encoding = Encoding(encoding=face_encoding_list, user_id=user_id, image_id=db_image.id)
                    db.add(db_encoding)
                    db.commit()

                    # Add the face encoding to Qdrant
                    try:
                        # For existing users, we don't want to add new embeddings to Qdrant
                        # Only add embeddings for new users
                        if not existing_user:
                            # First, delete any existing embeddings for this user ID
                            try:
                                qdrant_service.delete_face_embedding(str(user_id))
                                print(f"[INFO] Deleted any existing embeddings for new user {username} (ID: {user_id}) from Qdrant")
                            except Exception as delete_error:
                                print(f"[WARNING] Error deleting existing embeddings: {delete_error}")

                            # Then add the new embedding
                            success = qdrant_service.add_face_embedding(
                                user_id=str(user_id),  # Always convert to string
                                username=username,
                                encoding=face_encoding_list
                            )

                            if success:
                                print(f"[INFO] Added face embedding to Qdrant for new user {username} (ID: {user_id})")
                            else:
                                print(f"[WARNING] Failed to add face embedding to Qdrant for new user {username} (ID: {user_id})")
                        else:
                            print(f"[INFO] Skipping adding face embedding to Qdrant for existing user {username} (ID: {user_id})")
                    except Exception as qdrant_error:
                        print(f"[ERROR] Failed to add face embedding to Qdrant: {qdrant_error}")
                        import traceback
                        print(f"[ERROR] Traceback: {traceback.format_exc()}")
                except Exception as e:
                    print(f"Error generating encoding for {src_path}: {str(e)}")

        # Delete the unknown record from the database
        db.delete(unknown)
        db.commit()

        # Delete the unknown image files
        for image_filename in unknown_files:
            try:
                os.remove(os.path.join(image_dir, image_filename))
            except Exception as e:
                print(f"Error deleting file {image_filename}: {str(e)}")

    # Refresh detection cache if running
    if detection is not None and detection.running:
        detection.reload_encodings_and_users()

    return {"message": f"Successfully assigned {len(unknowns)} unknown faces to user {username}"}


@router.post("/user/{user_id}/update")
async def update_user(
    user_id: int,
    user_data: models.UserUpdate,
    db: Session = Depends(get_db)
):
    """Update user information"""
    # Check if user exists
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if employee_id is unique if provided
    if user_data.employee_id and user_data.employee_id != user.employee_id:
        existing_user = db.query(models.User).filter(
            models.User.employee_id == user_data.employee_id,
            models.User.user_id != user_id
        ).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Employee ID already in use")

    # Update user fields if provided
    if user_data.username is not None:
        # Check if username is unique
        existing_user = db.query(models.User).filter(
            models.User.username == user_data.username,
            models.User.user_id != user_id
        ).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Username already taken")
        user.username = user_data.username

    if user_data.email is not None:
        # Check if email is unique
        existing_user = db.query(models.User).filter(
            models.User.email == user_data.email,
            models.User.user_id != user_id
        ).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Email already registered")
        user.email = user_data.email

    if user_data.employee_id is not None:
        user.employee_id = user_data.employee_id

    if user_data.department is not None:
        user.department = user_data.department

    if user_data.dob is not None:
        user.dob = user_data.dob

    if user_data.address is not None:
        user.address = user_data.address

    if user_data.phone_number is not None:
        user.phone_number = user_data.phone_number

    # Save changes to database
    db.commit()

    return {"message": "User information updated successfully"}

@router.post("/user/{user_id}/update-photo")
async def update_profile_photo(
    user_id: int,
    photo: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Update a user's profile photo and face encoding"""
    # Check if user exists
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        # Read the uploaded file
        contents = await photo.read()

        # Convert to numpy array for face detection
        nparr = np.frombuffer(contents, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if img is None:
            raise HTTPException(status_code=400, detail="Invalid image file")

        # Ensure the static/images directory exists
        profile_dir = "static/images"
        os.makedirs(profile_dir, exist_ok=True)

        # Save the profile photo
        profile_filename = f"{user.username}.jpg"
        profile_path = os.path.join(profile_dir, profile_filename)

        # Save the original image
        cv2.imwrite(profile_path, img)

        # Generate face encoding
        try:
            # Crop the face from the image
            cropped_face = crop_face(img)
            if cropped_face is None:
                raise HTTPException(status_code=400, detail="No face detected in the image")

            # Generate encoding using the existing function
            face_encoding = generate_encoding(face_image=cropped_face)
            face_encoding_list = face_encoding.tolist()

            # Update or create encoding in database
            existing_encoding = db.query(models.Encoding).filter(models.Encoding.user_id == user.user_id).first()

            if existing_encoding:
                # Update existing encoding
                existing_encoding.encoding = face_encoding_list
                db.commit()
            else:
                # Create new encoding
                # First, create an image entry
                db_image = models.Image(filename=profile_filename, image_url=profile_path, user_id=user.user_id)
                db.add(db_image)
                db.commit()
                db.refresh(db_image)

                # Then create the encoding
                db_encoding = models.Encoding(encoding=face_encoding_list, user_id=user.user_id, image_id=db_image.user_id)
                db.add(db_encoding)
                db.commit()

            # Update the face encoding in Qdrant
            try:
                # First delete all existing embeddings for this user
                try:
                    qdrant_service.delete_face_embedding(str(user.user_id))
                    print(f"[INFO] Deleted all existing embeddings for user {user.username} (ID: {user.user_id}) from Qdrant")
                except Exception as delete_error:
                    print(f"[WARNING] Error deleting existing embeddings: {delete_error}")

                # Then add the new embedding
                success = qdrant_service.add_face_embedding(
                    user_id=str(user.user_id),
                    username=user.username,
                    encoding=face_encoding_list
                )

                if success:
                    print(f"[INFO] Updated face embedding in Qdrant for user {user.username} (ID: {user.user_id})")
                else:
                    print(f"[WARNING] Failed to add face embedding to Qdrant for user {user.username} (ID: {user.user_id})")
            except Exception as qdrant_error:
                print(f"[ERROR] Failed to update face embedding in Qdrant: {qdrant_error}")
                import traceback
                print(f"[ERROR] Traceback: {traceback.format_exc()}")

            # Reload detection cache if running
            if detection is not None:
                detection.reload_encodings_and_users()

            return {"message": "Profile photo and face encoding updated successfully"}
        except Exception as encoding_error:
            # If face encoding fails, still save the image but return a warning
            print(f"Error generating face encoding: {str(encoding_error)}")
            return {
                "message": "Profile photo updated, but face encoding failed. The photo may not be suitable for face recognition.",
                "warning": str(encoding_error)
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating profile photo: {str(e)}")



#WEBHOOK ENDPOINTS############

@router.post("/webhooks", response_model=models.WebhookResponse)
async def create_webhook(
    webhook: models.WebhookCreate,
    db: Session = Depends(get_db)
):
    # Check if webhook URL already exists
    existing_webhook = db.query(models.Webhook).filter(models.Webhook.url == webhook.url).first()
    if existing_webhook:
        raise HTTPException(status_code=400, detail="Webhook URL already exists")

    # Create new webhook
    new_webhook = models.Webhook(
        url=webhook.url,
        description=webhook.description,
        body_template=webhook.body_template
    )

    db.add(new_webhook)
    db.commit()
    db.refresh(new_webhook)

    return new_webhook

@router.get("/webhooks", response_model=List[models.WebhookResponse])
async def get_webhooks(db: Session = Depends(get_db)):
    webhooks = db.query(models.Webhook).all()
    return webhooks

@router.get("/webhooks/{webhook_id}", response_model=models.WebhookResponse)
async def get_webhook(webhook_id: int, db: Session = Depends(get_db)):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return webhook

@router.put("/webhooks/{webhook_id}", response_model=models.WebhookResponse)
async def update_webhook(
    webhook_id: int,
    webhook_update: models.WebhookUpdate,
    db: Session = Depends(get_db)
):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")

    # Update fields if provided
    if webhook_update.url is not None:
        # Check if the new URL already exists for another webhook
        existing_webhook = db.query(models.Webhook).filter(
            models.Webhook.url == webhook_update.url,
            models.Webhook.id != webhook_id
        ).first()
        if existing_webhook:
            raise HTTPException(status_code=400, detail="Webhook URL already exists")
        webhook.url = webhook_update.url

    if webhook_update.description is not None:
        webhook.description = webhook_update.description

    if webhook_update.body_template is not None:
        webhook.body_template = webhook_update.body_template

    if webhook_update.is_active is not None:
        webhook.is_active = webhook_update.is_active

    db.commit()
    db.refresh(webhook)

    return webhook

@router.delete("/webhooks/{webhook_id}")
async def delete_webhook(webhook_id: int, db: Session = Depends(get_db)):
    webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")

    db.delete(webhook)
    db.commit()

    return {"status": "success", "message": "Webhook deleted successfully"}

# # Test webhook endpoint
# @router.post("/webhooks/{webhook_id}/test")
# async def test_webhook(webhook_id: int, db: Session = Depends(get_db)):
#     webhook = db.query(models.Webhook).filter(models.Webhook.id == webhook_id).first()
#     if not webhook:
#         raise HTTPException(status_code=404, detail="Webhook not found")

#     if not webhook.is_active:
#         raise HTTPException(status_code=400, detail="Webhook is not active")

#     # Create a test payload
#     test_payload = {
#         "event": "test",
#         "timestamp": datetime.now(timezone.utc).isoformat(),
#         "data": {
#             "message": "This is a test notification from the face recognition system"
#         }
#     }

#     try:
#         # Send the test payload to the webhook URL
#         async with httpx.AsyncClient() as client:
#             response = await client.post(
#                 webhook.url,
#                 json=test_payload,
#                 timeout=10.0
#             )

#             return {
#                 "status": "success",
#                 "message": "Test webhook sent successfully",
#                 "response_status": response.status_code,
#                 "response_body": response.text
#             }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Failed to send test webhook: {str(e)}")


#WHATSAPP ENDPOINTS############

@router.post("/whatsapp", response_model=models.WhatsAppResponse)
async def create_whatsapp(
    whatsapp: models.WhatsAppCreate,
    db: Session = Depends(get_db)
):
    # Check if phone number already exists
    existing_whatsapp = db.query(models.WhatsApp).filter(models.WhatsApp.phone_number == whatsapp.phone_number).first()
    if existing_whatsapp:
        raise HTTPException(status_code=400, detail="Phone number already exists")

    # Create new WhatsApp contact
    new_whatsapp = models.WhatsApp(
        phone_number=whatsapp.phone_number,
        full_name=whatsapp.full_name
    )

    db.add(new_whatsapp)
    db.commit()
    db.refresh(new_whatsapp)

    return new_whatsapp

@router.get("/whatsapp", response_model=List[models.WhatsAppResponse])
async def get_whatsapp_contacts(db: Session = Depends(get_db)):
    whatsapp_contacts = db.query(models.WhatsApp).all()
    return whatsapp_contacts

@router.get("/whatsapp/{whatsapp_id}", response_model=models.WhatsAppResponse)
async def get_whatsapp_contact(whatsapp_id: int, db: Session = Depends(get_db)):
    whatsapp = db.query(models.WhatsApp).filter(models.WhatsApp.id == whatsapp_id).first()
    if not whatsapp:
        raise HTTPException(status_code=404, detail="WhatsApp contact not found")
    return whatsapp

@router.put("/whatsapp/{whatsapp_id}", response_model=models.WhatsAppResponse)
async def update_whatsapp(
    whatsapp_id: int,
    whatsapp_update: models.WhatsAppUpdate,
    db: Session = Depends(get_db)
):
    whatsapp = db.query(models.WhatsApp).filter(models.WhatsApp.id == whatsapp_id).first()
    if not whatsapp:
        raise HTTPException(status_code=404, detail="WhatsApp contact not found")

    # Update fields if provided
    if whatsapp_update.phone_number is not None:
        # Check if the new phone number already exists for another contact
        existing_whatsapp = db.query(models.WhatsApp).filter(
            models.WhatsApp.phone_number == whatsapp_update.phone_number,
            models.WhatsApp.id != whatsapp_id
        ).first()
        if existing_whatsapp:
            raise HTTPException(status_code=400, detail="Phone number already exists")
        whatsapp.phone_number = whatsapp_update.phone_number

    if whatsapp_update.full_name is not None:
        whatsapp.full_name = whatsapp_update.full_name

    if whatsapp_update.is_active is not None:
        whatsapp.is_active = whatsapp_update.is_active

    db.commit()
    db.refresh(whatsapp)
    return whatsapp

@router.delete("/whatsapp/{whatsapp_id}")
async def delete_whatsapp(whatsapp_id: int, db: Session = Depends(get_db)):
    whatsapp = db.query(models.WhatsApp).filter(models.WhatsApp.id == whatsapp_id).first()
    if not whatsapp:
        raise HTTPException(status_code=404, detail="WhatsApp contact not found")

    db.delete(whatsapp)
    db.commit()

    return {"status": "success", "message": "WhatsApp contact deleted successfully"}


#SMS ENDPOINTS############

@router.post("/sms", response_model=models.SMSResponse)
async def create_sms(
    sms: models.SMSCreate,
    db: Session = Depends(get_db)
):
    # Check if phone number already exists
    existing_sms = db.query(models.SMS).filter(models.SMS.phone_number == sms.phone_number).first()
    if existing_sms:
        raise HTTPException(status_code=400, detail="Phone number already exists")

    # Create new SMS contact
    new_sms = models.SMS(
        phone_number=sms.phone_number,
        full_name=sms.full_name
    )

    db.add(new_sms)
    db.commit()
    db.refresh(new_sms)

    return new_sms

@router.get("/sms", response_model=List[models.SMSResponse])
async def get_sms_contacts(db: Session = Depends(get_db)):
    sms_contacts = db.query(models.SMS).all()
    return sms_contacts

@router.get("/sms/{sms_id}", response_model=models.SMSResponse)
async def get_sms_contact(sms_id: int, db: Session = Depends(get_db)):
    sms = db.query(models.SMS).filter(models.SMS.id == sms_id).first()
    if not sms:
        raise HTTPException(status_code=404, detail="SMS contact not found")
    return sms

@router.put("/sms/{sms_id}", response_model=models.SMSResponse)
async def update_sms(
    sms_id: int,
    sms_update: models.SMSUpdate,
    db: Session = Depends(get_db)
):
    sms = db.query(models.SMS).filter(models.SMS.id == sms_id).first()
    if not sms:
        raise HTTPException(status_code=404, detail="SMS contact not found")

    # Update fields if provided
    if sms_update.phone_number is not None:
        # Check if the new phone number already exists for another contact
        existing_sms = db.query(models.SMS).filter(
            models.SMS.phone_number == sms_update.phone_number,
            models.SMS.id != sms_id
        ).first()
        if existing_sms:
            raise HTTPException(status_code=400, detail="Phone number already exists")
        sms.phone_number = sms_update.phone_number

    if sms_update.full_name is not None:
        sms.full_name = sms_update.full_name

    if sms_update.is_active is not None:
        sms.is_active = sms_update.is_active

    db.commit()
    db.refresh(sms)
    return sms

@router.delete("/sms/{sms_id}")
async def delete_sms(sms_id: int, db: Session = Depends(get_db)):
    sms = db.query(models.SMS).filter(models.SMS.id == sms_id).first()
    if not sms:
        raise HTTPException(status_code=404, detail="SMS contact not found")

    db.delete(sms)
    db.commit()

    return {"status": "success", "message": "SMS contact deleted successfully"}


#EMAIL ENDPOINTS############

@router.post("/email", response_model=models.EmailResponse)
async def create_email(
    email: models.EmailCreate,
    db: Session = Depends(get_db)
):
    # Check if email address already exists
    existing_email = db.query(models.Email).filter(models.Email.email_address == email.email_address).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email address already exists")

    # Create new Email contact
    new_email = models.Email(
        email_address=email.email_address,
        full_name=email.full_name
    )

    db.add(new_email)
    db.commit()
    db.refresh(new_email)

    return new_email

@router.get("/email", response_model=List[models.EmailResponse])
async def get_email_contacts(db: Session = Depends(get_db)):
    email_contacts = db.query(models.Email).all()
    return email_contacts

@router.get("/email/{email_id}", response_model=models.EmailResponse)
async def get_email_contact(email_id: int, db: Session = Depends(get_db)):
    email = db.query(models.Email).filter(models.Email.id == email_id).first()
    if not email:
        raise HTTPException(status_code=404, detail="Email contact not found")
    return email

@router.put("/email/{email_id}", response_model=models.EmailResponse)
async def update_email(
    email_id: int,
    email_update: models.EmailUpdate,
    db: Session = Depends(get_db)
):
    email = db.query(models.Email).filter(models.Email.id == email_id).first()
    if not email:
        raise HTTPException(status_code=404, detail="Email contact not found")

    # Update fields if provided
    if email_update.email_address is not None:
        # Check if the new email address already exists for another contact
        existing_email = db.query(models.Email).filter(
            models.Email.email_address == email_update.email_address,
            models.Email.id != email_id
        ).first()
        if existing_email:
            raise HTTPException(status_code=400, detail="Email address already exists")
        email.email_address = email_update.email_address

    if email_update.full_name is not None:
        email.full_name = email_update.full_name

    if email_update.is_active is not None:
        email.is_active = email_update.is_active

    db.commit()
    db.refresh(email)
    return email

@router.delete("/email/{email_id}")
async def delete_email(email_id: int, db: Session = Depends(get_db)):
    email = db.query(models.Email).filter(models.Email.id == email_id).first()
    if not email:
        raise HTTPException(status_code=404, detail="Email contact not found")

    db.delete(email)
    db.commit()

    return {"status": "success", "message": "Email contact deleted successfully"}








































































