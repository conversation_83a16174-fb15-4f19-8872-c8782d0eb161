from fastapi import APIRouter,WebSocket, Request, HTTPException
from fastapi.templating import Jin<PERSON>2Templates
from app.utils import Crowd_Detection,CameraWebSocketHandler
from fastapi.responses import HTM<PERSON>esponse,JSONResponse
import asyncio
import cv2
import json
from pathlib import Path
from pydantic import BaseModel
from typing import List
from app.alert_system import AlertSystem
import logging



# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('API_Routes')


router = APIRouter()
alert_system = AlertSystem()

templates = Jinja2Templates(directory="templates")

@router.get("/")
async def show_crowd_detection_page(request: Request):
    return templates.TemplateResponse("crowd_detection.html", {"request": request})


# Initialize Crowd_Detection Model
Crowd = Crowd_Detection(model_path='./app/models/yolo11n.pt',conf_value=0.4,alert_system=alert_system)



@router.on_event("shutdown")
def shutdown_event():
    Crowd.stop()
    logger.info("Application shutting down")
    Crowd.stop()
    if alert_system.active:
        alert_system.stop()


# API to start video processing
@router.post("/start-stream")
async def start_stream():
    if not Crowd.running:
        camera_details = load_cameras()
        Crowd.start(camera_details)
        return {"status": "Stream started"}
    return {"status": "Stream already running"}


# API to stop video processing
@router.post("/stop-stream")
async def stop_stream():
    logger.info("Stopping stream")
    if Crowd.running:
        Crowd.stop()
        Crowd.frames = []  # Clear cached frames
        Crowd.total_people_counts = []  # Reset counts
    return {"status": "Stream stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    logger.info("WebSocket connection established")
    try:
        while True:
            if Crowd.running:
                for idx, frame in enumerate(Crowd.frames):
                    if frame:
                        total_count = Crowd.total_people_counts[idx]
                        roi_count = Crowd.roi_count[idx]
                        cam_name = Crowd.camera_name[idx]
                    # await websocket.send_bytes(f"{idx}:{cam_name}:{total_count}:{json.dumps(roi_count)}".encode('utf-8') + b':' + frame)
                        await websocket.send_text(f"{idx}:{cam_name}:{total_count}:{json.dumps(roi_count)}")
                        await websocket.send_bytes(frame)

                    await asyncio.sleep(0.03)
            else:
                await asyncio.sleep(0.5)  # Avoid busy waiting
    except Exception as e:
        print("WebSocket disconnected:", e)
    # finally:
    #     await websocket.close()


#------------------addroi----------------------

# Serve Crowd Detection page
@router.get("/addroi", response_class=HTMLResponse)
async def serve_crowd_detection(request: Request):
    return templates.TemplateResponse("addroi.html", {"request": request})


# Instantiate the handler
camera_handler = CameraWebSocketHandler()

@router.get("/camera/start")
async def start_camera():
    camera_details = load_cameras()
    camera_handler.start(camera_details)
    return {"message": "Camera started"}

@router.get("/camera/stop")
async def stop_camera():
    camera_handler.stop()
    return {"message": "Camera stopped"}

@router.post("/camera/{action}")
async def change_camera(action: str):
    if action not in ["next", "prev"]:
        raise HTTPException(status_code=400, detail="Invalid action. Use 'next' or 'prev'.")

    camera_handler.change_camera(action)
    return {"status": "success", "current_camera_index": camera_handler.current_camera_index, "total_cameras": len(camera_handler.rtsp_url)}

@router.websocket("/addroi")
async def video_stream(websocket: WebSocket):
    await camera_handler.stream_video(websocket)

@router.post("/send-coordinates")
async def receive_coordinates(data: dict):
    # print(f"Received data: {data}")

    camera_name = data.get("cameraName")
    # print(camera_name)
    coordinates = data.get("coordinates")
    
    cameras = load_cameras()

    if not coordinates:
        return JSONResponse(
            content={"status": "error", "message": "No coordinates provided"}, 
            status_code=400
        )
    # Update ROI coordinates
    cameras[camera_name][1] = coordinates
    save_cameras(cameras)
    # print(f"Camera Index: {camera_name}, Coordinates: {coordinates}")
    return {"status": "success", "message": "Coordinates updated successfully"}




#--------------------------------------------------------------ADD CAMERFAA---------------------------------------------------

# Path to the cameras.json file
CAMERAS_FILE_PATH = "app/crowd_detection/cameras.json"


# Load cameras data from the file if it exists
def load_cameras():
    if Path(CAMERAS_FILE_PATH).exists():
        with open(CAMERAS_FILE_PATH, "r") as file:
            return json.load(file)
    return {}

# Save cameras data to the file
def save_cameras(cameras_data):
    with open(CAMERAS_FILE_PATH, "w") as file:
        json.dump(cameras_data, file, indent=4)

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData):
    cameras = load_cameras()
    
    # Check if the camera name already exists
    if camera_data.cameraName in cameras:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists in the values
    for existing_camera, existing_url in cameras.items():
        if existing_url[0] == camera_data.rtspUrl:
            # Return the camera name using the same URL
            return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_camera}"}
            
            


    # Add new camera with empty ROI coordinates
    cameras[camera_data.cameraName.upper()] = [camera_data.rtspUrl,[]]
    
    # Save the updated data
    save_cameras(cameras)
    
    return {"status": "success", "message": "Camera added successfully"}


# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str):
    cameras = load_cameras()

    if camera_name not in cameras:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Remove the camera
    del cameras[camera_name]
    
    # Save the updated camera list
    save_cameras(cameras)
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}


@router.get("/get-cameras")
async def get_cameras():
    return load_cameras()



# Pydantic model for alert settings
class AlertSettings(BaseModel):
    active: bool
    days: List[str]
    startTime: str
    endTime: str
    alertFrequency: int
    recipients: List[str]

# Route to save alert settings
@router.post("/save-alert-settings")
async def save_alert_settings(settings: AlertSettings):
    try:
        alert_system.save_settings(settings.dict())
        # If alerts should be active, start the system
        if settings.active and not alert_system.active:
            alert_system.start()
        # If alerts should be inactive, stop the system
        elif not settings.active and alert_system.active:
            alert_system.stop()
            
        return {"status": "success", "message": "Alert settings saved successfully"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Route to get alert settings
@router.get("/get-alert-settings")
async def get_alert_settings():
    return alert_system.settings

# Route to start alerts
@router.post("/start-alerts")
async def start_alerts():
    if alert_system.start():
        return {"status": "success", "message": "Alerts started"}
    return {"status": "error", "message": "Failed to start alerts"}

# Route to stop alerts
@router.post("/stop-alerts")
async def stop_alerts():
    if alert_system.stop():
        return {"status": "success", "message": "Alerts stopped"}
    return {"status": "error", "message": "Failed to stop alerts"}

# Add this to handle application shutdown
@router.on_event("shutdown")
async def shutdown_event():
    if alert_system.active:
        alert_system.stop()


