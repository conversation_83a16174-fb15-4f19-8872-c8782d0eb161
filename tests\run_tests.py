#!/usr/bin/env python3
"""
Test runner script for face recognition module.
This script runs all tests and generates a comprehensive report.
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner(text):
    """Print a banner with the given text."""
    print("\n" + "="*60)
    print(f" {text}")
    print("="*60)

def print_section(text):
    """Print a section header."""
    print(f"\n--- {text} ---")

def run_command(command, description):
    """Run a command and return the result."""
    print(f"\nRunning: {description}")
    print(f"Command: {command}")

    start_time = time.time()
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        end_time = time.time()

        print(f"Duration: {end_time - start_time:.2f} seconds")
        print(f"Return code: {result.returncode}")

        if result.stdout:
            print("STDOUT:")
            print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        return result.returncode == 0, result
    except Exception as e:
        print(f"Error running command: {e}")
        return False, None

def check_dependencies():
    """Check if required dependencies are installed."""
    print_section("Checking Dependencies")

    required_packages = [
        "pytest",
        "fastapi",
        "sqlalchemy",
        "opencv-python",
        "numpy",
        "scipy"
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ {package} is installed")
        except ImportError:
            print(f"✗ {package} is NOT installed")
            missing_packages.append(package)

    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False

    return True

def run_individual_tests():
    """Run individual test files."""
    print_section("Running Individual Test Files")

    test_files = [
        "test_registration.py",
        "test_face_detection.py",
        "test_api_endpoints.py",
        "test_database_operations.py",
        "test_utils.py"
    ]

    results = {}

    for test_file in test_files:
        if os.path.exists(test_file):
            success, result = run_command(f"python -m pytest {test_file} -v", f"Testing {test_file}")
            results[test_file] = success
        else:
            print(f"Warning: {test_file} not found")
            results[test_file] = False

    return results

def run_all_tests():
    """Run all tests together."""
    print_section("Running All Tests Together")

    success, result = run_command("python -m pytest . -v --tb=short", "All tests")
    return success

def run_coverage_analysis():
    """Run tests with coverage analysis."""
    print_section("Running Coverage Analysis")

    # Check if pytest-cov is available
    try:
        import pytest_cov
        success, result = run_command(
            "python -m pytest . --cov=../face_recognition/app --cov-report=term-missing",
            "Coverage analysis"
        )
        return success
    except ImportError:
        print("pytest-cov not installed. Skipping coverage analysis.")
        print("Install with: pip install pytest-cov")
        return False

def generate_report(individual_results, all_tests_success, coverage_success):
    """Generate a summary report."""
    print_banner("TEST SUMMARY REPORT")

    print("\nIndividual Test Results:")
    for test_file, success in individual_results.items():
        status = "PASS" if success else "FAIL"
        print(f"  {test_file:<30} {status}")

    print(f"\nAll Tests Together: {'PASS' if all_tests_success else 'FAIL'}")
    print(f"Coverage Analysis:  {'PASS' if coverage_success else 'SKIP/FAIL'}")

    # Calculate overall success rate
    passed_tests = sum(1 for success in individual_results.values() if success)
    total_tests = len(individual_results)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

    print(f"\nOverall Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")

    if success_rate == 100 and all_tests_success:
        print("\n🎉 All tests passed! The face recognition module is working correctly.")
    elif success_rate >= 80:
        print("\n⚠️  Most tests passed, but some issues were found. Review the failures above.")
    else:
        print("\n❌ Many tests failed. The face recognition module needs significant fixes.")

def generate_report_extended(quick_tests_success, registration_fixes_success, individual_results, all_tests_success, coverage_success):
    """Generate an extended summary report."""
    print_banner("COMPREHENSIVE TEST SUMMARY REPORT")

    print("\nQuick Tests:")
    print(f"  Smoke Tests:        {'PASS' if quick_tests_success else 'FAIL'}")
    print(f"  Registration Fixes: {'PASS' if registration_fixes_success else 'FAIL'}")

    print("\nIndividual Test Results:")
    for test_file, success in individual_results.items():
        status = "PASS" if success else "FAIL"
        print(f"  {test_file:<30} {status}")

    print(f"\nAll Tests Together: {'PASS' if all_tests_success else 'FAIL'}")
    print(f"Coverage Analysis:  {'PASS' if coverage_success else 'SKIP/FAIL'}")

    # Calculate overall success rate
    all_results = [quick_tests_success, registration_fixes_success] + list(individual_results.values()) + [all_tests_success]
    passed_tests = sum(1 for success in all_results if success)
    total_tests = len(all_results)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

    print(f"\nOverall Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")

    if success_rate == 100:
        print("\n🎉 All tests passed! The face recognition module is working correctly.")
        print("   ✓ Registration functionality is fixed")
        print("   ✓ All core features are working")
        print("   ✓ Database operations are stable")
        print("   ✓ API endpoints are functional")
    elif success_rate >= 80:
        print("\n⚠️  Most tests passed, but some issues were found.")
        print("   Review the failures above and address them.")
        if not quick_tests_success:
            print("   ⚠ Basic functionality issues detected")
        if not registration_fixes_success:
            print("   ⚠ Registration functionality needs attention")
    else:
        print("\n❌ Many tests failed. The face recognition module needs significant fixes.")
        print("   Priority fixes needed:")
        if not quick_tests_success:
            print("   1. Fix basic import and functionality issues")
        if not registration_fixes_success:
            print("   2. Fix registration functionality")
        if not all_tests_success:
            print("   3. Address comprehensive test failures")

def run_quick_tests():
    """Run quick smoke tests."""
    print_section("Running Quick Smoke Tests")

    success, result = run_command("python quick_test.py", "Quick smoke tests")
    return success

def run_registration_fixes():
    """Run registration fix tests."""
    print_section("Running Registration Fix Tests")

    success, result = run_command("python fix_registration.py", "Registration fixes")
    return success

def main():
    """Main function to run all tests."""
    print_banner("Face Recognition Module Test Suite")

    # Change to the tests directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)

    print(f"Working directory: {os.getcwd()}")
    print(f"Python version: {sys.version}")

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot run tests due to missing dependencies.")
        print("Install missing dependencies with: pip install -r requirements.txt")
        return 1

    # Run quick tests first
    quick_tests_success = run_quick_tests()

    # Run registration fix tests
    registration_fixes_success = run_registration_fixes()

    # Run individual tests
    individual_results = run_individual_tests()

    # Run all tests together
    all_tests_success = run_all_tests()

    # Run coverage analysis
    coverage_success = run_coverage_analysis()

    # Generate report
    generate_report_extended(
        quick_tests_success,
        registration_fixes_success,
        individual_results,
        all_tests_success,
        coverage_success
    )

    # Return appropriate exit code
    overall_success = (
        quick_tests_success and
        registration_fixes_success and
        all(individual_results.values()) and
        all_tests_success
    )

    return 0 if overall_success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
