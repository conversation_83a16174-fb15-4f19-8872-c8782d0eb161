<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Face Recognition System</title>
  <link rel="stylesheet" href="{{ url_for('static', path='face_recognition.css') }}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div>
        <div class="logo">
          <img src="/static/logo.png" alt="Logo">
        </div>
        <div class="buttons">
          <div class="btn-group">
            <div class="btn-group-title">Monitoring</div>
            <button class="menu-btn" id="startStreamBtn"><i class="fas fa-play-circle"></i> Start Monitoring</button>
            <button class="menu-btn" id="stopStreamBtn"><i class="fas fa-stop-circle"></i> Stop Monitoring</button>
          </div>

          <div class="btn-group">
            <div class="btn-group-title">Reports</div>
            <button class="menu-btn" id="attendanceBtn"><i class="fas fa-clipboard-list"></i> Attendance Logs</button>
          </div>

          <!-- Always show the admin button -->
          <div class="btn-group">
            <div class="btn-group-title">Administration</div>
            <button id="adminBtn" class="btn btn-primary">
              <i class="fas fa-user-shield"></i> Admin Panel
            </button>
          </div>
        </div>
      </div>
      <button class="homepage-btn" id="homepageBtn"><i class="fas fa-home"></i> Go To Homepage</button>
    </div>

    <div class="main">
      <div class="app-header">
        <div class="app-title">Face Recognition System</div>
        <div class="status-indicator">
          <div class="status-dot inactive" id="statusDot"></div>
          <span id="statusText">System Inactive</span>
        </div>
      </div>

      <div class="webcam-section" id="webcamSection">
        <h2>Camera Feeds</h2>
        <div class="video-grid" id="feeds">
          <!-- Video feeds will be added dynamically -->
        </div>
      </div>
    </div>

    <div class="live-observations">
      <h2>Live Observations</h2>
      <ul id="observationsList">
        <!-- Observations will be dynamically added here -->
      </ul>
    </div>
    
  </div>

  <!-- Camera management moved to admin panel -->

  <!-- Confirmation Dialog -->
  <div id="confirmDialog" class="confirm-dialog">
    <h3>Confirm Action</h3>
    <p id="confirmMessage">Are you sure you want to perform this action?</p>
    <div class="confirm-dialog-buttons">
      <button class="confirm-yes" id="confirmYes">Yes</button>
      <button class="confirm-no" id="confirmNo">No</button>
    </div>
  </div>

  <script>
    // DOM Elements
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const feedsDiv = document.getElementById("feeds");
    const observationsList = document.getElementById("observationsList");
    const homepageBtn = document.getElementById("homepageBtn");
    const statusDot = document.getElementById("statusDot");
    const statusText = document.getElementById("statusText");
    const confirmDialog = document.getElementById("confirmDialog");
    const confirmYes = document.getElementById("confirmYes");
    const confirmNo = document.getElementById("confirmNo");
    const confirmMessage = document.getElementById("confirmMessage");

    // State variables
    let websocket = null;
    let showList = false;
    let activeFeeds = new Map(); // Use Map instead of Set to track camera details
    let isStreaming = false;
    let pendingAction = null;

    // Event listener for the "Go to Homepage" button
    homepageBtn.addEventListener("click", () => {
      window.location.href = "/";
    });

    // Function to update the status indicator
    function updateStatus(active) {
      if (active) {
        statusDot.classList.remove("inactive");
        statusDot.classList.add("active");
        statusText.textContent = "System Active";
        startStreamBtn.classList.add("active");
        stopStreamBtn.classList.remove("active");
      } else {
        statusDot.classList.remove("active");
        statusDot.classList.add("inactive");
        statusText.textContent = "System Inactive";
        startStreamBtn.classList.remove("active");
        stopStreamBtn.classList.add("active");
      }
    }

    // Function to show confirmation dialog
    function showConfirmDialog(message, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.style.display = "block";

      // Store the callback for later use
      pendingAction = onConfirm;

      // Set up event listeners for the buttons
      confirmYes.onclick = () => {
        confirmDialog.style.display = "none";
        if (pendingAction) pendingAction();
        pendingAction = null;
      };

      confirmNo.onclick = () => {
        confirmDialog.style.display = "none";
        pendingAction = null;
      };
    }

    // Toast notification fix
function showToast(message, type = "info") {
  // Create toast element if it doesn't exist
  let toast = document.getElementById("toast");
  if (!toast) {
    toast = document.createElement("div");
    toast.id = "toast";
    toast.className = "toast";
    document.body.appendChild(toast);
    console.log("Toast element created since it wasn't found");
  }

  // Clear any existing timeout
  if (window.toastTimeout) {
    clearTimeout(window.toastTimeout);
    toast.classList.remove("show");
  }

  // Force a reflow to ensure transition works
  void toast.offsetWidth;

  // Set toast content and style
  toast.textContent = message;
  toast.className = `toast ${type}`;

  // Make sure z-index is high enough
  toast.style.zIndex = "9999";
  
  // Show the toast
  toast.classList.add("show");
  console.log("Toast should be visible now with message:", message);

  // Hide after 5 seconds
  window.toastTimeout = setTimeout(() => {
    toast.classList.remove("show");
    console.log("Toast hidden");
  }, 5000);
}

  
    // Function to start the video stream
    async function startStream() {
      try {
        // First make sure any existing streams are properly stopped
        if (isStreaming) {
          await stopStream(false); // Silent stop - don't show messages
        }

        showToast("Starting monitoring system...", "info");

        // First get the current threshold setting
        const settingsResponse = await fetch('/face_recognition/get-settings');
        let threshold = 0.7; // Default value

        if (settingsResponse.ok) {
          const settings = await settingsResponse.json();
          threshold = settings.face_threshold;
          console.log(`Using threshold value: ${threshold}`);
        } else {
          console.warn("Could not get threshold settings, using default value");
        }

        // Start the detection with the current threshold
        const response = await fetch('/face_recognition/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const result = await response.json();
          console.log(`Stream started successfully with threshold: ${result.threshold}`);
          isStreaming = true;
          updateStatus(true);
          startWebSocketFeed();
          showToast("Monitoring system started successfully", "success");
        } else {
          console.error("Failed to start stream");
          showToast("Failed to start monitoring system", "error");
        }
      } catch (error) {
        console.error("Error starting stream:", error);
        showToast(`Error: ${error.message}`, "error");
        updateStatus(false);
      }
    }

// Test toast function with inline styles to diagnose CSS issues
function createTestToast() {
  // Create a test toast with inline styles to verify positioning
  const testToast = document.createElement('div');
  testToast.textContent = "Test Toast Message";
  testToast.style.position = "fixed";
  testToast.style.bottom = "30px";
  testToast.style.left = "50%";
  testToast.style.transform = "translateX(-50%)";
  testToast.style.backgroundColor = "#3498db"; // --secondary-color
  testToast.style.color = "white";
  testToast.style.padding = "12px 25px";
  testToast.style.borderRadius = "8px";
  testToast.style.zIndex = "10000";
  testToast.style.fontWeight = "500";
  testToast.style.boxShadow = "0 5px 15px rgba(0, 0, 0, 0.2)";
  testToast.style.opacity = "1";
  testToast.style.minWidth = "250px";
  testToast.style.textAlign = "center";
  
  document.body.appendChild(testToast);
  
  // Remove after 5 seconds
  setTimeout(() => {
    document.body.removeChild(testToast);
  }, 5000);
}

// Improved showToast function that matches the CSS styling
function showToast(message, type = "info") {
  console.log(`Showing toast: ${message} (${type})`);
  
  // Get or create the toast element
  let toast = document.getElementById("toast");
  if (!toast) {
    toast = document.createElement("div");
    toast.id = "toast";
    document.body.appendChild(toast);
    console.log("Toast element created");
  }
  
  // Clear any existing timeout
  if (window.toastTimeout) {
    clearTimeout(window.toastTimeout);
  }
  
  // Remove any existing classes and set new ones
  toast.className = `toast ${type}`;
  toast.textContent = message;
  
  // Apply critical inline styles as fallback (matching exactly with CSS)
  toast.style.position = "fixed";
  toast.style.bottom = "30px";
  toast.style.left = "50%";
  toast.style.transform = "translateX(-50%)";
  toast.style.backgroundColor = "var(--primary-color)";  // Default color
  toast.style.color = "white";
  toast.style.padding = "12px 25px";
  toast.style.borderRadius = "8px";
  toast.style.zIndex = "9999";
  toast.style.fontWeight = "500";
  toast.style.boxShadow = "0 5px 15px rgba(0, 0, 0, 0.2)";
  toast.style.opacity = "0";
  toast.style.transition = "opacity 0.3s, transform 0.3s";
  toast.style.pointerEvents = "none";
  toast.style.width = "auto";
  toast.style.minWidth = "250px";
  toast.style.maxWidth = "80%";
  toast.style.textAlign = "center";
  toast.style.marginLeft = "0";
  
  // Set background color based on type using CSS variables
  switch(type) {
    case "success":
      toast.style.backgroundColor = "var(--success-color)";
      break;
    case "error":
      toast.style.backgroundColor = "var(--danger-color)";
      break;
    case "info":
      toast.style.backgroundColor = "var(--secondary-color)";
      break;
  }
  
  // Show the toast with a small delay to ensure styles are applied
  setTimeout(() => {
    toast.classList.add("show");
    toast.style.opacity = "1";
    toast.style.transform = "translateX(-50%) translateY(-10px)";
    console.log("Toast should be visible now");
  }, 10);
  
  // Hide after 5 seconds
  window.toastTimeout = setTimeout(() => {
    toast.classList.remove("show");
    toast.style.opacity = "0";
    toast.style.transform = "translateX(-50%)";
    console.log("Toast hidden");
  }, 5000);
}
    // Function to close the WebSocket connection without stopping the backend monitoring
    function closeWebSocketOnly() {
      if (websocket) {
        // Use a proper WebSocket closing sequence
        const ws = websocket;
        websocket = null; // Clear reference first

        ws.onclose = null; // Remove the onclose handler to avoid triggering it when we close
        ws.onerror = null; // Remove error handler
        ws.onmessage = null; // Remove message handler

        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close(1000, "Deliberate close"); // Normal closure
        }
      }

      // Clean up UI elements
      feedsDiv.innerHTML = ""; // Clear video feeds
      observationsList.innerHTML = ""; // Clear observations
      activeFeeds.clear(); // Clear tracked feeds
    }

    // Function to stop the video stream
    async function stopStream(showMessages = true) {
      try {
        // First close the WebSocket connection
        closeWebSocketOnly();

        // Only call the backend stop if we think streaming is active
        if (isStreaming) {
          if (showMessages) showToast("Stopping monitoring system...", "info");

          const response = await fetch('/face_recognition/stop', { method: 'POST' });
          if (response.ok) {
            if (showMessages) {
              console.log("Stream stopped successfully");
              showToast("Monitoring system stopped successfully", "success");
            }
          } else if (showMessages) {
            console.error("Failed to stop stream on server");
            showToast("Failed to stop monitoring system", "error");
          }
        }

        // Update state
        isStreaming = false;
        updateStatus(false);

        // Clear the localStorage state
        localStorage.removeItem('faceRecognitionActive');

      } catch (error) {
        if (showMessages) {
          console.error("Error stopping stream:", error);
          showToast(`Error: ${error.message}`, "error");
        }
        isStreaming = false;
        updateStatus(false);
      }
    }

    // Function to start the WebSocket connection and handle incoming frames
    function startWebSocketFeed() {
      // Use window.location to construct WebSocket URL dynamically
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/face_recognition/ws`;

      console.log(`Connecting to WebSocket at: ${wsUrl}`);
      websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log("WebSocket connection established.");
        showToast("Camera connection established", "success");
      };

      websocket.onmessage = (event) => {
        if (typeof event.data === 'string') {
          // Parse the message
          const message = event.data;
          const parts = message.split(':', 3); // Split only on the first two delimiters

          if (parts.length < 3) {
            console.error("Invalid message format:", message);
            return;
          }

          const cameraId = parts[0];
          const cameraName = parts[1];
          const data = parts.slice(2).join(':'); // In case the base64 data contains colons

          // Store camera information in the Map
          activeFeeds.set(cameraId, { name: cameraName, lastUpdate: Date.now() });

          // Update video feed
          let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
          if (!cameraContainer) {
            // Create a new container for this camera
            cameraContainer = document.createElement("div");
            cameraContainer.id = `camera-container-${cameraId}`;
            cameraContainer.classList.add("camera-container");

            // Add camera label
            const cameraLabel = document.createElement("h3");
            cameraLabel.textContent = cameraName;
            cameraContainer.appendChild(cameraLabel);

            // Add the video feed
            const imgElem = document.createElement("img");
            imgElem.id = `feed-${cameraId}`;
            imgElem.alt = "Processed Video Feed";
            cameraContainer.appendChild(imgElem);

            // Append the camera container to the feeds
            feedsDiv.appendChild(cameraContainer);
          }

          // Update the video feed image
          const imgElem = document.querySelector(`#feed-${cameraId}`);
          if (imgElem) {
            imgElem.src = `data:image/jpeg;base64,${data}`;
            imgElem.onerror = function() {
              console.error(`Error loading image for camera ${cameraName}`);
              this.src = ''; // Clear the invalid image
            };
          }

          // Update live observations
          let observationElem = document.querySelector(`#observation-${cameraId}`);
          if (!observationElem) {
            // Create a new list item if it doesn't exist
            observationElem = document.createElement("li");
            observationElem.id = `observation-${cameraId}`;
            observationsList.appendChild(observationElem);
          }

          // Format observations with timestamp
          const now = new Date();
          const timeString = now.toLocaleTimeString();
          observationElem.innerHTML = `
            <strong>${cameraName}</strong>
            <div class="observation-time">${timeString}</div>
            <div class="observation-status">
              <i class="fas fa-check-circle"></i> Face Detection Active
            </div>
          `;
        }
      };

      websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
        showToast("Connection error. Please refresh the page.", "error");
        isStreaming = false;
        updateStatus(false);
      };

      websocket.onclose = (event) => {
        console.log(`WebSocket connection closed: Code ${event.code}`);
        if (isStreaming && event.code !== 1000) {
          // Not a normal closure and we think we should be streaming
          showToast("Connection closed unexpectedly. Please refresh the page.", "error");
          isStreaming = false;
          updateStatus(false);
        }
      };
    }

    // Event listeners for start and stop buttons
    startStreamBtn.addEventListener('click', () => {
      if (!isStreaming) {
        startStream();
      } else {
        showToast("Monitoring system is already running", "info");
      }
    });

    stopStreamBtn.addEventListener('click', () => {
      if (isStreaming) {
        showConfirmDialog("Are you sure you want to stop the monitoring system?", () => {
          stopStream(true);
        });
      } else {
        showToast("Monitoring system is already stopped", "info");
      }
    });

    // Store monitoring state in localStorage when navigating to other pages
    // We'll only stop the WebSocket connection but keep the backend monitoring running
    window.addEventListener('beforeunload', (event) => {
      // Check if this is a page navigation or browser close
      if (event.clientY < 0 ||
          event.altKey ||
          event.ctrlKey ||
          event.metaKey ||
          event.shiftKey) {
        // This is likely a browser close, so stop the stream completely
        stopStream(false);
      } else {
        // This is likely a navigation, so store the state and just close the WebSocket
        localStorage.setItem('faceRecognitionActive', isStreaming.toString());

        // Close only the WebSocket connection without stopping the backend monitoring
        closeWebSocketOnly();
      }
    });

    // Camera management and user registration functionality moved to admin panel

    // Event listener for the "Logs" button
    const logBtn = document.getElementById("attendanceBtn");
    logBtn.addEventListener("click", () => {
      // Store the current monitoring state before navigating
      localStorage.setItem('faceRecognitionActive', isStreaming.toString());

      // Close only the WebSocket connection without stopping the backend monitoring
      if (isStreaming) {
        closeWebSocketOnly();
      }

      window.location.href = "/face_recognition/attendance";
    });

    // Event listener for the "Admin" button
    const adminBtn = document.getElementById("adminBtn");
    adminBtn.addEventListener("click", () => {
      // Store the current monitoring state before navigating
      localStorage.setItem('faceRecognitionActive', isStreaming.toString());

      // Close only the WebSocket connection without stopping the backend monitoring
      if (isStreaming) {
        closeWebSocketOnly();
      }

      window.location.href = "/face_recognition/admin";
    });

    // Toast styling is now in the CSS file
    // Add any additional dynamic styles here if needed
    const style = document.createElement('style');
    style.textContent = `
      .observation-time {
        font-size: 12px;
        color: #666;
        margin: 5px 0;
      }

      .observation-status {
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .observation-status i {
        color: var(--success-color);
      }

      .camera-url {
        font-size: 12px;
        color: #666;
        word-break: break-all;
      }
    `;
    document.head.appendChild(style);

    // Initialize the page
    updateStatus(false);

    // Check if monitoring was active when navigating away from the page
    document.addEventListener('DOMContentLoaded', () => {
      const wasActive = localStorage.getItem('faceRecognitionActive') === 'true';
      if (wasActive) {
        console.log("Resuming monitoring from previous session");
        // Start the stream automatically
        startStream();
      }
    });
  </script>

  <!-- Toast notification container - positioned outside the main layout -->
  <div id="toast" class="toast"></div>
</body>
</html>
