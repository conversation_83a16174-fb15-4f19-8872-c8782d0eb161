// DOM Elements
const chooseFileBtn = document.getElementById('choose-file-btn');
const uploadInput = document.getElementById('upload-input');
const capturedImageInput = document.getElementById('captured_image');
const modal = document.getElementById('modal');
const uploadBtn = document.getElementById('upload-btn');
const captureBtn = document.getElementById('capture-btn');
const closeModalBtn = document.getElementById('close-modal-btn');
const cameraContainer = document.getElementById('camera-container');
const video = document.getElementById('video');
const captureImageBtn = document.getElementById('capture-image-btn');
const canvas = document.getElementById('canvas');
const imagePreview = document.getElementById('image-preview');
const closeCameraBtn = document.getElementById('close-camera-btn');
const form = document.querySelector('form');  // Get the form element
const fileStatus = document.getElementById('file-status'); // File status message element

// Open modal on "Choose File" click
chooseFileBtn.addEventListener('click', () => {
    modal.style.display = 'flex';
});

// Close modal
closeModalBtn.addEventListener('click', () => {
    modal.style.display = 'none';
});

// Upload from Device
uploadBtn.addEventListener('click', () => {
    uploadInput.click(); // Trigger the hidden file input
    modal.style.display = 'none';
});

// Update file status text when file is selected
uploadInput.addEventListener('change', () => {
    const file = uploadInput.files[0];
    if (file) {
        fileStatus.textContent = "Image successfully uploaded";
    } else {
        fileStatus.textContent = "No file chosen";
    }
});

// Take Picture with Camera
captureBtn.addEventListener('click', async () => {
    modal.style.display = 'none';

    console.log("Opening camera...");

    // Show camera container immediately without animation first
    cameraContainer.style.display = 'block';
    cameraContainer.style.opacity = '1';
    cameraContainer.style.visibility = 'visible';

    try {
        console.log("Requesting camera access...");
        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                facingMode: "user"
            }
        });
        console.log("Camera access granted");
        video.srcObject = stream;

        // Make sure video is visible
        video.style.display = 'block';

        // Log when video is ready
        video.onloadedmetadata = () => {
            console.log("Video metadata loaded, dimensions:", video.videoWidth, "x", video.videoHeight);
        };
    } catch (err) {
        // Create toast notification for error
        showToast('Could not access the camera. Please check permissions.', 'error');
        console.error("Camera error:", err);
        hideCamera();
    }
});

// Function to hide camera with animation
function hideCamera() {
    console.log("Hiding camera...");
    // Directly set styles instead of using classes
    cameraContainer.style.opacity = '0';
    cameraContainer.style.visibility = 'hidden';

    // Wait for transition to complete before hiding
    setTimeout(() => {
        cameraContainer.style.display = 'none';
        console.log("Camera hidden");
    }, 300); // Match this with the transition duration
}

// Function to show toast notification
function showToast(message, type = 'info') {
    // Create toast element if it doesn't exist
    let toast = document.getElementById('toast');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        document.body.appendChild(toast);

        // Add toast styling
        toast.style.position = 'fixed';
        toast.style.bottom = '30px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.padding = '12px 25px';
        toast.style.borderRadius = '8px';
        toast.style.zIndex = '2100';
        toast.style.fontWeight = '500';
        toast.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s, transform 0.3s';
        toast.style.color = 'white';
    }

    // Set color based on type
    if (type === 'success') {
        toast.style.backgroundColor = '#2ecc71';
    } else if (type === 'error') {
        toast.style.backgroundColor = '#e74c3c';
    } else {
        toast.style.backgroundColor = '#3498db';
    }

    toast.textContent = message;

    // Show the toast
    toast.style.opacity = '1';
    toast.style.transform = 'translate(-50%, -10px)';

    // Hide after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(-50%)';
    }, 3000);
}

// Capture Image
captureImageBtn.addEventListener('click', () => {
    try {
        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        const dataURL = canvas.toDataURL('image/png');
        imagePreview.src = dataURL;
        imagePreview.style.display = 'block';

        // Convert the base64 string to a Blob
        const byteString = atob(dataURL.split(',')[1]);
        const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
        }
        const blob = new Blob([ab], { type: mimeString });
        const file = new File([blob], 'captured-image.png', { type: mimeString });

        // Now append the file to the form input
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        uploadInput.files = dataTransfer.files;

        // Update file status text
        fileStatus.textContent = "Image successfully captured";

        // Automatically set the base64 value in the hidden input for reference (if needed)
        capturedImageInput.value = dataURL;

        // Stop the video stream
        video.srcObject.getTracks().forEach(track => track.stop());

        // Hide camera with animation
        hideCamera();

        // Show success message
        showToast('Image captured successfully!', 'success');
    } catch (err) {
        console.error('Error capturing image:', err);
        showToast('Failed to capture image. Please try again.', 'error');
    }
});

// Close Camera
closeCameraBtn.addEventListener('click', () => {
    // Stop all video tracks
    if (video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }

    // Hide camera with animation
    hideCamera();
});

// Optionally, you can also add a custom submit handler for form validation before submitting it
form.addEventListener('submit', (e) => {
    // You could do additional checks here before form submission
    console.log("Form is being submitted with image:", uploadInput.files[0]);
});