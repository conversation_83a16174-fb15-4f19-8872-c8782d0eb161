from deep_sort_realtime.deepsort_tracker import DeepSort
import cv2
from ultralytics import YOLO
import cv2
import time
import threading
import queue
import time
import base64
import cv2
import json
import datetime
from concurrent.futures import ThreadPoolExecutor
from fastapi import WebSocket, WebSocketDisconnect
import datetime
from huggingface_hub import hf_hub_download
from app.utils import generate_deepface_encoding, get_available_devices, get_username_from_id, calculate_cosine_similarity
from app.models import Attendance, Encoding

# class FaceTracker:
#     def __init__(self):
#         self.deepsort = DeepSort(max_age=3, nn_budget=50, embedder_gpu=True)

#     def start(self, urls):
#         self.cap_devices = []
#         for url in urls:
#             cap = cv2.VideoCapture(url)
#             if cap.isOpened():
#                 self.cap_devices.append(cap)
#             else:
#                 break

#     def track_faces(self, detections, frame, encodings_cache = None, users_cache = None):
#         trackers = self.deepsort.update_tracks(raw_detections=detections, frame=frame)
#         result = []
#         tracked_faces = {}

#         for tracker in trackers:
#             track_id = tracker['id']
#             x1, y1, x2, y2 = tracker.to_ltrb()
#             bbox = (x1, y1, x2, y2)
#             name = "Unknown"

#             if track_id in tracked_faces:
#                 name = tracked_faces[track_id].get('name', 'Unknown')

#             result.append({'id': track_id, 'bbox': bbox, 'name':name})

#         return result


class Tracker:
    def __init__(self):
        self.deepsort = DeepSort(max_age=7, nn_budget=100)
        self.tracked_faces = {}

    def track(self, detections, frame):
        trackers = self.deepsort.update_tracks(raw_detections=detections, frame=frame)
        return trackers

class CameraProcessor:
    def __init__(self, camera_index, frame_queue, websocket, model, tracker, face_recognition, db):
        self.camera_index = camera_index
        self.frame_queue = frame_queue
        self.websocket = websocket
        self.model = model
        self.tracker = tracker
        self.face_recognition = face_recognition
        self.db = db
        self.retry_limit = 3
        self.retry_tracker = {}

    def process_frame(self):
        frame_count = 0
        start_time = time.time()

        while True:
            frame, timestamp = self.frame_queue.get()
            frame_count += 1
            fps = frame_count / (time.time() - start_time)

            detections = []
            detection_class = "face"
            results = self.model(frame, stream=True)

            for result in results:
                for faces in result.boxes:
                    x1, y1, x2, y2 = faces.xyxy[0].cpu().numpy()
                    confidence = faces.conf[0].cpu().item()
                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                    x = int(x1)
                    y = int(y1)
                    w = int(x2 - x1)
                    h = int(y2 - y1)
                    detections.append(([x, y, w, h], confidence, detection_class))

            trackers = self.tracker.track(detections, frame)

            for tracker in trackers:
                track_id = tracker.track_id
                x1, y1, x2, y2 = tracker.to_ltrb()
                if track_id not in self.tracker.tracked_faces:
                    # Extract face image from frame
                    face_image = frame[int(y1):int(y2), int(x1):int(x2)]

                    # Generate encoding with face alignment (handled inside generate_deepface_encoding)
                    face_encoding = generate_deepface_encoding(face_image)
                    self.tracker.tracked_faces[track_id] = {'encoding': face_encoding, 'last_seen': time.time()}
                    self.retry_tracker[track_id] = 0
                else:
                    self.tracker.tracked_faces[track_id]['last_seen'] = time.time()

                face_encoding = self.tracker.tracked_faces[track_id]['encoding']
                track_name, color = self.face_recognition.recognize_face(face_encoding, self.db)

                cv2.putText(frame, f"{track_name}", (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 50])
            if _:
                encoded_frame = base64.b64encode(buffer.tobytes()).decode('utf-8')
                message = {'camera_id': self.camera_index, 'frame': encoded_frame}
                self.websocket.send_text(json.dumps(message))


class WebSocketHandler:
    def __init__(self, websocket: WebSocket, db, available_cameras):
        self.websocket = websocket
        self.db = db
        self.available_cameras = available_cameras
        self.executor = ThreadPoolExecutor(max_workers=len(available_cameras))
        self.frame_queues = [queue.Queue(maxsize=5) for _ in available_cameras]
        self.video_caps = [cv2.VideoCapture(i) for i in range(len(available_cameras))]
        self.model_path = "path_to_model.pt"  # Replace with the actual path
        self.model = YOLO(self.model_path)
        self.face_recognition = FaceRecognition(
            encodings_cache=db.query(models.Encoding).all(),
            users_cache=db.query(models.User).all()
        )
        self.tracker = Tracker()

    def start_camera_threads(self):
        for i, cap in enumerate(self.video_caps):
            camera_processor = CameraProcessor(
                camera_index=i,
                frame_queue=self.frame_queues[i],
                websocket=self.websocket,
                model=self.model,
                tracker=self.tracker,
                face_recognition=self.face_recognition,
                db=self.db
            )
            self.executor.submit(self.producer, i, cap, self.frame_queues[i])
            self.executor.submit(camera_processor.process_frame)

    @staticmethod
    def producer(camera_index, cap, frame_queue):
        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                break
            timestamp = time.time()
            frame_queue.put((frame, timestamp))
            time.sleep(0.06)

    def cleanup(self):
        for cap in self.video_caps:
            cap.release()
