"""
Tests for utility functions in the face recognition module.
"""
import pytest
import cv2
import numpy as np
import os
import sys
from unittest.mock import patch, MagicMock

# Import the modules to test
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

from app.utils import (
    generate_encoding, 
    generate_deepface_encoding, 
    align_face, 
    crop_face, 
    calculate_cosine_similarity,
    load_cameras,
    save_cameras,
    get_available_devices,
    get_username_from_id
)


class TestUtils:
    """Test class for utility functions."""
    
    def test_calculate_cosine_similarity_identical(self):
        """Test cosine similarity with identical vectors."""
        vector1 = np.random.rand(512)
        vector2 = vector1.copy()
        
        similarity = calculate_cosine_similarity(vector1, vector2)
        assert similarity == pytest.approx(1.0, abs=1e-6)
    
    def test_calculate_cosine_similarity_orthogonal(self):
        """Test cosine similarity with orthogonal vectors."""
        vector1 = np.array([1, 0, 0, 0])
        vector2 = np.array([0, 1, 0, 0])
        
        similarity = calculate_cosine_similarity(vector1, vector2)
        assert similarity == pytest.approx(0.0, abs=1e-6)
    
    def test_calculate_cosine_similarity_opposite(self):
        """Test cosine similarity with opposite vectors."""
        vector1 = np.array([1, 1, 1, 1])
        vector2 = np.array([-1, -1, -1, -1])
        
        similarity = calculate_cosine_similarity(vector1, vector2)
        assert similarity == pytest.approx(-1.0, abs=1e-6)
    
    def test_calculate_cosine_similarity_with_lists(self):
        """Test cosine similarity with list inputs."""
        vector1 = [1, 0, 0, 0]
        vector2 = [0, 1, 0, 0]
        
        similarity = calculate_cosine_similarity(vector1, vector2)
        assert similarity == pytest.approx(0.0, abs=1e-6)
    
    def test_calculate_cosine_similarity_random_vectors(self):
        """Test cosine similarity with random vectors."""
        vector1 = np.random.rand(512)
        vector2 = np.random.rand(512)
        
        similarity = calculate_cosine_similarity(vector1, vector2)
        assert -1.0 <= similarity <= 1.0
    
    def test_crop_face_with_simple_image(self, test_image):
        """Test crop_face function with a simple test image."""
        result = crop_face(test_image)
        # The function might return None if no face is detected in our simple test image
        assert result is None or isinstance(result, np.ndarray)
    
    def test_crop_face_with_none_input(self):
        """Test crop_face function with None input."""
        result = crop_face(None)
        assert result is None
    
    def test_crop_face_with_empty_array(self):
        """Test crop_face function with empty array."""
        empty_img = np.array([])
        result = crop_face(empty_img)
        assert result is None
    
    def test_align_face_function(self, test_image):
        """Test align_face function."""
        # align_face is currently just an alias for crop_face
        result = align_face(test_image)
        assert result is None or isinstance(result, np.ndarray)
    
    def test_generate_encoding_with_valid_image(self):
        """Test generate_encoding function with a valid face image."""
        # Create a more realistic face-like image
        face_img = np.random.randint(0, 255, (160, 160, 3), dtype=np.uint8)
        
        try:
            encoding = generate_encoding(face_img)
            if encoding is not None:
                assert isinstance(encoding, np.ndarray)
                assert len(encoding.shape) == 1  # Should be 1D array
                assert encoding.shape[0] > 0  # Should have some dimensions
        except Exception as e:
            # If encoding fails due to no face detected, that's acceptable
            assert "face" in str(e).lower() or "encoding" in str(e).lower() or "error" in str(e).lower()
    
    def test_generate_encoding_with_none_input(self):
        """Test generate_encoding function with None input."""
        try:
            result = generate_encoding(None)
            assert result is None
        except Exception:
            # It's acceptable for the function to raise an exception with invalid input
            pass
    
    def test_generate_deepface_encoding_alias(self):
        """Test that generate_deepface_encoding is an alias for generate_encoding."""
        face_img = np.random.randint(0, 255, (160, 160, 3), dtype=np.uint8)
        
        try:
            encoding1 = generate_encoding(face_img)
            encoding2 = generate_deepface_encoding(face_img)
            
            # Both should behave the same way
            if encoding1 is None:
                assert encoding2 is None
            else:
                assert isinstance(encoding2, np.ndarray)
        except Exception:
            # Both functions should fail in the same way
            pass
    
    @patch('app.utils.database.SessionLocal')
    def test_load_cameras_with_mock_db(self, mock_session_local):
        """Test load_cameras function with mocked database."""
        # Mock database session
        mock_session = MagicMock()
        mock_session_local.return_value = mock_session
        
        # Mock camera objects
        mock_camera1 = MagicMock()
        mock_camera1.name = "Camera1"
        mock_camera1.rtsp_url = "rtsp://camera1.com"
        
        mock_camera2 = MagicMock()
        mock_camera2.name = "Camera2"
        mock_camera2.rtsp_url = "rtsp://camera2.com"
        
        mock_session.query.return_value.all.return_value = [mock_camera1, mock_camera2]
        
        result = load_cameras()
        
        expected = {
            "Camera1": ["rtsp://camera1.com"],
            "Camera2": ["rtsp://camera2.com"]
        }
        
        assert result == expected
        mock_session.close.assert_called_once()
    
    def test_load_cameras_with_provided_session(self, test_db_session, test_camera):
        """Test load_cameras function with provided database session."""
        result = load_cameras(test_db_session)
        
        assert isinstance(result, dict)
        assert test_camera.name in result
        assert result[test_camera.name] == [test_camera.rtsp_url]
    
    def test_save_cameras_deprecation_warning(self, capsys):
        """Test that save_cameras function shows deprecation warning."""
        save_cameras({"test": ["url"]})
        
        captured = capsys.readouterr()
        assert "deprecated" in captured.out.lower()
    
    def test_get_available_devices(self):
        """Test get_available_devices function."""
        try:
            devices = get_available_devices()
            assert isinstance(devices, list)
            # Should contain at least CPU
            device_names = [str(device) for device in devices]
            assert any("cpu" in device.lower() for device in device_names)
        except Exception:
            # If CUDA/GPU detection fails, that's acceptable
            pass
    
    def test_get_username_from_id_with_valid_cache(self):
        """Test get_username_from_id function with valid cache."""
        users_cache = {
            1: "user1",
            2: "user2",
            3: "user3"
        }
        
        assert get_username_from_id(1, users_cache) == "user1"
        assert get_username_from_id(2, users_cache) == "user2"
        assert get_username_from_id(3, users_cache) == "user3"
    
    def test_get_username_from_id_with_invalid_id(self):
        """Test get_username_from_id function with invalid user ID."""
        users_cache = {
            1: "user1",
            2: "user2"
        }
        
        result = get_username_from_id(999, users_cache)
        assert result is None or result == "Unknown"
    
    def test_get_username_from_id_with_empty_cache(self):
        """Test get_username_from_id function with empty cache."""
        users_cache = {}
        
        result = get_username_from_id(1, users_cache)
        assert result is None or result == "Unknown"
    
    def test_encoding_consistency(self):
        """Test that encoding generation is reasonably consistent."""
        # Create a simple test image
        img = np.ones((160, 160, 3), dtype=np.uint8) * 128  # Gray image
        
        try:
            encoding1 = generate_encoding(img)
            encoding2 = generate_encoding(img)
            
            if encoding1 is not None and encoding2 is not None:
                # Encodings should be similar (but might not be identical due to randomness)
                similarity = calculate_cosine_similarity(encoding1, encoding2)
                assert similarity > 0.8  # Should be quite similar
        except Exception:
            # If encoding fails, that's acceptable for this test
            pass
    
    def test_face_processing_pipeline(self):
        """Test the complete face processing pipeline."""
        # Create a test image
        img = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        
        try:
            # Step 1: Crop face
            cropped = crop_face(img)
            
            if cropped is not None:
                # Step 2: Generate encoding
                encoding = generate_encoding(cropped)
                
                if encoding is not None:
                    # Step 3: Test similarity with itself
                    similarity = calculate_cosine_similarity(encoding, encoding)
                    assert similarity == pytest.approx(1.0, abs=1e-6)
        except Exception:
            # If any step fails, that's acceptable for this test
            pass


if __name__ == "__main__":
    pytest.main([__file__])
