import cv2
import threading
import numpy as np
import concurrent.futures
from ultralytics import YOLO

class HelmetDetection:
    def __init__(self, seg_model, pose_model, seg_conf_value=0.3, pose_conf_value=0.3, iou_threshold=0.3):
        self.seg_conf_value = seg_conf_value
        self.pose_conf_value = pose_conf_value
        self.iou_threshold = iou_threshold
        self.seg_model = YOLO(seg_model,task ="segment")  # 1:gloves, 2:helmet, 3:person, 5:vest
        self.pose_model = YOLO(pose_model,task= "pose")
        self.threads = []
        self.running = False
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (640, 480))
        # Thread lock for thread safety
        self.locks = []

    def start(self, camera_details, test_video_path=None):
        self.person_count, self.no_helmet_count, self.no_vest_count, self.no_gloves_count = 0, 0, 0, 0
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0], cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.helmet_frames = [None] * len(self.cap_devices)
        self.frames = [None] * len(self.cap_devices)  # Store actual frames for each camera
        self.warning_count = [{"person_count": 0, "no_helmet_count": 0, "no_vest_count": 0, "no_gloves_count": 0} for _ in range(len(self.cap_devices))]
#       Create locks for each camera
        self.locks = [threading.Lock() for _ in range(len(self.cap_devices))]
        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(self.blank, f"{self.camera_name[idx]} is Offline", (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".png", temp)
                    with self.locks[idx]:  # Lock when updating shared resource
                        self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        for thread in self.threads:
            thread.join()
    
    
    def mask_iou(self, person_mask, ppe_mask):
        """Check if the PPE mask is significantly covered by the person mask"""
        # Convert masks to binary format if they aren't already
        person_binary = (person_mask > 0).astype(np.uint8) * 255
        ppe_binary = (ppe_mask > 0).astype(np.uint8) * 255
        
        # Calculate intersection using bitwise_and
        intersection = cv2.bitwise_and(ppe_binary, person_binary)
        
        # Calculate areas
        ppe_area = np.sum(ppe_binary > 0)  # Count non-zero pixels
        covered_area = np.sum(intersection > 0)  # Count non-zero pixels in intersection
        
        overlap_area = covered_area / ppe_area if ppe_area > 0 else 0
        # print("overlap_area", overlap_area)
        return overlap_area
    
    
    def bbox_iou(self,box1, box2):
        """Compute Intersection over Union (IoU) between two boxes."""
        x1, y1, x2, y2 = box1
        x1p, y1p, x2p, y2p = box2

        # Intersection
        xi1, yi1 = max(x1, x1p), max(y1, y1p)
        xi2, yi2 = min(x2, x2p), min(y2, y2p)
        inter_area = max(0, xi2 - xi1) * max(0, yi2 - yi1)

        # Union
        box1_area = (x2 - x1) * (y2 - y1)
        box2_area = (x2p - x1p) * (y2p - y1p)
        union_area = box1_area + box2_area - inter_area

        return inter_area / union_area if union_area != 0 else 0

    def is_ppe_in_correct_position(self, keypoints, ppe_type, ppe_bbox):
        x1, y1, x2, y2 = ppe_bbox  # Unpack bbox coordinates at the beginning
        ppe_center_x = (x1 + x2) / 2
        ppe_center_y = (y1 + y2) / 2
        
        if ppe_type == 'helmet':
            # Head keypoints: nose, eyes, ears
            head_keypoints = [0, 1, 2, 3, 4]
            valid_keypoints = [kp for kp_idx, kp in enumerate(keypoints) if kp_idx in head_keypoints and kp[0] > 0 and kp[1] > 0]
            
            if not valid_keypoints:
                # If no valid head keypoints, default to true if helmet is in upper part of person bbox
                person_height = keypoints[:, 1].max() - keypoints[:, 1].min()
                upper_body_limit = keypoints[:, 1].min() + person_height * 0.4  # Top 40% of body
                return ppe_center_y < upper_body_limit
            
            # Calculate head center
            head_center_x = sum([kp[0] for kp in valid_keypoints]) / len(valid_keypoints)
            head_center_y = sum([kp[1] for kp in valid_keypoints]) / len(valid_keypoints)
            
            # Calculate distance between helmet center and head center
            distance = np.sqrt((ppe_center_x - head_center_x)**2 + (ppe_center_y - head_center_y)**2)
            
            # Calculate head size as reference
            # Be more generous with head size estimation
            head_width = 0
            if all(keypoints[i][0] > 0 for i in [1, 2]):  # Both eyes visible
                head_width = np.sqrt((keypoints[1][0] - keypoints[2][0])**2 + (keypoints[1][1] - keypoints[2][1])**2)
            elif all(keypoints[i][0] > 0 for i in [3, 4]):  # Both ears visible
                head_width = np.sqrt((keypoints[3][0] - keypoints[4][0])**2 + (keypoints[3][1] - keypoints[4][1])**2)
            
            if head_width > 0:
                # Use a more generous multiplier (2.0 instead of 1.2)
                return distance < head_width * 2.0
            else:
                # If can't calculate width, use the helmet box dimensions as reference
                helmet_height = y2 - y1
                return distance < helmet_height * 1.5
        
        elif ppe_type == 'vest':
            # Check if vest is in the torso area
            torso_keypoints = [5, 6, 11, 12]  # shoulders and hips
            valid_keypoints = [kp for kp_idx, kp in enumerate(keypoints) if kp_idx in torso_keypoints and kp[0] > 0 and kp[1] > 0]
            
            if len(valid_keypoints) < 2:  # Need at least 2 torso keypoints
                return False
            
            # Calculate torso boundaries
            torso_x_values = [kp[0] for kp in valid_keypoints]
            torso_y_values = [kp[1] for kp in valid_keypoints]
            
            min_x, max_x = min(torso_x_values), max(torso_x_values)
            min_y, max_y = min(torso_y_values), max(torso_y_values)
            
            # Add margin to torso boundaries (20% on each side to be more forgiving)
            width = max_x - min_x
            height = max_y - min_y
            min_x -= width * 0.2
            max_x += width * 0.2
            min_y -= height * 0.2
            max_y += height * 0.2
            
            # For vest, check if ANY part of the bbox overlaps with the torso area
            # This helps when vest masks are split into pieces
            overlap_x = (x1 <= max_x and x2 >= min_x)
            overlap_y = (y1 <= max_y and y2 >= min_y)
            
            return overlap_x and overlap_y
        
        elif ppe_type == 'gloves':
            # Check if gloves are near hands
            hand_keypoints = [9, 10]  # wrists
            valid_wrists = [kp for kp_idx, kp in enumerate(keypoints) if kp_idx in hand_keypoints and kp[0] > 0 and kp[1] > 0]
            
            if not valid_wrists:
                return False
            
            # Check if glove is close to any wrist
            for wrist in valid_wrists:
                distance = np.sqrt((ppe_center_x - wrist[0])**2 + (ppe_center_y - wrist[1])**2)
                
                # Get forearm length as reference (distance from elbow to wrist)
                forearm_length = 0
                if wrist is keypoints[9] and keypoints[7][0] > 0:  # Left wrist and elbow
                    forearm_length = np.sqrt((keypoints[7][0] - keypoints[9][0])**2 + (keypoints[7][1] - keypoints[9][1])**2)
                elif wrist is keypoints[10] and keypoints[8][0] > 0:  # Right wrist and elbow
                    forearm_length = np.sqrt((keypoints[8][0] - keypoints[10][0])**2 + (keypoints[8][1] - keypoints[10][1])**2)
                else:
                    # Estimate forearm length if elbow not visible (about 15% of image height)
                    forearm_length = 0.15 * (y2 - y1)
                
                # Glove should be close to wrist (within forearm length)
                if distance < forearm_length:
                    return True
                    
            return False
        
        # Default case
        return False

    def assign_masks_to_person(self, pose_list, seg_classes):
    
        # Extract all person masks from segmentation data
        person_masks = seg_classes['person']
        person_info = []
        # print("person_masks",person_masks)
        
        for person_idx, (keypoints, pose_bbox) in enumerate(pose_list):
            best_iou = 0
            best_person_mask = None
            best_person_bbox = None

            for mask, cls_id, conf, bbox in person_masks:
                iou = self.bbox_iou(bbox, pose_bbox)

                if iou > best_iou and iou > self.iou_threshold:
                    best_iou = iou
                    best_person_mask = mask
                    best_person_bbox = bbox

            if best_person_mask is not None:
                person_info.append({
                    # "pose_index": person_idx,
                    "pose_bbox": pose_bbox,
                    "mask_bbox": best_person_bbox,
                    "keypoints": keypoints,
                    "matched_mask": best_person_mask,
                    "iou": best_iou
                })

        
        # print("person_info",person_info)
        
        return person_info
    
    
    def check_ppe_wearing(self, person_info, seg_classes):
        compliance_results = []

        for person in person_info:
            person_mask = person["matched_mask"]
            keypoints = person["keypoints"]
            person_result = {
                "helmet": False,
                "gloves": False,
                "vest": False,
                "bbox": person["pose_bbox"],
                "iou_scores": {"helmet": 0, "vest": 0, "gloves": 0},
                "position_correct": {"helmet": False, "vest": False, "gloves": False}
            }

            # Check helmet
            for helmet_mask, cls_id, conf, bbox in seg_classes['helmet']:
                overlap = self.mask_iou(person_mask, helmet_mask)
                if overlap > self.iou_threshold:
                    position_correct = self.is_ppe_in_correct_position(keypoints, 'helmet', bbox)
                    person_result["helmet"] = overlap > self.iou_threshold and position_correct
                    person_result["iou_scores"]["helmet"] = overlap
                    person_result["position_correct"]["helmet"] = position_correct
                    break
            
            # Check vest - combine overlaps from potentially multiple vest segments
            total_vest_overlap = 0
            vest_position_correct = False
            
            for vest_mask, cls_id, conf, bbox in seg_classes['vest']:
                overlap = self.mask_iou(person_mask, vest_mask)
                if overlap > 0.05:  # Lower threshold for individual vest pieces
                    position_correct = self.is_ppe_in_correct_position(keypoints, 'vest', bbox)
                    total_vest_overlap += overlap  # Accumulate overlap scores
                    if position_correct:
                        vest_position_correct = True  # If any piece is in correct position
            
            # Consider vest detected if combined overlap is sufficient
            if total_vest_overlap > self.iou_threshold:
                person_result["vest"] = vest_position_correct
                person_result["iou_scores"]["vest"] = total_vest_overlap
                person_result["position_correct"]["vest"] = vest_position_correct
            
            # Check gloves - for gloves we need to check both hands
            left_glove_found = False
            right_glove_found = False
            for gloves_mask, cls_id, conf, bbox in seg_classes['gloves']:
                overlap = self.mask_iou(person_mask, gloves_mask)
                if overlap > self.iou_threshold:
                    position_correct = self.is_ppe_in_correct_position(keypoints, 'gloves', bbox)
                    person_result["iou_scores"]["gloves"] = max(person_result["iou_scores"]["gloves"], overlap)
                    
                    # Update position_correct status for gloves
                    if position_correct:
                        # Check which hand this glove belongs to based on x-coordinate
                        glove_center_x = (bbox[0] + bbox[2]) / 2
                        
                        # Determine if this is left or right glove based on position relative to body center
                        body_center_x = (person["pose_bbox"][0] + person["pose_bbox"][2]) / 2
                        
                        if glove_center_x < body_center_x:  # Left side
                            left_glove_found = True
                        else:  # Right side
                            right_glove_found = True
                        
                        # Consider gloves detected if at least one hand has a glove
                        person_result["gloves"] = True
                        person_result["position_correct"]["gloves"] = True

            # Add this person's results to our compliance list
            compliance_results.append(person_result)
        
        return compliance_results
    
    def process_frame(self, frame):
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_seg = executor.submit(self.seg_model, frame,classes = [1,2,3,5])  # 1:gloves, 2:helmet, 3:person, 5:vest
            future_poses = executor.submit(self.pose_model, frame)
            
            seg_results = future_seg.result()
            pose_results = future_poses.result()
        

        seg_classes = {'person': [], 'helmet': [], 'gloves': [], 'vest': []}
        for result in seg_results:
            if result.masks is not None:
                masks = result.masks.data.cpu().numpy()
                clss  = result.boxes.cls.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                boxes = result.boxes.xyxy.cpu().numpy()
                for idx, (mask, cls, conf) in enumerate(zip(masks, clss, confs)):
                    if conf > self.seg_conf_value:
                        x1, y1, x2, y2 = map(int, boxes[idx])

                        binary_mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                        resized_mask = cv2.resize(mask, (frame.shape[1], frame.shape[0]))
                        binary_mask[resized_mask > 0.5] = 255
                        # seg_classes.append((binary_mask, int(cls), conf, (x1, y1, x2, y2)))        # 1:gloves, 2:helmet, 3:person, 5:vest
                        if cls == 1:
                            seg_classes['gloves'].append([binary_mask, int(cls), conf, (x1, y1, x2, y2)])
                        elif cls == 2:
                            seg_classes['helmet'].append([binary_mask, int(cls), conf, (x1, y1, x2, y2)])
                        elif cls == 3:
                            seg_classes['person'].append([binary_mask, int(cls), conf, (x1, y1, x2, y2)])
                        elif cls == 5:
                            seg_classes['vest'].append([binary_mask, int(cls), conf, (x1, y1, x2, y2)])
            

        pose_list = []
        for pose_result in pose_results:
            if pose_result.keypoints is not None:
                for kps, conf, pose_box in zip(pose_result.keypoints.xy.cpu().numpy(), pose_result.boxes.conf,pose_result.boxes.xyxy.cpu().numpy()):
                    if conf > self.pose_conf_value:
                        x1, y1, x2, y2 = map(int, pose_box)
                        # Skip if bounding box is invalid
                        if x1 >= x2 or y1 >= y2:
                            continue
                        pose_list.append((kps,(x1, y1, x2, y2)))
        
        return seg_classes, pose_list

    def update(self, idx, cap, test_video_path=None):
        frame_counter, skip_frames = 0, 2
        while self.running:
            ret, frame = cap.read()
            
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            frame = cv2.resize(frame, (640, 480), interpolation=cv2.INTER_AREA)
            # Store a copy of this frame for this specific camera
            with self.locks[idx]:
                self.frames[idx] = frame.copy()
            seg_classes, pose_list = self.process_frame(frame)
            
            # print(seg_classes)
            # person_masks = [mask for mask, cls_id, _, _ in seg_classes if cls_id == 3]  # Person class ID = 3
            
            # Associate masks with keypoints and validate PPE
            matched_person = self.assign_masks_to_person(pose_list, seg_classes)
            compliance_results = self.check_ppe_wearing(matched_person, seg_classes)

            # print(compliance_results)

            # # Store counts
            self.person_count = len(compliance_results)
            self.no_helmet_count = sum(1 for person in compliance_results if not person["helmet"])
            self.no_vest_count = sum(1 for person in compliance_results if not person["vest"])
            self.no_gloves_count = sum(1 for person in compliance_results if not person["gloves"])

            with self.locks[idx]:
                self.warning_count[idx] = {
                    "person_count": self.person_count,
                    "no_helmet_count": self.no_helmet_count,
                    "no_vest_count": self.no_vest_count,
                    "no_gloves_count": self.no_gloves_count
                }

            # Visualization (if needed)
            # for result in compliance_results:
            #     x1, y1, x2, y2 = result["bbox"]
                
            #     # Create label text
            #     label = []
            #     if result["helmet"]:
            #         label.append(f"Helmet: {result['iou_scores']['helmet']:.2f}")
            #     else:
            #         label.append("No Helmet")
                    
            #     if result["vest"]:
            #         label.append(f"Vest: {result['iou_scores']['vest']:.2f}")
            #     else:
            #         label.append("No Vest")
                    
            #     if result["gloves"]:
            #         label.append(f"Gloves: {result['iou_scores']['gloves']:.2f}")
            #     else:
            #         label.append("No Gloves")
                
            #     text = " | ".join(label)
            #     color = (0, 255, 0) if all([result["helmet"], result["vest"], result["gloves"]]) else (0, 0, 255)
                
            #     cv2.rectangle(self.frame, (x1, y1), (x2, y2), color, 2)
            #     y_offset = y1 - 10
            #     for idx, item in enumerate(label):
            #         y_pos = y_offset - (idx * 20)
            #         cv2.putText(self.frame, item, (x1, y_pos), 
            #                     cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Encode frame for web display
            _, buffer = cv2.imencode(".png", frame)
            with self.locks[idx]:
                self.helmet_frames[idx] = buffer.tobytes()

            if not self.running:
                break

