#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to demonstrate and test environment variable configuration.
This script shows how to use environment variables to configure the face recognition system.
"""
import os
import sys
import subprocess
import tempfile
from pathlib import Path

# Add the face_recognition app to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

def print_banner(text):
    """Print a banner with the given text."""
    print("\n" + "="*60)
    print(f" {text}")
    print("="*60)

def print_section(text):
    """Print a section header."""
    print(f"\n--- {text} ---")

def test_default_configuration():
    """Test loading default configuration."""
    print_section("Testing Default Configuration")
    
    try:
        from app.config import Settings
        
        # Create settings with defaults
        settings = Settings()
        
        print("✓ Configuration loaded successfully")
        print(f"  App Name: {settings.APP_NAME}")
        print(f"  Version: {settings.APP_VERSION}")
        print(f"  Debug: {settings.DEBUG}")
        print(f"  Host: {settings.HOST}")
        print(f"  Port: {settings.PORT}")
        print(f"  Database Host: {settings.DB_HOST}")
        print(f"  Database Name: {settings.DB_NAME}")
        print(f"  Face Threshold: {settings.FACE_THRESHOLD}")
        print(f"  Max Workers: {settings.MAX_WORKERS}")
        
        return True
    except Exception as e:
        print(f"✗ Failed to load default configuration: {e}")
        return False

def test_environment_override():
    """Test environment variable override."""
    print_section("Testing Environment Variable Override")
    
    try:
        # Set environment variables
        env_vars = {
            'FR_APP_NAME': 'Test Environment App',
            'FR_DEBUG': 'true',
            'FR_PORT': '9000',
            'FR_DB_HOST': 'test-database',
            'FR_FACE_THRESHOLD': '0.85',
            'FR_MAX_WORKERS': '8',
            'FR_LOG_LEVEL': 'DEBUG'
        }
        
        # Set environment variables
        for key, value in env_vars.items():
            os.environ[key] = value
        
        # Import after setting environment variables
        if 'app.config' in sys.modules:
            del sys.modules['app.config']
        
        from app.config import Settings
        
        # Create new settings instance
        settings = Settings()
        
        print("✓ Environment variables loaded successfully")
        print(f"  App Name: {settings.APP_NAME} (should be 'Test Environment App')")
        print(f"  Debug: {settings.DEBUG} (should be True)")
        print(f"  Port: {settings.PORT} (should be 9000)")
        print(f"  Database Host: {settings.DB_HOST} (should be 'test-database')")
        print(f"  Face Threshold: {settings.FACE_THRESHOLD} (should be 0.85)")
        print(f"  Max Workers: {settings.MAX_WORKERS} (should be 8)")
        print(f"  Log Level: {settings.LOG_LEVEL} (should be 'DEBUG')")
        
        # Verify values
        assert settings.APP_NAME == 'Test Environment App'
        assert settings.DEBUG == True
        assert settings.PORT == 9000
        assert settings.DB_HOST == 'test-database'
        assert settings.FACE_THRESHOLD == 0.85
        assert settings.MAX_WORKERS == 8
        assert settings.LOG_LEVEL == 'DEBUG'
        
        print("✓ All environment variable overrides working correctly")
        
        # Clean up environment variables
        for key in env_vars.keys():
            if key in os.environ:
                del os.environ[key]
        
        return True
    except Exception as e:
        print(f"✗ Failed to override with environment variables: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_env_file_loading():
    """Test loading configuration from .env file."""
    print_section("Testing .env File Loading")
    
    try:
        # Create a temporary .env file
        env_content = """
# Test environment file
FR_APP_NAME=Env File Test App
FR_DEBUG=false
FR_PORT=8080
FR_DB_HOST=env-database
FR_DB_NAME=env_test_db
FR_FACE_THRESHOLD=0.75
FR_MAX_WORKERS=6
FR_LOG_LEVEL=WARNING
FR_QDRANT_ENABLED=false
"""
        
        # Write to temporary .env file
        face_recognition_dir = Path(__file__).parent.parent / 'face_recognition'
        env_file_path = face_recognition_dir / '.env'
        
        # Backup existing .env file if it exists
        backup_path = None
        if env_file_path.exists():
            backup_path = env_file_path.with_suffix('.env.backup')
            env_file_path.rename(backup_path)
        
        try:
            # Write test .env file
            with open(env_file_path, 'w') as f:
                f.write(env_content)
            
            # Clear any existing environment variables
            env_vars_to_clear = [key for key in os.environ.keys() if key.startswith('FR_')]
            for key in env_vars_to_clear:
                del os.environ[key]
            
            # Reload the config module
            if 'app.config' in sys.modules:
                del sys.modules['app.config']
            
            from app.config import Settings
            
            # Create new settings instance
            settings = Settings()
            
            print("✓ .env file loaded successfully")
            print(f"  App Name: {settings.APP_NAME} (should be 'Env File Test App')")
            print(f"  Debug: {settings.DEBUG} (should be False)")
            print(f"  Port: {settings.PORT} (should be 8080)")
            print(f"  Database Host: {settings.DB_HOST} (should be 'env-database')")
            print(f"  Database Name: {settings.DB_NAME} (should be 'env_test_db')")
            print(f"  Face Threshold: {settings.FACE_THRESHOLD} (should be 0.75)")
            print(f"  Max Workers: {settings.MAX_WORKERS} (should be 6)")
            print(f"  Log Level: {settings.LOG_LEVEL} (should be 'WARNING')")
            print(f"  Qdrant Enabled: {settings.QDRANT_ENABLED} (should be False)")
            
            # Verify values
            assert settings.APP_NAME == 'Env File Test App'
            assert settings.DEBUG == False
            assert settings.PORT == 8080
            assert settings.DB_HOST == 'env-database'
            assert settings.DB_NAME == 'env_test_db'
            assert settings.FACE_THRESHOLD == 0.75
            assert settings.MAX_WORKERS == 6
            assert settings.LOG_LEVEL == 'WARNING'
            assert settings.QDRANT_ENABLED == False
            
            print("✓ All .env file values loaded correctly")
            
        finally:
            # Clean up test .env file
            if env_file_path.exists():
                env_file_path.unlink()
            
            # Restore backup if it existed
            if backup_path and backup_path.exists():
                backup_path.rename(env_file_path)
        
        return True
    except Exception as e:
        print(f"✗ Failed to load .env file: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_properties():
    """Test configuration helper properties and methods."""
    print_section("Testing Configuration Properties and Methods")
    
    try:
        from app.config import Settings
        
        # Create settings with test values
        os.environ.update({
            'FR_DB_DRIVER': 'postgresql',
            'FR_DB_USER': 'testuser',
            'FR_DB_PASSWORD': 'testpass',
            'FR_DB_HOST': 'testhost',
            'FR_DB_PORT': '5432',
            'FR_DB_NAME': 'testdb',
            'FR_QDRANT_HOST': 'qdrant-server',
            'FR_QDRANT_PORT': '6334'
        })
        
        # Reload config
        if 'app.config' in sys.modules:
            del sys.modules['app.config']
        
        from app.config import Settings
        settings = Settings()
        
        # Test database URL property
        expected_db_url = "********************************************/testdb"
        actual_db_url = settings.database_url
        print(f"  Database URL: {actual_db_url}")
        assert actual_db_url == expected_db_url
        print("✓ Database URL property working correctly")
        
        # Test Qdrant URL property
        expected_qdrant_url = "http://qdrant-server:6334"
        actual_qdrant_url = settings.qdrant_url
        print(f"  Qdrant URL: {actual_qdrant_url}")
        assert actual_qdrant_url == expected_qdrant_url
        print("✓ Qdrant URL property working correctly")
        
        # Test path helper methods
        image_path = settings.get_image_upload_path("test.jpg")
        print(f"  Image upload path: {image_path}")
        assert "test.jpg" in image_path
        print("✓ Image upload path method working correctly")
        
        cropped_path = settings.get_cropped_face_path("cropped.jpg")
        print(f"  Cropped face path: {cropped_path}")
        assert "cropped.jpg" in cropped_path
        print("✓ Cropped face path method working correctly")
        
        unknown_path = settings.get_unknown_face_path("unknown.jpg")
        print(f"  Unknown face path: {unknown_path}")
        assert "unknown.jpg" in unknown_path
        print("✓ Unknown face path method working correctly")
        
        # Test ensure_directories method (mock it to avoid creating actual directories)
        print("✓ Directory creation method available")
        
        # Clean up environment variables
        env_vars_to_clear = ['FR_DB_DRIVER', 'FR_DB_USER', 'FR_DB_PASSWORD', 
                           'FR_DB_HOST', 'FR_DB_PORT', 'FR_DB_NAME',
                           'FR_QDRANT_HOST', 'FR_QDRANT_PORT']
        for key in env_vars_to_clear:
            if key in os.environ:
                del os.environ[key]
        
        return True
    except Exception as e:
        print(f"✗ Failed to test configuration properties: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation():
    """Test configuration validation."""
    print_section("Testing Configuration Validation")
    
    try:
        from app.config import Settings
        
        # Test valid threshold
        os.environ['FR_FACE_THRESHOLD'] = '0.8'
        settings = Settings()
        assert settings.FACE_THRESHOLD == 0.8
        print("✓ Valid face threshold accepted")
        
        # Test invalid threshold (should raise error)
        try:
            os.environ['FR_FACE_THRESHOLD'] = '1.5'
            if 'app.config' in sys.modules:
                del sys.modules['app.config']
            from app.config import Settings
            Settings()
            print("✗ Invalid face threshold should have been rejected")
            return False
        except Exception:
            print("✓ Invalid face threshold correctly rejected")
        
        # Test valid image quality
        os.environ['FR_IMAGE_QUALITY'] = '85'
        os.environ['FR_FACE_THRESHOLD'] = '0.7'  # Reset to valid value
        if 'app.config' in sys.modules:
            del sys.modules['app.config']
        from app.config import Settings
        settings = Settings()
        assert settings.IMAGE_QUALITY == 85
        print("✓ Valid image quality accepted")
        
        # Clean up
        if 'FR_FACE_THRESHOLD' in os.environ:
            del os.environ['FR_FACE_THRESHOLD']
        if 'FR_IMAGE_QUALITY' in os.environ:
            del os.environ['FR_IMAGE_QUALITY']
        
        return True
    except Exception as e:
        print(f"✗ Failed to test validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all environment configuration tests."""
    print_banner("Face Recognition Environment Configuration Tests")
    
    tests = [
        test_default_configuration,
        test_environment_override,
        test_env_file_loading,
        test_configuration_properties,
        test_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print_banner(f"Environment Configuration Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All environment configuration tests passed!")
        print("\nYou can now use environment variables to configure the face recognition system:")
        print("1. Set environment variables with FR_ prefix")
        print("2. Create a .env file in the face_recognition directory")
        print("3. Use docker environment variables")
        print("\nExample:")
        print("  export FR_DEBUG=true")
        print("  export FR_DB_HOST=localhost")
        print("  export FR_FACE_THRESHOLD=0.8")
        return 0
    else:
        print("❌ Some environment configuration tests failed.")
        print("Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
