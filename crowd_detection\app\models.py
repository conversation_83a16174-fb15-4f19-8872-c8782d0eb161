from sqlalchemy import Column, Integer, String, <PERSON><PERSON>an, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime, timezone


Base = declarative_base()

class Camera(Base):
    __tablename__ = 'cameras'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    rtsp_url = Column(String(255), unique=True, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now())

class Image(Base):
    __tablename__ = 'images'
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(100), index=True)
    image_url = Column(String(255), nullable=True)

class Webhook(Base):
    __tablename__ = 'webhooks'
    
    id = Column(Integer, primary_key=True, index=True)
    url = Column(String(255), nullable=False, unique=True)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f"<Webhook(id={self.id}, url={self.url}, active={self.is_active})>"
    
class WhatsApp(Base):
    __tablename__ = 'whatsapp'

    id = Column(Integer, primary_key=True, index=True)
    phone_number = Column(String(20), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<WhatsApp(id={self.id}, phone_number={self.phone_number}, full_name={self.full_name}, active={self.is_active})>"

class SMS(Base):
    __tablename__ = 'sms'

    id = Column(Integer, primary_key=True, index=True)
    phone_number = Column(String(20), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<SMS(id={self.id}, phone_number={self.phone_number}, full_name={self.full_name}, active={self.is_active})>"

class Email(Base):
    __tablename__ = 'email'

    id = Column(Integer, primary_key=True, index=True)
    email_address = Column(String(100), nullable=False, unique=True)
    full_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Email(id={self.id}, email_address={self.email_address}, full_name={self.full_name}, active={self.is_active})>"

class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

class WebhookCreate(BaseModel):
    url: str
    description: Optional[str] = None
    body_template: Optional[dict] = None

class WebhookUpdate(BaseModel):
    url: Optional[str] = None
    description: Optional[str] = None
    body_template: Optional[dict] = None
    is_active: Optional[bool] = None

class WebhookResponse(BaseModel):
    id: int
    url: str
    description: Optional[str]
    body_template: Optional[dict]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class WhatsAppCreate(BaseModel):
    phone_number: str
    full_name: str

class WhatsAppUpdate(BaseModel):
    phone_number: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class WhatsAppResponse(BaseModel):
    id: int
    phone_number: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class SMSCreate(BaseModel):
    phone_number: str
    full_name: str

class SMSUpdate(BaseModel):
    phone_number: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class SMSResponse(BaseModel):
    id: int
    phone_number: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class EmailCreate(BaseModel):
    email_address: str
    full_name: str

class EmailUpdate(BaseModel):
    email_address: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class EmailResponse(BaseModel):
    id: int
    email_address: str
    full_name: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True