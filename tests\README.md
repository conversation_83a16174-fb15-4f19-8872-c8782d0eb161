# Face Recognition Module Test Suite

This directory contains comprehensive tests for the face recognition module. The tests are designed to validate all aspects of the face recognition system including registration, detection, database operations, and API endpoints.

## Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                # Test configuration and fixtures
├── test_registration.py       # User registration functionality tests
├── test_face_detection.py     # Face detection and recognition tests
├── test_api_endpoints.py      # API endpoint tests
├── test_database_operations.py # Database operation tests
├── test_utils.py              # Utility function tests
├── run_tests.py               # Test runner script
├── requirements.txt           # Test dependencies
└── README.md                  # This file
```

## Quick Start

### 1. Install Test Dependencies

```bash
cd tests
pip install -r requirements.txt
```

### 2. Run All Tests

```bash
python run_tests.py
```

### 3. Run Individual Test Files

```bash
# Run registration tests
python -m pytest test_registration.py -v

# Run face detection tests
python -m pytest test_face_detection.py -v

# Run API endpoint tests
python -m pytest test_api_endpoints.py -v

# Run database operation tests
python -m pytest test_database_operations.py -v

# Run utility function tests
python -m pytest test_utils.py -v
```

### 4. Run Tests with Coverage

```bash
python -m pytest . --cov=../face_recognition/app --cov-report=html
```

## Test Categories

### Registration Tests (`test_registration.py`)
- Face cropping functionality
- Face encoding generation
- User creation in database
- Duplicate username/email prevention
- Image and encoding record creation
- Base64 image processing
- Complete registration workflow

### Face Detection Tests (`test_face_detection.py`)
- Cosine similarity calculations
- Face recognition with different scenarios
- Attendance logging
- Face encoding consistency
- Threshold boundary testing
- Multiple user recognition

### API Endpoint Tests (`test_api_endpoints.py`)
- All HTTP endpoints
- WebSocket connections
- Camera management endpoints
- User management endpoints
- Attendance endpoints
- Error handling

### Database Operation Tests (`test_database_operations.py`)
- Model creation and validation
- Relationship testing
- Query operations
- Data integrity
- Foreign key constraints

### Utility Function Tests (`test_utils.py`)
- Face processing utilities
- Similarity calculations
- Camera loading functions
- Device detection
- Username resolution

## Test Fixtures

The `conftest.py` file provides several useful fixtures:

- `test_db_engine`: In-memory SQLite database for testing
- `test_db_session`: Database session for each test
- `test_user_data`: Sample user data
- `test_image`: Test image with simulated face
- `test_face_encoding`: Sample face encoding
- `temp_image_dir`: Temporary directory for test images
- `sample_camera_data`: Sample camera configuration
- `test_user_with_encoding`: Complete user with face encoding
- `test_camera`: Test camera in database

## Running Specific Tests

### Run tests by pattern:
```bash
python -m pytest -k "test_registration" -v
python -m pytest -k "test_face" -v
python -m pytest -k "test_database" -v
```

### Run tests with different verbosity:
```bash
python -m pytest -v          # Verbose
python -m pytest -vv         # Very verbose
python -m pytest -q          # Quiet
```

### Run tests with specific markers:
```bash
python -m pytest -m "not slow" -v    # Skip slow tests
```

## Test Configuration

### Environment Variables
Set these environment variables for testing:
- `TESTING=true`: Enables test mode
- `DATABASE_URL=sqlite:///:memory:`: Use in-memory database

### Mock Objects
Tests use extensive mocking to isolate functionality:
- Database operations are mocked where appropriate
- External services (Qdrant, file system) are mocked
- Face detection models are mocked for speed

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure the face_recognition module is in the Python path
2. **Missing Dependencies**: Install all requirements from `requirements.txt`
3. **Database Errors**: Tests use in-memory SQLite, no external database needed
4. **Model Loading Errors**: Face detection models are mocked in tests

### Debug Mode
Run tests with debug output:
```bash
python -m pytest --tb=long -v --capture=no
```

### Test Coverage
Generate detailed coverage report:
```bash
python -m pytest --cov=../face_recognition/app --cov-report=html --cov-report=term
```

## Adding New Tests

### Test Naming Convention
- Test files: `test_*.py`
- Test functions: `test_*`
- Test classes: `Test*`

### Example Test Function
```python
def test_new_functionality(test_db_session, test_user_data):
    """Test description."""
    # Arrange
    user = User(**test_user_data)
    
    # Act
    test_db_session.add(user)
    test_db_session.commit()
    
    # Assert
    assert user.user_id is not None
```

## Continuous Integration

These tests are designed to run in CI/CD environments:
- No external dependencies required
- Fast execution with mocked services
- Comprehensive coverage reporting
- Clear pass/fail indicators

## Performance Considerations

- Tests use in-memory database for speed
- Face detection models are mocked
- Large file operations are avoided
- Parallel test execution is supported

## Cleanup

Tests automatically clean up:
- Database sessions are closed after each test
- Temporary files are removed
- Mock objects are reset

When you're done testing, you can safely delete the entire `tests/` directory.

## Support

If you encounter issues with the tests:
1. Check the test output for specific error messages
2. Verify all dependencies are installed
3. Ensure the face_recognition module is properly structured
4. Review the test logs for detailed information
