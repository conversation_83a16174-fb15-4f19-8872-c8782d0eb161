#!/usr/bin/env python3
"""
Startup script for face recognition application.
This script ensures all required models and dependencies are available before starting the app.
"""
import os
import sys
import logging
from pathlib import Path

# Add the app to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """Set up logging for startup script."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    return logging.getLogger(__name__)

def check_dlib_model():
    """Check if dlib face landmarks model is available."""
    logger = logging.getLogger(__name__)
    
    try:
        from app.config import settings
        model_path = Path(settings.DLIB_MODEL_PATH)
        
        if model_path.exists():
            file_size = model_path.stat().st_size
            logger.info(f"✓ dlib face landmarks model found: {model_path} ({file_size:,} bytes)")
            return True
        else:
            logger.error(f"✗ dlib face landmarks model not found: {model_path}")
            logger.info("You can download it using: python download_models.py")
            return False
    except Exception as e:
        logger.error(f"Error checking dlib model: {e}")
        return False

def check_dlib_installation():
    """Check if dlib is properly installed."""
    logger = logging.getLogger(__name__)
    
    try:
        import dlib
        logger.info(f"✓ dlib version {dlib.DLIB_VERSION} is installed")
        
        # Test dlib functionality
        detector = dlib.get_frontal_face_detector()
        logger.info("✓ dlib face detector initialized successfully")
        
        return True
    except ImportError as e:
        logger.error(f"✗ dlib is not installed: {e}")
        logger.info("Install dlib with: pip install dlib")
        return False
    except Exception as e:
        logger.error(f"✗ Error testing dlib: {e}")
        return False

def check_opencv():
    """Check if OpenCV is properly installed."""
    logger = logging.getLogger(__name__)
    
    try:
        import cv2
        logger.info(f"✓ OpenCV version {cv2.__version__} is installed")
        return True
    except ImportError as e:
        logger.error(f"✗ OpenCV is not installed: {e}")
        logger.info("Install OpenCV with: pip install opencv-python")
        return False
    except Exception as e:
        logger.error(f"✗ Error testing OpenCV: {e}")
        return False

def check_face_recognition():
    """Check if face_recognition library is available."""
    logger = logging.getLogger(__name__)
    
    try:
        import face_recognition
        logger.info("✓ face_recognition library is installed")
        return True
    except ImportError as e:
        logger.error(f"✗ face_recognition library is not installed: {e}")
        logger.info("Install face_recognition with: pip install face-recognition")
        return False
    except Exception as e:
        logger.error(f"✗ Error testing face_recognition: {e}")
        return False

def check_pytorch():
    """Check if PyTorch is available."""
    logger = logging.getLogger(__name__)
    
    try:
        import torch
        logger.info(f"✓ PyTorch version {torch.__version__} is installed")
        
        # Check CUDA availability
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA is available: {torch.cuda.get_device_name(0)}")
        else:
            logger.info("⚠ CUDA is not available, using CPU")
        
        return True
    except ImportError as e:
        logger.error(f"✗ PyTorch is not installed: {e}")
        logger.info("Install PyTorch from: https://pytorch.org/get-started/locally/")
        return False
    except Exception as e:
        logger.error(f"✗ Error testing PyTorch: {e}")
        return False

def check_database_connection():
    """Check database connection."""
    logger = logging.getLogger(__name__)
    
    try:
        from app.config import settings
        from app.database import engine
        
        # Test database connection
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            logger.info("✓ Database connection successful")
            return True
    except Exception as e:
        logger.error(f"✗ Database connection failed: {e}")
        logger.info("Check your database configuration and ensure the database server is running")
        return False

def check_qdrant_connection():
    """Check Qdrant connection if enabled."""
    logger = logging.getLogger(__name__)
    
    try:
        from app.config import settings
        
        if not settings.QDRANT_ENABLED:
            logger.info("⚠ Qdrant is disabled in configuration")
            return True
        
        from qdrant_client import QdrantClient
        
        client = QdrantClient(
            host=settings.QDRANT_HOST,
            port=settings.QDRANT_PORT,
            timeout=5
        )
        
        # Test connection
        collections = client.get_collections()
        logger.info("✓ Qdrant connection successful")
        return True
    except Exception as e:
        logger.error(f"✗ Qdrant connection failed: {e}")
        logger.info("Check your Qdrant configuration and ensure the Qdrant server is running")
        return False

def create_required_directories():
    """Create required directories."""
    logger = logging.getLogger(__name__)
    
    try:
        from app.config import settings
        
        directories = [
            settings.STATIC_DIR,
            settings.IMAGES_DIR,
            settings.CROPPED_FACES_DIR,
            settings.DATASET_DIR,
            settings.UNKNOWN_DIR,
            settings.MODEL_CACHE_DIR,
            "app/models"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"✓ Directory ensured: {directory}")
        
        return True
    except Exception as e:
        logger.error(f"✗ Error creating directories: {e}")
        return False

def run_startup_checks():
    """Run all startup checks."""
    logger = setup_logging()
    logger.info("="*60)
    logger.info(" Face Recognition Application Startup Checks")
    logger.info("="*60)
    
    checks = [
        ("Create Required Directories", create_required_directories),
        ("OpenCV Installation", check_opencv),
        ("dlib Installation", check_dlib_installation),
        ("dlib Face Landmarks Model", check_dlib_model),
        ("face_recognition Library", check_face_recognition),
        ("PyTorch Installation", check_pytorch),
        ("Database Connection", check_database_connection),
        ("Qdrant Connection", check_qdrant_connection),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n--- {check_name} ---")
        try:
            if check_func():
                passed += 1
        except Exception as e:
            logger.error(f"Check '{check_name}' crashed: {e}")
    
    logger.info("\n" + "="*60)
    logger.info(f" Startup Checks Results: {passed}/{total} passed")
    logger.info("="*60)
    
    if passed == total:
        logger.info("🎉 All startup checks passed! Starting the application...")
        return True
    else:
        logger.error("❌ Some startup checks failed. Please fix the issues above.")
        return False

def main():
    """Main startup function."""
    if run_startup_checks():
        # Import and start the application
        try:
            import uvicorn
            from app.main import app
            from app.config import settings
            
            logger = logging.getLogger(__name__)
            logger.info(f"Starting {settings.APP_NAME} on {settings.HOST}:{settings.PORT}")
            
            uvicorn.run(
                app,
                host=settings.HOST,
                port=settings.PORT,
                log_level=settings.LOG_LEVEL.lower(),
                reload=settings.RELOAD
            )
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to start application: {e}")
            return 1
    else:
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
