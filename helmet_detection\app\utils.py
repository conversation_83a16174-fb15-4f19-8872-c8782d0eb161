import cv2
import threading
from ultralytics import YOLO
import numpy as np
import concurrent.futures

class HelmetDetection:
    def __init__(self, object_model, pose_model, conf_value, pose_conf_value=0.5):
        self.conf_value = conf_value
        self.pose_conf_value = pose_conf_value
        self.object_model = YOLO(object_model, task="detect")  
        self.pose_model = YOLO(pose_model, task="pose")
        self.threads = []
        self.running = False
        self.blank = cv2.imread(r".\static\black.jpg")
        self.blank = cv2.resize(self.blank, (640, 480))
        # Thread lock for thread safety
        self.locks = []

    def start(self, camera_details, test_video_path=None):
        self.person_count, self.no_helmet_count, self.no_vest_count, self.no_gloves_count = 0, 0, 0, 0
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        # If test_video_path is provided, override camera details
        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0], cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.helmet_frames = [None] * len(self.cap_devices)
        self.frames = [None] * len(self.cap_devices)  # Store actual frames for each camera
        self.warning_count = [{"person_count": 0, "no_helmet_count": 0, "no_vest_count": 0, "no_gloves_count": 0} for _ in range(len(self.cap_devices))]
        # Create locks for each camera
        self.locks = [threading.Lock() for _ in range(len(self.cap_devices))]

        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(self.blank, f"{self.camera_name[idx]} is Offline", (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".png", temp)
                    with self.locks[idx]:  # Lock when updating shared resource
                        self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        for thread in self.threads:
            thread.join()
        # cv2.destroyAllWindows()

    def check_PPE(self, objects, poses, base_threshold=50, scale_factor=0.2):
        helmet_id, gloves_id, vest_id = 2, 1, 5
        person_compliance = []  

        for pose in poses:
            keypoints = pose["keypoints"]
            x1, y1, x2, y2 = pose["bbox"]  
            person_height = y2 - y1  # Calculate the height of the person

            # **Adaptive threshold scaling**
            distance_threshold = max(base_threshold, int(person_height * scale_factor))

            head_points = np.array([keypoints[1], keypoints[2], keypoints[3], keypoints[4]])
            chest_points = np.array([keypoints[5], keypoints[6], keypoints[11], keypoints[12]])  
            hand_points = np.array([keypoints[9], keypoints[10]])

            helmet_worn, vest_worn, gloves_worn = False, False, False

            for obj in objects:
                ox1, oy1, ox2, oy2 = obj["bbox"]
                center_x, center_y = (ox1 + ox2) // 2, (oy1 + oy2) // 2 

                if obj["class_id"] == helmet_id and not helmet_worn and head_points.size > 0:
                    distances = np.linalg.norm(head_points - np.array([center_x, center_y]), axis=1)
                    if np.any(distances < distance_threshold):
                        helmet_worn = True

                if obj["class_id"] == vest_id and not vest_worn and chest_points.size > 0:
                    distances = np.linalg.norm(chest_points - np.array([center_x, center_y]), axis=1)
                    if np.any(distances < distance_threshold + 50):  
                        vest_worn = True

                if obj["class_id"] == gloves_id and not gloves_worn and hand_points.size > 0:
                    distances = np.linalg.norm(hand_points - np.array([center_x, center_y]), axis=1)
                    if np.any(distances < distance_threshold + 35):
                        gloves_worn = True

                if helmet_worn and vest_worn and gloves_worn:
                    break

            compliance_status = {
                "bbox": pose["bbox"],
                "helmet": helmet_worn,
                "vest": vest_worn,
                "gloves": gloves_worn
            }
            person_compliance.append(compliance_status)

        return person_compliance

    def process_frame(self, frame):
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_objects = executor.submit(self.object_model, frame, classes=[1, 2, 3, 5], verbose=False)
            future_poses = executor.submit(self.pose_model, frame, verbose=False)
            
            object_results = future_objects.result()
            pose_results = future_poses.result()
 
        objects, poses = [], []

        for object_result in object_results:
            for det in object_result.boxes:
                if det.conf > self.conf_value:
                    x1, y1, x2, y2 = map(int, det.xyxy[0])
                    objects.append({"class_id": int(det.cls), "bbox": (x1, y1, x2, y2)})
                 
        # Process pose detections (if keypoints exist)
        for pose_result in pose_results:
            if pose_result.keypoints is not None:
                for person, box in zip(pose_result.keypoints, pose_result.boxes):
                    if box.conf > self.pose_conf_value:
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        poses.append({"keypoints": person.xy.cpu().numpy()[0], "bbox": (x1, y1, x2, y2)})

        return objects, poses

    def update(self, idx, cap, test_video_path=None):
        frame_counter, skip_frames = 0, 2
        
        # Create named window with specific position
        # window_name = f"Camera {idx}: {self.camera_name[idx]}"
        # cv2.namedWindow(window_name)
        
        # Position windows side by side
        # x_position = idx * 660  # 640 + 20 pixel margin
        # cv2.moveWindow(window_name, x_position, 30)
        
        while self.running:
            ret, frame = cap.read()  # Use local variable instead of self.frame
            
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            frame = cv2.resize(frame, (640, 480), interpolation=cv2.INTER_AREA)
            
            # Store a copy of this frame for this specific camera
            with self.locks[idx]:
                self.frames[idx] = frame.copy()

            objects, poses = self.process_frame(frame)
            compliance_results = self.check_PPE(objects=objects, poses=poses)

            # Store counts with proper locking
            person_count = len(compliance_results)
            no_helmet_count = sum(1 for person in compliance_results if not person["helmet"])
            no_vest_count = sum(1 for person in compliance_results if not person["vest"])
            no_gloves_count = sum(1 for person in compliance_results if not person["gloves"])

            with self.locks[idx]:
                self.warning_count[idx] = {
                    "person_count": person_count,
                    "no_helmet_count": no_helmet_count,
                    "no_vest_count": no_vest_count,
                    "no_gloves_count": no_gloves_count
                }
            
            # Draw information on the frame
            # info_text = f"People: {person_count} | No Helmet: {no_helmet_count} | No Vest: {no_vest_count} | No Gloves: {no_gloves_count}"
            # cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Show the frame for this specific camera
            # cv2.imshow(window_name, frame)
            # cv2.waitKey(1)

            # Store the encoded frame for web display
            _, buffer = cv2.imencode(".png", frame)
            with self.locks[idx]:
                self.helmet_frames[idx] = buffer.tobytes()

            if not self.running:
                break

        # cv2.destroyWindow(window_name)