# Note: This module uses PyTorch FaceNet (InceptionResnetV1) instead of Keras FaceNet for face encoding
from deepface import DeepFace
from scipy.spatial.distance import cosine
import cv2
from mtcnn import MTCNN
import os
import asyncio
from .models import Encoding, User
import math
from facenet_pytorch import InceptionResnetV1
import numpy as np
import torch
from torchvision import transforms
from pathlib import Path
import json
import time
import dlib
from imutils import face_utils


embedder = InceptionResnetV1(pretrained='vggface2').eval()
# Initialize dlib's face detector and facial landmark predictor
face_detector = dlib.get_frontal_face_detector()
landmark_predictor = dlib.shape_predictor("app/models/shape_predictor_68_face_landmarks.dat")
# DEPRECATED: Camera configuration is now stored in the database
# This constant is kept for backward compatibility
# CAMERAS_FILE_PATH = "app/face_recognition/cameras.json"

def generate_encoding(face_image):
    """
    Generate face encoding using a CPU-friendly method.
    
    Args:
        face_image: Face image (numpy array)
        
    Returns:
        Face encoding (numpy array)
    """
    try:
        # Use a simpler method for face encoding
        # Resize to a standard size
        face = cv2.resize(face_image, (160, 160))
        
        # Convert to grayscale
        gray = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
        
        # Flatten and normalize
        encoding = gray.flatten().astype(np.float32) / 255.0
        
        # Reduce dimensionality to 128 (similar to FaceNet)
        from sklearn.decomposition import PCA
        pca = PCA(n_components=128)
        encoding = pca.fit_transform(encoding.reshape(1, -1))[0]
        
        return encoding
    except Exception as e:
        print(f"Error in generate_encoding: {e}")
        # Return a random encoding as fallback
        return np.random.rand(128).astype(np.float32)

def generate_deepface_encoding(face_image):
        """Generates a face encoding using PyTorch FaceNet (alias for generate_encoding)."""
        return generate_encoding(face_image)


def align_face(image):
    """
    Align face using OpenCV instead of MTCNN.
    This is a simplified version that just crops the face.
    
    Args:
        image: Input image (numpy array)
        
    Returns:
        Aligned face image or None if no face is detected
    """
    return crop_face(image)

def crop_face(image):
    """
    Crop the face from an image using a CPU-friendly method.
    
    Args:
        image: Input image (numpy array)
        
    Returns:
        Cropped face image or None if no face is detected
    """
    try:
        # Use OpenCV's face detector instead of MTCNN
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) == 0:
            print("No face detected")
            return None
        
        # Get the largest face
        largest_area = 0
        largest_face = None
        
        for (x, y, w, h) in faces:
            if w * h > largest_area:
                largest_area = w * h
                largest_face = (x, y, w, h)
        
        if largest_face is None:
            return None
            
        x, y, w, h = largest_face
        
        # Add some margin
        margin = int(0.2 * w)
        x_start = max(0, x - margin)
        y_start = max(0, y - margin)
        x_end = min(image.shape[1], x + w + margin)
        y_end = min(image.shape[0], y + h + margin)
        
        # Crop the face
        face = image[y_start:y_end, x_start:x_end]
        
        # Resize to a standard size
        face = cv2.resize(face, (160, 160))
        
        return face
    except Exception as e:
        print(f"Error in crop_face: {e}")
        return None

def calculate_cosine_similarity(embedding1, embedding2):
    return 1-cosine(embedding1, embedding2)

def load_cameras(db=None):
    """
    Load cameras from the database.

    Args:
        db: SQLAlchemy database session

    Returns:
        Dictionary of cameras with camera name as key and a list containing RTSP URL as value
    """
    from app.core import database
    from .models import Camera

    # If no db session is provided, create one
    if db is None:
        db_session = database.SessionLocal()
        close_session = True
    else:
        db_session = db
        close_session = False

    try:
        # Query all cameras from the database
        cameras = db_session.query(Camera).all()

        # Convert to the expected format (dict with camera name as key and list with RTSP URL as value)
        result = {}
        for camera in cameras:
            result[camera.name] = [camera.rtsp_url]
        # print(result)
        return result
    finally:
        # Close the session if we created it
        if close_session:
            db_session.close()

# This function is kept for backward compatibility but doesn't do anything
# since we're now using the database
def save_cameras(cameras_data):
    """
    This function is deprecated. Cameras should be saved to the database using the API endpoints.
    """
    print("Warning: save_cameras() is deprecated. Use the API endpoints to manage cameras.")

# def get_available_devices():
#     print("Fetching available devices...")
#     index = 0
#     available_devices = []
#     while True:
#         cap = cv2.VideoCapture(index)
#         if not cap.read()[0]:
#             break
#         else:
#             available_devices.append(index)
#         cap.release()
#         index += 1
#     return available_devices
# devices = get_available_devices()

# print(devices)

def get_username_from_id(user_id, db_users_cache):
    """Function to extract the username from the user_id from cached data of user."""
    user = next((user for user in db_users_cache if user.id == user_id), None)
    if user:
        return user.username
    return None



# to save images with a delay mechanism
# This is to prevent saving too many images in a short time

last_saved_time = {}
SAVE_DELAY = 10  # seconds

def save_person_image(face_image, username):
    """Save face image to per-user folder with a delay mechanism."""
    global last_saved_time

    # Validate face image before saving
    if face_image is None or face_image.size == 0:
        print(f"[INFO] Skipped saving invalid image for {username}")
        return False

    # Add minimum face size check
    if face_image.shape[0] < 50 or face_image.shape[1] < 50:
        print(f"[INFO] Skipped saving too small face for {username}")
        return False

    current_time = time.time()
    user_folder = os.path.join("Dataset", username)
    os.makedirs(user_folder, exist_ok=True)

    last_time = last_saved_time.get(username, 0)
    if current_time - last_time >= SAVE_DELAY:
        timestamp = int(current_time)
        image_path = os.path.join(user_folder, f"{username}_{timestamp}.jpg")
        cv2.imwrite(image_path, face_image)
        last_saved_time[username] = current_time
        print(f"[INFO] Saved image for {username} at {image_path}")
        return True
    else:
        print(f"[INFO] Skipped saving image for {username} (within delay window)")
        return False



def save_unknown_image(face_image, persistent_id):
    """Save unknown face image with delay based on persistent_id.
    Returns the image path if successful, None otherwise."""
    global last_saved_time

    current_time = time.time()
    folder_path = os.path.join("Dataset", "unknown")
    os.makedirs(folder_path, exist_ok=True)

    # Ensure the face image is valid
    if face_image is None or face_image.size == 0:
        print(f"[ERROR] Invalid face image for {persistent_id}")
        return None

    # Ensure the persistent_id is valid
    if not persistent_id or not isinstance(persistent_id, str):
        print(f"[ERROR] Invalid persistent_id: {persistent_id}")
        return None

    # Generate timestamp and image filename
    timestamp = int(current_time)
    image_filename = f"unknown_{persistent_id}_{timestamp}.jpg"
    image_path = os.path.join(folder_path, image_filename)
    web_path = f"Dataset/unknown/{image_filename}"  # Path for web access

    # Check if we should save based on delay
    last_time = last_saved_time.get(persistent_id, 0)
    if current_time - last_time >= SAVE_DELAY:
        try:
            success = cv2.imwrite(image_path, face_image)
            if success:
                last_saved_time[persistent_id] = current_time
                print(f"[INFO] Saved unknown image for {persistent_id} at {image_path}")
                return web_path  # Return the web-accessible path
            else:
                print(f"[ERROR] Failed to save image for {persistent_id} at {image_path}")
                return None
        except Exception as e:
            print(f"[ERROR] Exception saving image for {persistent_id}: {str(e)}")
            return None
    else:
        print(f"[INFO] Skipped saving image for {persistent_id} (within delay window)")
        return None
