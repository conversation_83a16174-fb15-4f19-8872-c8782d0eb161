from twilio.rest import Client
import os

# Load credentials from environment or settings
TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID", "your_account_sid")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN", "your_auth_token")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER", "+**********")  # your Twilio number

# Normally Twilio is synchronous. You can run it in a thread to avoid blocking.
import asyncio
from concurrent.futures import ThreadPoolExecutor

executor = ThreadPoolExecutor()

def _send_sms_sync(to_number: str, message: str):
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
    message = client.messages.create(
        body=message,
        from_=TWILIO_PHONE_NUMBER,
        to=to_number
    )
    return message.sid

async def send_sms(to_number: str, message: str):
    loop = asyncio.get_event_loop()
    sid = await loop.run_in_executor(executor, _send_sms_sync, to_number, message)
    return sid
