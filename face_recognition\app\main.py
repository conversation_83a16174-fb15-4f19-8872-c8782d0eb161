from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.templating import Jin<PERSON>2Templates
from contextlib import asynccontextmanager
import os
import logging
from app.routes import router
from app.config import settings
# from app.database import create_tables

# Configure logging based on settings
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format=settings.LOG_FORMAT,
    filename=settings.LOG_FILE if settings.LOG_FILE else None,
)
logger = logging.getLogger(__name__)

# Log startup information
logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
logger.info(f"Debug mode: {settings.DEBUG}")
logger.info(f"Testing mode: {settings.TESTING}")

# Application lifespan events (modern FastAPI approach)
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    try:
        logger.info("Application startup initiated")

        # Ensure directories exist
        settings.ensure_directories()

        # Log configuration summary
        logger.info(f"Database: {settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}")
        logger.info(f"Face threshold: {settings.FACE_THRESHOLD}")
        logger.info(f"Qdrant enabled: {settings.QDRANT_ENABLED}")
        logger.info(f"Device: {settings.DEVICE}")
        logger.info(f"Max workers: {settings.MAX_WORKERS}")

        # Create database tables if they don't exist
        # create_tables()
        # logger.info("Database tables created successfully")

        logger.info("Application startup completed successfully")
    except Exception as e:
        logger.error(f"Error during application startup: {e}")
        raise

    yield  # Application is running

    # Shutdown
    logger.info("Application shutdown initiated")
    # Add any cleanup code here
    logger.info("Application shutdown completed")

# Create FastAPI app with configuration
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    debug=settings.DEBUG,
    description="Face Recognition System for attendance and security",
    lifespan=lifespan
)

# Add CORS middleware with configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ensure all required directories exist
settings.ensure_directories()
logger.info("Required directories created/verified")

# Set up templates
templates = Jinja2Templates(directory="templates")

# Mount static files using configured directory
app.mount("/static", StaticFiles(directory=settings.STATIC_DIR), name="static")

# Mount cropped faces directory if it exists
if os.path.exists(settings.CROPPED_FACES_DIR):
    app.mount("/cropped_faces", StaticFiles(directory=settings.CROPPED_FACES_DIR), name="cropped_faces")

# Include router without prefix
app.include_router(router, prefix="/face_recognition")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "debug": settings.DEBUG
    }

# Configuration endpoint (for debugging, remove in production)
@app.get("/config")
async def get_config():
    """Get current configuration (for debugging only)."""
    if not settings.DEBUG:
        return {"error": "Configuration endpoint only available in debug mode"}

    # Return non-sensitive configuration
    config_data = {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "debug": settings.DEBUG,
        "host": settings.HOST,
        "port": settings.PORT,
        "database_host": settings.DB_HOST,
        "database_name": settings.DB_NAME,
        "face_threshold": settings.FACE_THRESHOLD,
        "max_workers": settings.MAX_WORKERS,
        "qdrant_enabled": settings.QDRANT_ENABLED,
        "device": settings.DEVICE
    }
    return config_data

# Duplicate function removed

if __name__ == "__main__":
    import uvicorn

    # Use configuration for server settings
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
        workers=1 if settings.RELOAD else settings.MAX_WORKERS
    )