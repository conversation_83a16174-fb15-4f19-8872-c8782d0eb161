from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.templating import <PERSON><PERSON>2Templates
import os
import logging
from app.routes import router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Face Recognition System")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add no_auth middleware to set admin role for all requests
@app.middleware("http")
async def no_auth_middleware(request: Request, call_next):
    # Set admin role for all requests without authentication
    request.state.user = {"role": "admin"}
    response = await call_next(request)
    return response

# Set up templates
templates = Jinja2Templates(directory="templates")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Include router without prefix
app.include_router(router, prefix="/face_recognition")

# Remove any authentication middleware or dependencies
# Authentication system has been removed

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
