-- Create databases for each module
CREATE DATABASE IF NOT EXISTS face_recognition_db;
CREATE DATABASE IF NOT EXISTS crowd_detection_db;
CREATE DATABASE IF NOT EXISTS helmet_detection_db;
CREATE DATABASE IF NOT EXISTS quality_control_db;
CREATE DATABASE IF NOT EXISTS surveillance_system;

-- Grant privileges to root user for remote connections
ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY 'root';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

FLUSH PRIVILEGES;
