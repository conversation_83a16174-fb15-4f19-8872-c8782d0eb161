# Alternative docker-compose.yml with init container for model download
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: surveillance_system
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant_db
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  # Init container to download models
  face_recognition_init:
    image: alpine:latest
    container_name: face_recognition_init
    volumes:
      - face_models:/models
    command: >
      sh -c "
        echo 'Downloading face recognition models...' &&
        apk add --no-cache curl bzip2 &&
        mkdir -p /models &&
        if [ ! -f /models/shape_predictor_68_face_landmarks.dat ]; then
          echo 'Downloading dlib face landmarks model...' &&
          curl -L 'https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2' -o /models/shape_predictor_68_face_landmarks.dat.bz2 &&
          bzip2 -d /models/shape_predictor_68_face_landmarks.dat.bz2 &&
          echo 'Model downloaded successfully: ' &&
          ls -la /models/shape_predictor_68_face_landmarks.dat
        else
          echo 'Model already exists'
        fi &&
        echo 'Model setup complete'
      "

  face_recognition:
    build:
      context: ../face_recognition
    container_name: face_recognition_app
    runtime: nvidia
    ports:
      - "8001:8000"
    volumes:
      - face_models:/app/app/models
    environment:
      # Face Recognition Database
      - FR_DB_USER=root
      - FR_DB_PASSWORD=root
      - FR_DB_HOST=mysql
      - FR_DB_NAME=face_recognition_db
      - FR_DB_PORT=3306
      # Authentication Database
      - FR_AUTH_DB_USER=root
      - FR_AUTH_DB_PASSWORD=root
      - FR_AUTH_DB_HOST=mysql
      - FR_AUTH_DB_NAME=surveillance_system
      - FR_AUTH_DB_PORT=3306
      # Qdrant
      - FR_QDRANT_HOST=qdrant
      - FR_QDRANT_PORT=6333
      - FR_QDRANT_ENABLED=true
      # Server Settings
      - FR_HOST=0.0.0.0
      - FR_PORT=8000
      - FR_DEBUG=false
      # Performance
      - FR_DEVICE=cuda
      - FR_MAX_WORKERS=4
      # Model Paths
      - FR_DLIB_MODEL_PATH=/app/app/models/shape_predictor_68_face_landmarks.dat
      - FACE_RECOGNITION_MODELS_PATH=/app/app/models
    depends_on:
      mysql:
        condition: service_healthy
      qdrant:
        condition: service_started
      face_recognition_init:
        condition: service_completed_successfully

  alarm_service:
    build:
      context: ../alarm_service
    container_name: alarm_service_app
    ports:
      - "8000:8000"
    environment:
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_HOST=mysql
      - DB_NAME=surveillance_system
      - DB_PORT=3306
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
  qdrant_data:
  face_models:  # Shared volume for face recognition models
