# dlib Setup Guide for Face Recognition

## Overview

This guide explains how to properly set up dlib and the required face landmarks model for the face recognition system, both for Docker and local development.

## What Was Fixed

### 1. **Updated Requirements.txt**
- ✅ Added `dlib==19.24.2` with specific version
- ✅ Added `face-recognition==1.3.0` library
- ✅ Added `imutils==0.5.4` for image utilities
- ✅ Organized dependencies by category
- ✅ Added proper version constraints

### 2. **Enhanced Dockerfile**
- ✅ Added all required system dependencies for dlib compilation
- ✅ Added automatic download of dlib face landmarks model
- ✅ Added model verification steps
- ✅ Set environment variables for model paths
- ✅ Added health check for container monitoring

### 3. **Created Model Download Script**
- ✅ `download_models.py` - Automatic model download for local development
- ✅ Progress indication during download
- ✅ Automatic extraction of compressed models
- ✅ Model verification and info generation

### 4. **Added Startup Validation**
- ✅ `startup.py` - Comprehensive startup checks
- ✅ Validates all dependencies before starting
- ✅ Checks model availability
- ✅ Tests database and Qdrant connections

### 5. **Updated Configuration**
- ✅ Added `DLIB_MODEL_PATH` configuration option
- ✅ Environment variable support for model paths
- ✅ Flexible model location configuration

## Docker Setup (Recommended)

### 1. Build and Run with Docker Compose
```bash
cd docker-service
docker-compose up --build face_recognition
```

### 2. What Happens During Docker Build:
1. **System Dependencies**: Installs all required libraries for dlib compilation
2. **Python Dependencies**: Installs dlib and other requirements
3. **Model Download**: Automatically downloads dlib face landmarks model
4. **Verification**: Checks that all models are present
5. **Startup Checks**: Validates everything before starting the app

### 3. Access the Application:
```
http://localhost:8001
```

## Local Development Setup

### 1. Install System Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    libx11-dev \
    libatlas-base-dev \
    libgtk-3-dev \
    libboost-python-dev \
    libboost-all-dev \
    python3-dev
```

**macOS:**
```bash
brew install cmake boost boost-python3
```

**Windows:**
- Install Visual Studio Build Tools
- Install CMake
- Or use conda: `conda install -c conda-forge dlib`

### 2. Install Python Dependencies
```bash
cd face_recognition
pip install -r requirements.txt
```

### 3. Download Required Models
```bash
cd face_recognition
python download_models.py
```

### 4. Run with Startup Validation
```bash
cd face_recognition
python startup.py
```

## Manual Model Download

If automatic download fails, you can download manually:

```bash
cd face_recognition
mkdir -p app/models

# Download dlib face landmarks model
curl -L "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2" \
     -o app/models/shape_predictor_68_face_landmarks.dat.bz2

# Extract the model
bzip2 -d app/models/shape_predictor_68_face_landmarks.dat.bz2

# Verify the model
ls -la app/models/shape_predictor_68_face_landmarks.dat
```

## Environment Variables

You can configure model paths using environment variables:

```bash
# dlib face landmarks model path
export FR_DLIB_MODEL_PATH=/path/to/shape_predictor_68_face_landmarks.dat

# Face recognition models directory
export FR_FACE_RECOGNITION_MODELS_PATH=/path/to/models

# Alternative dlib model path
export DLIB_MODEL_PATH=/path/to/shape_predictor_68_face_landmarks.dat
```

## Troubleshooting

### 1. **dlib Compilation Errors**

**Error**: `No module named 'dlib'`
**Solution**: 
```bash
# Install system dependencies first
sudo apt-get install build-essential cmake

# Then install dlib
pip install dlib
```

**Error**: `CMake not found`
**Solution**:
```bash
# Ubuntu/Debian
sudo apt-get install cmake

# macOS
brew install cmake

# Or install via pip
pip install cmake
```

### 2. **Face Landmarks Model Missing**

**Error**: `FileNotFoundError: shape_predictor_68_face_landmarks.dat`
**Solution**:
```bash
cd face_recognition
python download_models.py
```

### 3. **Docker Build Fails**

**Error**: `Package not found` during Docker build
**Solution**: The Dockerfile includes all required dependencies. If it fails:
```bash
# Clean Docker cache and rebuild
docker system prune -a
docker-compose build --no-cache face_recognition
```

### 4. **Memory Issues During Build**

**Error**: `Killed` during dlib compilation
**Solution**: Increase Docker memory allocation or use pre-compiled wheels:
```bash
# In requirements.txt, try:
dlib==19.24.2 --find-links https://pypi.org/simple/
```

### 5. **Model Download Fails**

**Error**: Network issues downloading model
**Solution**: Download manually and place in `app/models/` directory

## File Structure

After setup, your file structure should look like:

```
face_recognition/
├── app/
│   ├── models/
│   │   ├── shape_predictor_68_face_landmarks.dat  # 99.7 MB
│   │   └── README.md
│   ├── main.py
│   ├── config.py
│   └── ...
├── requirements.txt
├── Dockerfile
├── download_models.py
├── startup.py
└── ...
```

## Verification

### Check if Everything is Working:

1. **Run Startup Checks**:
   ```bash
   cd face_recognition
   python startup.py
   ```

2. **Test dlib in Python**:
   ```python
   import dlib
   detector = dlib.get_frontal_face_detector()
   predictor = dlib.shape_predictor("app/models/shape_predictor_68_face_landmarks.dat")
   print("✓ dlib is working correctly")
   ```

3. **Test Face Recognition**:
   ```python
   import face_recognition
   print("✓ face_recognition library is working")
   ```

## Performance Notes

- **dlib compilation** can take 5-15 minutes depending on your system
- **Model download** is ~100MB and happens once
- **Docker build** includes all steps and may take 10-20 minutes first time
- **Subsequent builds** use Docker layer caching and are much faster

## Production Deployment

For production, the Docker setup is recommended as it:
- ✅ Includes all system dependencies
- ✅ Automatically downloads required models
- ✅ Validates everything before starting
- ✅ Provides health checks for monitoring
- ✅ Uses NVIDIA GPU support when available

## Next Steps

1. **Build and test**: `docker-compose up --build face_recognition`
2. **Access the app**: http://localhost:8001
3. **Check logs**: `docker-compose logs face_recognition`
4. **Monitor health**: `docker-compose ps`

The face recognition system should now work without dlib-related errors!
