from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, JSON, Boolean
from sqlalchemy.orm import relationship
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import UploadFile
from modules.face_recognition.database import Base

class WebhookPayload(BaseModel):
    section: str
    event: str
    user_id: int
    camera_id: int
    username: str = None
    camera_name: str = None
    webhooks: bool = True
    whatsapp: bool = True
    sms: bool = True
    email: bool = True
    timestamp: datetime = None
    

