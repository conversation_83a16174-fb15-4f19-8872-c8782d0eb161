import httpx

whatsapp_webhook = 'http://localhost:8080/api/send'

async def trigger_whatsapp_webhook(webhook_url: str, phone_number: str, message: str):
    payload = {
        "phone_number": phone_number,
        "message": message
    }

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(webhook_url, json=payload)
            response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
            return response.json()
        except httpx.HTTPStatusError as exc:
            print(f"HTTP error occurred: {exc.response.status_code} - {exc.response.text}")
        except Exception as e:
            print(f"Error triggering webhook: {e}")
