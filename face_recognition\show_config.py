#!/usr/bin/env python3
"""
Script to display current configuration values.
This script shows all configuration settings and their current values.
"""
import os
import sys
import json
from pathlib import Path

# Add the app to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def print_banner(text):
    """Print a banner with the given text."""
    print("\n" + "="*60)
    print(f" {text}")
    print("="*60)

def print_section(text):
    """Print a section header."""
    print(f"\n--- {text} ---")

def mask_sensitive_value(key, value):
    """Mask sensitive configuration values."""
    sensitive_keys = ['password', 'secret', 'key', 'token']
    
    if any(sensitive in key.lower() for sensitive in sensitive_keys):
        if isinstance(value, str) and len(value) > 4:
            return value[:2] + "*" * (len(value) - 4) + value[-2:]
        else:
            return "***"
    return value

def show_configuration():
    """Display current configuration."""
    try:
        from app.config import settings
        
        print_banner("Face Recognition System Configuration")
        
        # Application Settings
        print_section("Application Settings")
        print(f"  App Name: {settings.APP_NAME}")
        print(f"  Version: {settings.APP_VERSION}")
        print(f"  Debug Mode: {settings.DEBUG}")
        print(f"  Testing Mode: {settings.TESTING}")
        
        # Server Settings
        print_section("Server Settings")
        print(f"  Host: {settings.HOST}")
        print(f"  Port: {settings.PORT}")
        print(f"  Auto-reload: {settings.RELOAD}")
        
        # Database Settings
        print_section("Database Settings")
        print(f"  Driver: {settings.DB_DRIVER}")
        print(f"  Host: {settings.DB_HOST}")
        print(f"  Port: {settings.DB_PORT}")
        print(f"  Database: {settings.DB_NAME}")
        print(f"  Username: {settings.DB_USER}")
        print(f"  Password: {mask_sensitive_value('password', settings.DB_PASSWORD)}")
        print(f"  Echo SQL: {settings.DB_ECHO}")
        print(f"  Database URL: {mask_sensitive_value('url', settings.database_url)}")
        
        # Face Recognition Settings
        print_section("Face Recognition Settings")
        print(f"  Face Threshold: {settings.FACE_THRESHOLD}")
        print(f"  Encoding Model: {settings.FACE_ENCODING_MODEL}")
        print(f"  Model Path: {settings.FACE_MODEL_PATH or 'Auto-detect'}")
        print(f"  Max Face Size: {settings.MAX_FACE_SIZE}px")
        print(f"  Min Face Size: {settings.MIN_FACE_SIZE}px")
        
        # Image Processing Settings
        print_section("Image Processing Settings")
        print(f"  Max Upload Size: {settings.IMAGE_UPLOAD_MAX_SIZE / (1024*1024):.1f}MB")
        print(f"  Allowed Extensions: {', '.join(settings.ALLOWED_IMAGE_EXTENSIONS)}")
        print(f"  Image Quality: {settings.IMAGE_QUALITY}%")
        
        # Storage Settings
        print_section("Storage Settings")
        print(f"  Static Directory: {settings.STATIC_DIR}")
        print(f"  Images Directory: {settings.IMAGES_DIR}")
        print(f"  Cropped Faces Directory: {settings.CROPPED_FACES_DIR}")
        print(f"  Dataset Directory: {settings.DATASET_DIR}")
        print(f"  Unknown Faces Directory: {settings.UNKNOWN_DIR}")
        
        # Camera Settings
        print_section("Camera Settings")
        print(f"  Default Timeout: {settings.DEFAULT_CAMERA_TIMEOUT}s")
        print(f"  Retry Attempts: {settings.CAMERA_RETRY_ATTEMPTS}")
        print(f"  Frame Rate: {settings.FRAME_RATE}fps")
        
        # Qdrant Settings
        print_section("Qdrant Vector Database Settings")
        print(f"  Enabled: {settings.QDRANT_ENABLED}")
        print(f"  Host: {settings.QDRANT_HOST}")
        print(f"  Port: {settings.QDRANT_PORT}")
        print(f"  Collection: {settings.QDRANT_COLLECTION_NAME}")
        print(f"  Vector Size: {settings.QDRANT_VECTOR_SIZE}")
        print(f"  Qdrant URL: {settings.qdrant_url}")
        
        # Security Settings
        print_section("Security Settings")
        print(f"  Secret Key: {mask_sensitive_value('secret', settings.SECRET_KEY)}")
        print(f"  Token Expiry: {settings.ACCESS_TOKEN_EXPIRE_MINUTES} minutes")
        print(f"  CORS Origins: {settings.CORS_ORIGINS}")
        
        # Logging Settings
        print_section("Logging Settings")
        print(f"  Log Level: {settings.LOG_LEVEL}")
        print(f"  Log File: {settings.LOG_FILE or 'Console only'}")
        print(f"  Log Format: {settings.LOG_FORMAT}")
        
        # Performance Settings
        print_section("Performance Settings")
        print(f"  Max Workers: {settings.MAX_WORKERS}")
        print(f"  Batch Size: {settings.BATCH_SIZE}")
        print(f"  Cache TTL: {settings.CACHE_TTL}s")
        
        # Attendance Settings
        print_section("Attendance Settings")
        print(f"  Time Window: {settings.ATTENDANCE_TIME_WINDOW}s")
        print(f"  Duplicate Prevention: {settings.DUPLICATE_ATTENDANCE_PREVENTION}")
        
        # Model Settings
        print_section("Model Settings")
        print(f"  Device: {settings.DEVICE}")
        print(f"  Model Cache Directory: {settings.MODEL_CACHE_DIR}")
        print(f"  Auto-download Models: {settings.DOWNLOAD_MODELS}")
        
        # Environment Variables
        print_section("Environment Variables")
        fr_env_vars = {k: v for k, v in os.environ.items() if k.startswith('FR_')}
        if fr_env_vars:
            for key, value in sorted(fr_env_vars.items()):
                masked_value = mask_sensitive_value(key, value)
                print(f"  {key}: {masked_value}")
        else:
            print("  No FR_ environment variables set")
        
        # Configuration File
        print_section("Configuration File")
        env_file_path = Path(__file__).parent / '.env'
        if env_file_path.exists():
            print(f"  .env file found: {env_file_path}")
            print(f"  .env file size: {env_file_path.stat().st_size} bytes")
        else:
            print("  No .env file found")
        
        # Example .env file
        example_env_path = Path(__file__).parent / '.env.example'
        if example_env_path.exists():
            print(f"  .env.example file found: {example_env_path}")
        else:
            print("  No .env.example file found")
        
        print_banner("Configuration Display Complete")
        
        return True
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_environment_help():
    """Show help for environment variables."""
    print_banner("Environment Variables Help")
    
    print("\nTo configure the Face Recognition System using environment variables:")
    print("\n1. Set environment variables with the FR_ prefix:")
    print("   export FR_DEBUG=true")
    print("   export FR_DB_HOST=localhost")
    print("   export FR_FACE_THRESHOLD=0.8")
    
    print("\n2. Create a .env file in the face_recognition directory:")
    print("   cp .env.example .env")
    print("   # Edit .env with your values")
    
    print("\n3. Use with Docker:")
    print("   docker run -e FR_DEBUG=false -e FR_DB_HOST=db your-app")
    
    print("\n4. Common environment variables:")
    print("   FR_DEBUG=true/false          - Enable debug mode")
    print("   FR_DB_HOST=hostname          - Database host")
    print("   FR_DB_PASSWORD=password      - Database password")
    print("   FR_FACE_THRESHOLD=0.0-1.0    - Face recognition threshold")
    print("   FR_PORT=8000                 - Server port")
    print("   FR_LOG_LEVEL=INFO            - Logging level")
    
    print("\nFor a complete list of variables, see CONFIG_GUIDE.md")

def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_environment_help()
        return 0
    
    success = show_configuration()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
