from fastapi import <PERSON><PERSON><PERSON>
from app.schema import Query
from app.database import db
from openai import OpenAI
from app.config import settings
import asyncio
import httpx
from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.ext.asyncio import AsyncSession


app = FastAPI(title="Attendance Chatbot API")

cursor = db.cursor()
client = OpenAI(api_key = "***********************************************************************************************")

metadata = MetaData()


@app.post("/chat")
def ask_sql_bot(query: Query):
    
    print("got into it")
    question = query.question

    # (1) Prompt LLM to convert NL to SQL
    schema_description = """
    You have access to the following table(s):
    - users(user_id,employee_id, username, email, department, dob, phone_number, address)
    - attendances(id, timestamp, user_id)
    - camera_permissions(id, user_id, camera_id, created_at)
    - unknowns(id, persistent_id, encoding, timestamp)
    """
    prompt = f"{schema_description}\n\nTranslate the following question into a SQL query:\n\nQuestion: {question}\nSQL:"
    
    response = client.chat.completions.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        temperature=0
    )

    sql_query = response.choices[0].message.content
    
    sql_query = response.choices[0].message.content.strip().split(';')[0] + ';'
    print("Generated SQL:", sql_query)

    # (2) Execute the SQL
    try:
        cursor.execute(sql_query)
        result = cursor.fetchall()
        columns = cursor.column_names
        data = [dict(zip(columns, row)) for row in result]
        return {"sql": sql_query, "result": data}
    except Exception as e:
        return {"error": str(e), "sql": sql_query}