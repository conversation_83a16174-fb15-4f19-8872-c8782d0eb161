#!/usr/bin/env python3
"""
Script to download required models for face recognition.
This script downloads the dlib face landmarks model and other required models.
"""
import os
import sys
import urllib.request
import bz2
import shutil
from pathlib import Path

def print_banner(text):
    """Print a banner with the given text."""
    print("\n" + "="*60)
    print(f" {text}")
    print("="*60)

def print_section(text):
    """Print a section header."""
    print(f"\n--- {text} ---")

def download_file(url, destination, description="file"):
    """Download a file with progress indication."""
    print(f"Downloading {description}...")
    print(f"URL: {url}")
    print(f"Destination: {destination}")
    
    try:
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\rProgress: {percent}%", end="", flush=True)
        
        urllib.request.urlretrieve(url, destination, progress_hook)
        print(f"\n✓ Downloaded {description} successfully")
        return True
    except Exception as e:
        print(f"\n✗ Failed to download {description}: {e}")
        return False

def extract_bz2(source_path, destination_path):
    """Extract a bz2 compressed file."""
    print(f"Extracting {source_path}...")
    try:
        with bz2.BZ2File(source_path, 'rb') as source:
            with open(destination_path, 'wb') as dest:
                shutil.copyfileobj(source, dest)
        print(f"✓ Extracted to {destination_path}")
        return True
    except Exception as e:
        print(f"✗ Failed to extract {source_path}: {e}")
        return False

def download_dlib_face_landmarks():
    """Download dlib face landmarks model."""
    print_section("Downloading dlib Face Landmarks Model")
    
    # Define paths
    models_dir = Path("app/models")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    model_url = "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2"
    compressed_path = models_dir / "shape_predictor_68_face_landmarks.dat.bz2"
    extracted_path = models_dir / "shape_predictor_68_face_landmarks.dat"
    
    # Check if model already exists
    if extracted_path.exists():
        file_size = extracted_path.stat().st_size
        print(f"✓ Face landmarks model already exists ({file_size:,} bytes)")
        print(f"  Path: {extracted_path}")
        return True
    
    # Download compressed model
    if not download_file(model_url, compressed_path, "dlib face landmarks model"):
        return False
    
    # Extract the model
    if not extract_bz2(compressed_path, extracted_path):
        return False
    
    # Clean up compressed file
    try:
        compressed_path.unlink()
        print(f"✓ Cleaned up compressed file")
    except Exception as e:
        print(f"⚠ Warning: Could not remove compressed file: {e}")
    
    # Verify the extracted file
    if extracted_path.exists():
        file_size = extracted_path.stat().st_size
        print(f"✓ Face landmarks model ready ({file_size:,} bytes)")
        print(f"  Path: {extracted_path}")
        return True
    else:
        print("✗ Extracted model file not found")
        return False

def download_yolo_model():
    """Download YOLO model if needed."""
    print_section("Checking YOLO Model")
    
    yolo_path = Path("app/yolov8n.pt")
    
    if yolo_path.exists():
        file_size = yolo_path.stat().st_size
        print(f"✓ YOLO model already exists ({file_size:,} bytes)")
        print(f"  Path: {yolo_path}")
        return True
    
    print("⚠ YOLO model not found. It will be downloaded automatically by ultralytics when first used.")
    return True

def verify_models():
    """Verify all required models are present."""
    print_section("Verifying Models")
    
    models_to_check = [
        ("dlib Face Landmarks", "app/models/shape_predictor_68_face_landmarks.dat"),
        ("YOLO Model", "app/yolov8n.pt")
    ]
    
    all_present = True
    
    for model_name, model_path in models_to_check:
        path = Path(model_path)
        if path.exists():
            file_size = path.stat().st_size
            print(f"✓ {model_name}: {path} ({file_size:,} bytes)")
        else:
            print(f"✗ {model_name}: {path} (missing)")
            all_present = False
    
    return all_present

def create_models_info():
    """Create a models info file."""
    print_section("Creating Models Info")
    
    models_dir = Path("app/models")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    info_content = """# Face Recognition Models

This directory contains the required models for face recognition:

## dlib Face Landmarks Model
- **File**: `shape_predictor_68_face_landmarks.dat`
- **Source**: https://github.com/davisking/dlib-models
- **Purpose**: Facial landmark detection for face alignment and feature extraction
- **Size**: ~99.7 MB

## YOLO Model
- **File**: `yolov8n.pt` (in parent directory)
- **Source**: Ultralytics (downloaded automatically)
- **Purpose**: Object detection and face detection
- **Size**: ~6.2 MB

## Usage

These models are automatically loaded by the face recognition system. The paths are configured in the application settings.

## Environment Variables

You can override model paths using environment variables:
- `FR_FACE_MODEL_PATH`: Path to dlib face landmarks model
- `DLIB_MODEL_PATH`: Alternative path for dlib model
- `FACE_RECOGNITION_MODELS_PATH`: Base directory for models

## Manual Download

If you need to download models manually:

```bash
# Download dlib face landmarks model
curl -L "https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2" -o shape_predictor_68_face_landmarks.dat.bz2
bzip2 -d shape_predictor_68_face_landmarks.dat.bz2

# Or use the download script
python download_models.py
```
"""
    
    info_path = models_dir / "README.md"
    try:
        with open(info_path, 'w') as f:
            f.write(info_content)
        print(f"✓ Created models info file: {info_path}")
        return True
    except Exception as e:
        print(f"✗ Failed to create models info file: {e}")
        return False

def main():
    """Main function to download all required models."""
    print_banner("Face Recognition Models Download")
    
    print(f"Working directory: {os.getcwd()}")
    print(f"Python version: {sys.version}")
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"Changed to: {os.getcwd()}")
    
    success_count = 0
    total_tasks = 4
    
    # Download dlib face landmarks model
    if download_dlib_face_landmarks():
        success_count += 1
    
    # Check YOLO model
    if download_yolo_model():
        success_count += 1
    
    # Verify all models
    if verify_models():
        success_count += 1
    
    # Create models info
    if create_models_info():
        success_count += 1
    
    print_banner(f"Model Download Results: {success_count}/{total_tasks} completed")
    
    if success_count == total_tasks:
        print("🎉 All models are ready!")
        print("\nYou can now run the face recognition system:")
        print("1. Docker: docker-compose up")
        print("2. Local: python -m uvicorn app.main:app --reload")
        return 0
    else:
        print("⚠️ Some models may be missing or failed to download.")
        print("Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
