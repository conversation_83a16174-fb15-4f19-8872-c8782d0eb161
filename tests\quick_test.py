#!/usr/bin/env python3
"""
Quick test script to verify basic functionality of the face recognition module.
This script performs basic smoke tests to ensure the module is working.
"""
import os
import sys
import traceback

# Add the face_recognition app to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test basic imports
        import cv2
        import numpy as np
        print("✓ OpenCV and NumPy imported successfully")
        
        # Test face recognition module imports
        from app import models, database, utils
        print("✓ Face recognition modules imported successfully")
        
        # Test specific functions
        from app.utils import crop_face, generate_encoding, calculate_cosine_similarity
        print("✓ Utility functions imported successfully")
        
        # Test routes
        from app.routes import router
        print("✓ Routes imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality without external dependencies."""
    print("\nTesting basic functionality...")
    
    try:
        import numpy as np
        from app.utils import calculate_cosine_similarity, generate_encoding, crop_face
        
        # Test cosine similarity
        vec1 = np.random.rand(128)
        vec2 = vec1.copy()
        similarity = calculate_cosine_similarity(vec1, vec2)
        assert abs(similarity - 1.0) < 1e-6, f"Expected similarity ~1.0, got {similarity}"
        print("✓ Cosine similarity calculation works")
        
        # Test with different vectors
        vec3 = np.random.rand(128)
        similarity2 = calculate_cosine_similarity(vec1, vec3)
        assert -1.0 <= similarity2 <= 1.0, f"Similarity out of range: {similarity2}"
        print("✓ Cosine similarity range validation works")
        
        # Test face encoding with a simple image
        test_img = np.random.randint(0, 255, (160, 160, 3), dtype=np.uint8)
        try:
            encoding = generate_encoding(test_img)
            if encoding is not None:
                assert isinstance(encoding, np.ndarray), "Encoding should be numpy array"
                print("✓ Face encoding generation works")
            else:
                print("⚠ Face encoding returned None (acceptable for test image)")
        except Exception as e:
            print(f"⚠ Face encoding failed (acceptable): {e}")
        
        # Test face cropping
        try:
            cropped = crop_face(test_img)
            if cropped is not None:
                assert isinstance(cropped, np.ndarray), "Cropped face should be numpy array"
                print("✓ Face cropping works")
            else:
                print("⚠ Face cropping returned None (acceptable for test image)")
        except Exception as e:
            print(f"⚠ Face cropping failed (acceptable): {e}")
        
        return True
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_database_models():
    """Test database model creation."""
    print("\nTesting database models...")
    
    try:
        from app.models import User, Image, Encoding, Camera, Attendance
        
        # Test model creation (without database)
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>"
        }
        user = User(**user_data)
        assert user.username == "test_user"
        assert user.email == "<EMAIL>"
        print("✓ User model creation works")
        
        # Test other models
        camera = Camera(name="Test Camera", rtsp_url="rtsp://test.com")
        assert camera.name == "Test Camera"
        print("✓ Camera model creation works")
        
        return True
    except Exception as e:
        print(f"✗ Database model test failed: {e}")
        traceback.print_exc()
        return False

def test_api_routes():
    """Test that API routes can be created."""
    print("\nTesting API routes...")
    
    try:
        from fastapi import FastAPI
        from app.routes import router
        
        app = FastAPI()
        app.include_router(router)
        
        # Check that routes are registered
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/registration", "/users", "/admin"]
        
        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✓ Route {expected_route} registered")
            else:
                print(f"⚠ Route {expected_route} not found")
        
        return True
    except Exception as e:
        print(f"✗ API routes test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all quick tests."""
    print("="*60)
    print(" Face Recognition Module - Quick Test")
    print("="*60)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_database_models,
        test_api_routes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print("\n" + "="*60)
    print(f" Test Results: {passed}/{total} passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All quick tests passed! The face recognition module appears to be working.")
        return 0
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Some minor issues may exist.")
        return 0
    else:
        print("❌ Many tests failed. The module needs significant fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
