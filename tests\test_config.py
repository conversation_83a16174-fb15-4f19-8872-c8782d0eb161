"""
Tests for configuration system with environment variables.
"""
import pytest
import os
import sys
from unittest.mock import patch

# Import the modules to test
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

from app.config import Settings


class TestConfiguration:
    """Test class for configuration system."""
    
    def test_default_values(self):
        """Test that default values are loaded correctly."""
        settings = Settings()
        
        # Test application defaults
        assert settings.APP_NAME == "Face Recognition System"
        assert settings.APP_VERSION == "1.0.0"
        assert settings.DEBUG == False
        assert settings.TESTING == False
        
        # Test server defaults
        assert settings.HOST == "0.0.0.0"
        assert settings.PORT == 8000
        assert settings.RELOAD == False
        
        # Test database defaults
        assert settings.DB_USER == "root"
        assert settings.DB_HOST == "localhost"
        assert settings.DB_NAME == "face_recognition_db"
        assert settings.DB_PORT == 3306
        assert settings.DB_DRIVER == "mysql+pymysql"
        
        # Test face recognition defaults
        assert settings.FACE_THRESHOLD == 0.7
        assert settings.FACE_ENCODING_MODEL == "facenet"
        assert settings.MAX_FACE_SIZE == 500
        assert settings.MIN_FACE_SIZE == 50
    
    def test_environment_variable_override(self):
        """Test that environment variables override defaults."""
        env_vars = {
            'FR_APP_NAME': 'Test App',
            'FR_DEBUG': 'true',
            'FR_PORT': '9000',
            'FR_DB_HOST': 'test-db',
            'FR_FACE_THRESHOLD': '0.8',
            'FR_MAX_WORKERS': '8'
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert settings.APP_NAME == "Test App"
            assert settings.DEBUG == True
            assert settings.PORT == 9000
            assert settings.DB_HOST == "test-db"
            assert settings.FACE_THRESHOLD == 0.8
            assert settings.MAX_WORKERS == 8
    
    def test_boolean_environment_variables(self):
        """Test boolean environment variable parsing."""
        # Test true values
        true_values = ['true', 'True', 'TRUE', '1', 'yes', 'on']
        for value in true_values:
            with patch.dict(os.environ, {'FR_DEBUG': value}):
                settings = Settings()
                assert settings.DEBUG == True, f"Failed for value: {value}"
        
        # Test false values
        false_values = ['false', 'False', 'FALSE', '0', 'no', 'off', '']
        for value in false_values:
            with patch.dict(os.environ, {'FR_DEBUG': value}):
                settings = Settings()
                assert settings.DEBUG == False, f"Failed for value: {value}"
    
    def test_list_environment_variables(self):
        """Test list environment variable parsing."""
        with patch.dict(os.environ, {'FR_ALLOWED_IMAGE_EXTENSIONS': '[".jpg", ".png", ".gif"]'}):
            settings = Settings()
            assert settings.ALLOWED_IMAGE_EXTENSIONS == [".jpg", ".png", ".gif"]
        
        with patch.dict(os.environ, {'FR_CORS_ORIGINS': '["https://example.com", "https://test.com"]'}):
            settings = Settings()
            assert settings.CORS_ORIGINS == ["https://example.com", "https://test.com"]
    
    def test_validation_constraints(self):
        """Test that validation constraints work."""
        # Test face threshold range validation
        with patch.dict(os.environ, {'FR_FACE_THRESHOLD': '1.5'}):
            with pytest.raises(ValueError):
                Settings()
        
        with patch.dict(os.environ, {'FR_FACE_THRESHOLD': '-0.1'}):
            with pytest.raises(ValueError):
                Settings()
        
        # Test image quality range validation
        with patch.dict(os.environ, {'FR_IMAGE_QUALITY': '101'}):
            with pytest.raises(ValueError):
                Settings()
        
        with patch.dict(os.environ, {'FR_IMAGE_QUALITY': '0'}):
            with pytest.raises(ValueError):
                Settings()
    
    def test_database_url_property(self):
        """Test database URL generation."""
        env_vars = {
            'FR_DB_DRIVER': 'postgresql',
            'FR_DB_USER': 'testuser',
            'FR_DB_PASSWORD': 'testpass',
            'FR_DB_HOST': 'testhost',
            'FR_DB_PORT': '5432',
            'FR_DB_NAME': 'testdb'
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            expected_url = "********************************************/testdb"
            assert settings.database_url == expected_url
    
    def test_qdrant_url_property(self):
        """Test Qdrant URL generation."""
        env_vars = {
            'FR_QDRANT_HOST': 'qdrant-server',
            'FR_QDRANT_PORT': '6334'
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            expected_url = "http://qdrant-server:6334"
            assert settings.qdrant_url == expected_url
    
    def test_path_helper_methods(self):
        """Test path helper methods."""
        settings = Settings()
        
        # Test image upload path
        image_path = settings.get_image_upload_path("test.jpg")
        assert image_path == os.path.join(settings.IMAGES_DIR, "test.jpg")
        
        # Test cropped face path
        cropped_path = settings.get_cropped_face_path("cropped_test.jpg")
        assert cropped_path == os.path.join(settings.CROPPED_FACES_DIR, "cropped_test.jpg")
        
        # Test unknown face path
        unknown_path = settings.get_unknown_face_path("unknown_test.jpg")
        assert unknown_path == os.path.join(settings.UNKNOWN_DIR, "unknown_test.jpg")
    
    @patch('os.makedirs')
    def test_ensure_directories(self, mock_makedirs):
        """Test directory creation."""
        settings = Settings()
        settings.ensure_directories()
        
        # Check that makedirs was called for each directory
        expected_dirs = [
            settings.STATIC_DIR,
            settings.IMAGES_DIR,
            settings.CROPPED_FACES_DIR,
            settings.DATASET_DIR,
            settings.UNKNOWN_DIR,
            settings.MODEL_CACHE_DIR
        ]
        
        assert mock_makedirs.call_count == len(expected_dirs)
        
        # Check that each directory was created with exist_ok=True
        for call in mock_makedirs.call_args_list:
            args, kwargs = call
            assert kwargs.get('exist_ok') == True
    
    def test_case_insensitive_env_vars(self):
        """Test that environment variables are case insensitive."""
        env_vars = {
            'fr_app_name': 'Test App Lower',  # lowercase
            'FR_DEBUG': 'true',               # uppercase
            'Fr_Port': '9001'                 # mixed case
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert settings.APP_NAME == "Test App Lower"
            assert settings.DEBUG == True
            assert settings.PORT == 9001
    
    def test_env_prefix_requirement(self):
        """Test that only FR_ prefixed variables are used."""
        env_vars = {
            'APP_NAME': 'Should Not Be Used',  # No FR_ prefix
            'FR_APP_NAME': 'Should Be Used',   # Correct prefix
            'OTHER_DEBUG': 'true',             # Wrong prefix
            'FR_DEBUG': 'false'                # Correct prefix
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert settings.APP_NAME == "Should Be Used"
            assert settings.DEBUG == False
    
    def test_extra_env_vars_ignored(self):
        """Test that extra environment variables are ignored."""
        env_vars = {
            'FR_NONEXISTENT_SETTING': 'should be ignored',
            'FR_ANOTHER_FAKE_SETTING': 'also ignored',
            'FR_APP_NAME': 'Test App'
        }
        
        with patch.dict(os.environ, env_vars):
            # Should not raise an error
            settings = Settings()
            assert settings.APP_NAME == "Test App"
            
            # Extra variables should not be accessible
            assert not hasattr(settings, 'NONEXISTENT_SETTING')
            assert not hasattr(settings, 'ANOTHER_FAKE_SETTING')
    
    def test_type_conversion(self):
        """Test automatic type conversion from environment variables."""
        env_vars = {
            'FR_PORT': '8080',                    # string to int
            'FR_DEBUG': 'true',                   # string to bool
            'FR_FACE_THRESHOLD': '0.85',          # string to float
            'FR_MAX_WORKERS': '6',                # string to int
            'FR_APP_NAME': 'String Value'         # string to string
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            
            assert isinstance(settings.PORT, int)
            assert settings.PORT == 8080
            
            assert isinstance(settings.DEBUG, bool)
            assert settings.DEBUG == True
            
            assert isinstance(settings.FACE_THRESHOLD, float)
            assert settings.FACE_THRESHOLD == 0.85
            
            assert isinstance(settings.MAX_WORKERS, int)
            assert settings.MAX_WORKERS == 6
            
            assert isinstance(settings.APP_NAME, str)
            assert settings.APP_NAME == "String Value"
    
    def test_optional_fields(self):
        """Test optional configuration fields."""
        settings = Settings()
        
        # These should be None by default
        assert settings.FACE_MODEL_PATH is None
        assert settings.LOG_FILE is None
        
        # Test setting optional fields via environment
        env_vars = {
            'FR_FACE_MODEL_PATH': '/path/to/model.pt',
            'FR_LOG_FILE': '/var/log/face_recognition.log'
        }
        
        with patch.dict(os.environ, env_vars):
            settings = Settings()
            assert settings.FACE_MODEL_PATH == '/path/to/model.pt'
            assert settings.LOG_FILE == '/var/log/face_recognition.log'
    
    def test_configuration_documentation(self):
        """Test that all fields have descriptions."""
        settings = Settings()
        
        # Get all field info
        field_info = settings.__fields__
        
        # Check that each field has a description
        for field_name, field in field_info.items():
            assert hasattr(field, 'field_info')
            assert field.field_info.description is not None
            assert len(field.field_info.description) > 0
            print(f"✓ {field_name}: {field.field_info.description}")


if __name__ == "__main__":
    pytest.main([__file__])
