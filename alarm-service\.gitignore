# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
*.env

# PyInstaller
#  Usually contains pyc files and other temporary files
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Pyre type checker
.pyre/

# mypy
.mypy_cache/

# PyCharm
.idea/

# VSCode
.vscode/

# Local settings
*.local
*.db
*.sqlite3

# Jupyter Notebook
.ipynb_checkpoints

# MacOS
.DS_Store

# Logs
*.log
