import cv2
import threading
import numpy as np
import concurrent.futures
from ultralytics import YOLO

class HelmetDetection:
    def __init__(self, seg_model, pose_model, seg_conf_value=0.3, pose_conf_value=0.3, iou_threshold=0.3):
        self.seg_conf_value = seg_conf_value
        self.pose_conf_value = pose_conf_value
        self.iou_threshold = iou_threshold
        self.seg_model = YOLO(seg_model,task ="segment")  # 1:gloves, 2:helmet, 3:person, 5:vest
        self.pose_model = YOLO(pose_model,task= "pose")
        self.threads = []
        self.running = False
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (640, 480))

    def start(self, camera_details, test_video_path=None):
        self.person_count, self.no_helmet_count, self.no_vest_count, self.no_gloves_count = 0, 0, 0, 0
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0], cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.helmet_frames = [None] * len(self.cap_devices)
        self.warning_count = [{"person_count": 0, "no_helmet_count": 0, "no_vest_count": 0, "no_gloves_count": 0} for _ in range(len(self.cap_devices))]

        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(self.blank, f"{self.camera_name[idx]} is Offline", (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".png", temp)
                    self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        # for thread in self.threads:
            # thread.join()
    
    def calculate_iou(self, mask1, mask2):
        """Calculate IoU between two binary masks"""
        intersection = np.logical_and(mask1, mask2).sum()
        union = np.logical_or(mask1, mask2).sum()
        return intersection / union if union > 0 else 0
    
    def get_keypoint_region_mask(self, keypoints, region_type, frame_shape):
        """Create a binary mask around specific keypoints based on region type"""
        mask = np.zeros(frame_shape[:2], dtype=np.uint8)
        
        if region_type == "head":
            # Head region (keypoints 0-4: nose, eyes, ears)
            valid_points = []
            for i in range(min(5, len(keypoints))):
                # Only include points that are not (0,0) and have actual values
                if keypoints[i][0] > 0 and keypoints[i][1] > 0:
                    valid_points.append(keypoints[i])
            
            if len(valid_points) >= 2:  # Need at least 2 valid points to form a region
                points = np.array(valid_points)
                hull = cv2.convexHull(points.astype(np.int32))
                cv2.fillConvexPoly(mask, hull, 255)
                # Expand the region more for helmets
                mask = cv2.dilate(mask, np.ones((25, 25), np.uint8), iterations=2)
                
        elif region_type == "torso":
            # Torso region (keypoints 5,6,11,12: shoulders and hips)
            # Check which points of the torso are valid
            torso_indices = [5, 6, 11, 12]
            valid_points = []
            for idx in torso_indices:
                if idx < len(keypoints) and keypoints[idx][0] > 0 and keypoints[idx][1] > 0:
                    valid_points.append(keypoints[idx])
            
            if len(valid_points) >= 3:  # Need at least 3 valid points to form a polygon
                cv2.fillConvexPoly(mask, np.array(valid_points).astype(np.int32), 255)
                # Expand the region slightly
                mask = cv2.dilate(mask, np.ones((20, 20), np.uint8), iterations=1)
                
        elif region_type == "hands":
            # Hands (keypoints 9,10: wrists)
            wrist_indices = [9, 10]
            for idx in wrist_indices:
                if idx < len(keypoints) and keypoints[idx][0] > 0 and keypoints[idx][1] > 0:
                    cv2.circle(mask, (int(keypoints[idx][0]), int(keypoints[idx][1])), 30, 255, -1)
                
        return mask
    
    def calculate_bbox_iou(self, bbox1, bbox2):

        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2
        
        # Calculate intersection area
        x_left = max(x1_min, x2_min)
        y_top = max(y1_min, y2_min)
        x_right = min(x1_max, x2_max)
        y_bottom = min(y1_max, y2_max)
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection_area = (x_right - x_left) * (y_bottom - y_top)
        
        # Calculate union area
        bbox1_area = (x1_max - x1_min) * (y1_max - y1_min)
        bbox2_area = (x2_max - x2_min) * (y2_max - y2_min)
        union_area = bbox1_area + bbox2_area - intersection_area
        
        # Calculate IoU
        iou = intersection_area / union_area if union_area > 0 else 0
        
        return iou


    def assign_masks_to_person(self, pose_list, seg_classes):
        person_compliance = []
        
        # Define class IDs
        PERSON_ID, HELMET_ID, GLOVES_ID, VEST_ID = 3, 2, 1, 5
        
        # Extract all person masks from segmentation data
        person_masks = []
        for mask, cls_id, conf, bbox in seg_classes:
            if cls_id == PERSON_ID:
                person_masks.append((mask, conf, bbox))
        
        # For each detected person from pose detection
        for person_idx, (keypoints, pose_bbox) in enumerate(pose_list):
            # Skip if keypoints are invalid
            if not np.any(keypoints) or all(kp[0] <= 0 or kp[1] <= 0 for kp in keypoints):
                continue
            
            # First, associate this pose with the best matching person mask
            best_person_mask = None
            best_person_bbox = None
            best_match_score = 0
            
            for person_mask, person_conf, person_bbox in person_masks:
                # Calculate IoU between pose bbox and segmentation bbox
                bbox_iou = self.calculate_bbox_iou(pose_bbox, person_bbox)
                
                # Count keypoints within the mask
                mask_height, mask_width = person_mask.shape[:2]
                keypoints_in_mask = 0
                valid_keypoints = 0
                
                for kp in keypoints:
                    x, y = int(kp[0]), int(kp[1])
                    if x <= 0 or y <= 0:  # Skip invalid keypoints
                        continue
                    
                    valid_keypoints += 1
                    if 0 <= x < mask_width and 0 <= y < mask_height and person_mask[y, x] > 0:
                        keypoints_in_mask += 1
                
                # Skip if no valid keypoints
                if valid_keypoints == 0:
                    continue
                
                # Calculate match score: weighted combination of bbox IoU and keypoint overlap
                keypoint_ratio = keypoints_in_mask / valid_keypoints
                match_score = bbox_iou * 0.5 + keypoint_ratio * 0.5
                
                # Keep track of best match
                if match_score > best_match_score:
                    best_match_score = match_score
                    best_person_mask = person_mask
                    best_person_bbox = person_bbox
            
            # If no good person mask match found, skip this person
            if best_person_mask is None or best_match_score < 0.3:
                continue
            
            # Now that we have matched the person to their mask, check for PPE
            # Create body region masks using keypoints
            head_region = self.get_keypoint_region_mask(keypoints, "head", best_person_mask.shape)
            torso_region = self.get_keypoint_region_mask(keypoints, "torso", best_person_mask.shape)
            hands_region = self.get_keypoint_region_mask(keypoints, "hands", best_person_mask.shape)
            
            # Initialize PPE detection results
            ppe_results = {
                "helmet": {"worn": False, "iou": 0, "conf": 0, "keypoint_match": 0},
                "vest": {"worn": False, "iou": 0, "conf": 0, "keypoint_match": 0},
                "gloves": {"worn": False, "iou": 0, "conf": 0, "keypoint_match": 0}
            }
            
            # Check each PPE item
            for mask, cls_id, conf, bbox in seg_classes:
                # For each PPE type, we'll calculate:
                # 1. IoU with the appropriate body region
                # 2. Keypoint-based verification score
                # 3. Combined weighted score
                
                if cls_id == HELMET_ID:
                    # First check: IoU with head region mask
                    if np.any(head_region):
                        iou = self.calculate_iou(mask, head_region)
                        
                        # Second check: Position relative to head keypoints
                        keypoint_match = self.verify_helmet_position(keypoints, bbox)
                        
                        # Combined score
                        combined_score = iou * 0.4 + keypoint_match * 0.3 + conf * 0.3
                        
                        if combined_score > 0.35 and combined_score > ppe_results["helmet"]["iou"]:
                            ppe_results["helmet"] = {
                                "worn": True,
                                "iou": iou,
                                "conf": conf,
                                "keypoint_match": keypoint_match
                            }
                
                elif cls_id == VEST_ID:
                    # First check: IoU with torso region mask
                    if np.any(torso_region):
                        iou = self.calculate_iou(mask, torso_region)
                        
                        # Second check: Position relative to shoulder and hip keypoints
                        keypoint_match = self.verify_vest_position(keypoints, bbox)
                        
                        # Combined score
                        combined_score = iou * 0.4 + keypoint_match * 0.3 + conf * 0.3
                        
                        if combined_score > 0.35 and combined_score > ppe_results["vest"]["iou"]:
                            ppe_results["vest"] = {
                                "worn": True,
                                "iou": iou,
                                "conf": conf,
                                "keypoint_match": keypoint_match
                            }
                
                elif cls_id == GLOVES_ID:
                    # First check: IoU with hands region mask
                    if np.any(hands_region):
                        iou = self.calculate_iou(mask, hands_region)
                        
                        # Second check: Position relative to wrist keypoints
                        keypoint_match = self.verify_gloves_position(keypoints, bbox)
                        
                        # Combined score
                        combined_score = iou * 0.4 + keypoint_match * 0.3 + conf * 0.3
                        
                        if combined_score > 0.35 and combined_score > ppe_results["gloves"]["iou"]:
                            ppe_results["gloves"] = {
                                "worn": True,
                                "iou": iou,
                                "conf": conf,
                                "keypoint_match": keypoint_match
                            }
            
            # Add the compliance result for this person
            compliance_status = {
                "bbox": best_person_bbox,
                "helmet": ppe_results["helmet"]["worn"],
                "vest": ppe_results["vest"]["worn"],
                "gloves": ppe_results["gloves"]["worn"],
                "iou_scores": {
                    "helmet": ppe_results["helmet"]["iou"],
                    "vest": ppe_results["vest"]["iou"],
                    "gloves": ppe_results["gloves"]["iou"]
                },
                "conf_scores": {
                    "helmet": ppe_results["helmet"]["conf"],
                    "vest": ppe_results["vest"]["conf"],
                    "gloves": ppe_results["gloves"]["conf"]
                }
            }
            person_compliance.append(compliance_status)
        
        return person_compliance

    def verify_helmet_position(self, keypoints, helmet_bbox):
        """Verify helmet position based on head keypoints (0-4: nose, eyes, ears)"""
        # Extract head keypoints and helmet bbox
        head_keypoints = keypoints[:5]
        x1, y1, x2, y2 = helmet_bbox
        
        # Count how many head keypoints fall within the helmet bbox
        valid_keypoints = 0
        keypoints_in_bbox = 0
        
        for kp in head_keypoints:
            x, y = int(kp[0]), int(kp[1])
            if x <= 0 or y <= 0:  # Skip invalid keypoints
                continue
            
            valid_keypoints += 1
            if x1 <= x <= x2 and y1 <= y <= y2:
                keypoints_in_bbox += 1
        
        # Calculate match score
        if valid_keypoints == 0:
            return 0
        
        # Also check if helmet is above the nose
        nose_x, nose_y = keypoints[0]
        if nose_y > 0 and y1 < nose_y:  # Helmet should be above nose
            above_nose_bonus = 0.3
        else:
            above_nose_bonus = 0
        
        return (keypoints_in_bbox / valid_keypoints) * 0.7 + above_nose_bonus

    def verify_vest_position(self, keypoints, vest_bbox):
        """Verify vest position based on shoulder and hip keypoints (5,6,11,12)"""
        # Torso keypoints: shoulders (5,6) and hips (11,12)
        torso_indices = [5, 6, 11, 12]
        torso_keypoints = [keypoints[i] for i in torso_indices if i < len(keypoints)]
        
        x1, y1, x2, y2 = vest_bbox
        
        # Count torso keypoints within vest bbox
        valid_keypoints = 0
        keypoints_in_bbox = 0
        
        for kp in torso_keypoints:
            x, y = int(kp[0]), int(kp[1])
            if x <= 0 or y <= 0:  # Skip invalid keypoints
                continue
            
            valid_keypoints += 1
            if x1 <= x <= x2 and y1 <= y <= y2:
                keypoints_in_bbox += 1
        
        # Calculate match score
        if valid_keypoints == 0:
            return 0
        
        return keypoints_in_bbox / valid_keypoints

    def verify_gloves_position(self, keypoints, gloves_bbox):
        """Verify gloves position based on wrist keypoints (9,10)"""
        # Wrist keypoints (9,10)
        wrist_indices = [9, 10]
        wrist_keypoints = [keypoints[i] for i in wrist_indices if i < len(keypoints)]
        
        x1, y1, x2, y2 = gloves_bbox
        
        # For gloves, check if they're near wrists
        valid_keypoints = 0
        keypoints_near_bbox = 0
        
        for kp in wrist_keypoints:
            x, y = int(kp[0]), int(kp[1])
            if x <= 0 or y <= 0:  # Skip invalid keypoints
                continue
            
            valid_keypoints += 1
            
            # For gloves, we'll be more lenient - check if within or very close to bbox
            # Calculate distance to bbox center
            bbox_center_x = (x1 + x2) / 2
            bbox_center_y = (y1 + y2) / 2
            distance = np.sqrt((x - bbox_center_x)**2 + (y - bbox_center_y)**2)
            
            # If within bbox or close to it (within half the bbox width)
            bbox_width = x2 - x1
            if (x1 <= x <= x2 and y1 <= y <= y2) or distance < bbox_width / 2:
                keypoints_near_bbox += 1
        
        # Calculate match score
        if valid_keypoints == 0:
            return 0
        
        return keypoints_near_bbox / valid_keypoints

    def process_frame(self, frame):
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_seg = executor.submit(self.seg_model, frame,classes = [1,2,3,5])  # 1:gloves, 2:helmet, 3:person, 5:vest
            future_poses = executor.submit(self.pose_model, frame)
            
            seg_results = future_seg.result()
            pose_results = future_poses.result()
        

        seg_classes = []
        for result in seg_results:
            if result.masks is not None:
                masks = result.masks.data.cpu().numpy()
                clss  = result.boxes.cls.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                boxes = result.boxes.xyxy.cpu().numpy()
                for idx, (mask, cls, conf) in enumerate(zip(masks, clss, confs)):
                    if conf > self.seg_conf_value:
                        x1, y1, x2, y2 = map(int, boxes[idx])

                        binary_mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                        resized_mask = cv2.resize(mask, (frame.shape[1], frame.shape[0]))
                        binary_mask[resized_mask > 0.5] = 255
                        seg_classes.append((binary_mask, int(cls), conf, (x1, y1, x2, y2)))
            

        pose_list = []
        for pose_result in pose_results:
            if pose_result.keypoints is not None:
                for kps, conf, pose_box in zip(pose_result.keypoints.xy.cpu().numpy(), pose_result.boxes.conf,pose_result.boxes.xyxy.cpu().numpy()):
                    if conf > self.pose_conf_value:
                        x1, y1, x2, y2 = map(int, pose_box)
                        # Skip if bounding box is invalid
                        if x1 >= x2 or y1 >= y2:
                            continue
                        pose_list.append((kps,(x1, y1, x2, y2)))
        
        return seg_classes, pose_list

    def update(self, idx, cap, test_video_path=None):
        frame_counter, skip_frames = 0, 2
        while self.running:
            ret, self.frame = cap.read()
            
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            self.frame = cv2.resize(self.frame, (640, 480), interpolation=cv2.INTER_AREA)

            seg_classes, pose_list = self.process_frame(self.frame)
            
   
            # person_masks = [mask for mask, cls_id, _, _ in seg_classes if cls_id == 3]  # Person class ID = 3
            
            # Associate masks with keypoints and validate PPE
            compliance_results = self.assign_masks_to_person(pose_list, seg_classes)


            # print(compliance_results)

            # Store counts
            self.person_count = len(compliance_results)
            self.no_helmet_count = sum(1 for person in compliance_results if not person["helmet"])
            self.no_vest_count = sum(1 for person in compliance_results if not person["vest"])
            self.no_gloves_count = sum(1 for person in compliance_results if not person["gloves"])


            self.warning_count[idx] = {
                "person_count": self.person_count,
                "no_helmet_count": self.no_helmet_count,
                "no_vest_count": self.no_vest_count,
                "no_gloves_count": self.no_gloves_count
            }

            # Visualization (if needed)
            # for result in compliance_results:
            #     x1, y1, x2, y2 = result["bbox"]
                
            #     # Create label text
            #     label = []
            #     if result["helmet"]:
            #         label.append(f"Helmet: {result['iou_scores']['helmet']:.2f}")
            #     else:
            #         label.append("No Helmet")
                    
            #     if result["vest"]:
            #         label.append(f"Vest: {result['iou_scores']['vest']:.2f}")
            #     else:
            #         label.append("No Vest")
                    
            #     if result["gloves"]:
            #         label.append(f"Gloves: {result['iou_scores']['gloves']:.2f}")
            #     else:
            #         label.append("No Gloves")
                
            #     text = " | ".join(label)
            #     color = (0, 255, 0) if all([result["helmet"], result["vest"], result["gloves"]]) else (0, 0, 255)
                
            #     cv2.rectangle(self.frame, (x1, y1), (x2, y2), color, 2)
            #     y_offset = y1 - 10
            #     for idx, item in enumerate(label):
            #         y_pos = y_offset - (idx * 20)
            #         cv2.putText(self.frame, item, (x1, y_pos), 
            #                     cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Encode frame for web display
            _, buffer = cv2.imencode(".png", self.frame)
            self.helmet_frames[idx] = buffer.tobytes()

            if not self.running:
                break

