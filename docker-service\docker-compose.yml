version: "3.9"

services:
  mysql:
    image: mysql:8.0
    container_name: mysql_db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: surveillance_system
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant
    container_name: qdrant_db
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  face_recognition:
    build:
      context: ../face_recognition
    container_name: face_recognition_app
    runtime: nvidia
    ports:
      - "8001:8000"
    command: >
      bash -c "
        echo 'Setting up face recognition models...' &&
        mkdir -p app/models &&
        if [ ! -f app/models/shape_predictor_68_face_landmarks.dat ]; then
          echo 'Downloading dlib face landmarks model...' &&
          curl -L 'https://github.com/davisking/dlib-models/raw/master/shape_predictor_68_face_landmarks.dat.bz2' -o app/models/shape_predictor_68_face_landmarks.dat.bz2 &&
          bzip2 -d app/models/shape_predictor_68_face_landmarks.dat.bz2 &&
          echo 'Model downloaded and extracted successfully'
        else
          echo 'Model already exists'
        fi &&
        echo 'Starting face recognition application...' &&
        python startup.py
      "
    environment:
      # Face Recognition Database (using FR_ prefix for new config system)
      - FR_DB_USER=root
      - FR_DB_PASSWORD=root
      - FR_DB_HOST=mysql
      - FR_DB_NAME=face_recognition_db
      - FR_DB_PORT=3306
      # Authentication/Surveillance System Database
      - FR_AUTH_DB_USER=root
      - FR_AUTH_DB_PASSWORD=root
      - FR_AUTH_DB_HOST=mysql
      - FR_AUTH_DB_NAME=surveillance_system
      - FR_AUTH_DB_PORT=3306
      # Qdrant Vector Database
      - FR_QDRANT_HOST=qdrant
      - FR_QDRANT_PORT=6333
      - FR_QDRANT_ENABLED=true
      # Server Settings
      - FR_HOST=0.0.0.0
      - FR_PORT=8000
      - FR_DEBUG=false
      # Performance Settings
      - FR_DEVICE=cuda
      - FR_MAX_WORKERS=4
      # Model Paths
      - FR_DLIB_MODEL_PATH=/app/app/models/shape_predictor_68_face_landmarks.dat
      - FACE_RECOGNITION_MODELS_PATH=/app/app/models
      # Legacy environment variables (for backward compatibility)
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_HOST=mysql
      - DB_NAME=face_recognition_db
      - DB_PORT=3306
      - AUTH_DB_USER=root
      - AUTH_DB_PASSWORD=root
      - AUTH_DB_HOST=mysql
      - AUTH_DB_NAME=surveillance_system
      - AUTH_DB_PORT=3306
    depends_on:
      mysql:
        condition: service_healthy
      qdrant:
        condition: service_started
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  alarm_service:
    build:
      context: ../alarm-service
    container_name: alarm_service_app
    ports:
      - "8000:8000"
    environment:
      # These environment variables will be available to all modules in the alarm service
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_HOST=mysql
      - DB_PORT=3306
    depends_on:
      mysql:
        condition: service_healthy
      face_recognition:
        condition: service_started
    restart: unless-stopped

volumes:
  mysql_data:
  qdrant_data:








  # crowd_detection:
  #   build:
  #     context: ../crowd_detection
  #   container_name: crowd_detection_app
  #   ports:
  #     - "8002:8000"
  #   depends_on:
  #     - mysql
  #     - qdrant
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: all
  #             capabilities: [gpu]

  # helmet_detection:
  #   build:
  #     context: ../helmet_detection
  #   container_name: helmet_detection_app
  #   ports:
  #     - "8003:8000"
  #   depends_on:
  #     - mysql
  #     - qdrant
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: all
  #             capabilities: [gpu]

  # quality_control:
  #   build:
  #     context: ../quality_control
  #   container_name: quality_control_app
  #   ports:
  #     - "8004:8000"
  #   depends_on:
  #     - mysql
  #     - qdrant
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: all
  #             capabilities: [gpu]
