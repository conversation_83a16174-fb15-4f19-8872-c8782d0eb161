# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
*.md
!README.md

# Test files
tests/
test_*.py
*_test.py

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Development files
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup

# Large model files that will be downloaded
# (uncomment if you want to download them in Docker instead of copying)
# app/models/*.dat
# app/models/*.pt
# *.pt

# Dataset files (usually too large for Docker context)
Dataset/
dataset/
data/

# Static files that can be regenerated
static/images/
cropped_faces/

# Migration files (can be problematic in containers)
# migrations/

# Local configuration files
.env.local
.env.development
.env.production

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore
