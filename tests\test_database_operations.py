"""
Tests for database operations in the face recognition module.
"""
import pytest
import os
import sys
from datetime import datetime, timezone

# Import the modules to test
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

from app.models import User, Image, Encoding, Camera, Attendance, Unknown, UnknownAttendance


class TestDatabaseOperations:
    """Test class for database operations."""
    
    def test_user_model_creation(self, test_db_session, test_user_data):
        """Test creating a User model instance."""
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        assert user.user_id is not None
        assert user.username == test_user_data["username"]
        assert user.email == test_user_data["email"]
        assert user.employee_id == test_user_data["employee_id"]
        assert user.department == test_user_data["department"]
        assert user.dob == test_user_data["dob"]
        assert user.address == test_user_data["address"]
        assert user.phone_number == test_user_data["phone_number"]
    
    def test_user_model_minimal_data(self, test_db_session):
        """Test creating a User with minimal required data."""
        user = User(username="minimal_user", email="<EMAIL>")
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        assert user.user_id is not None
        assert user.username == "minimal_user"
        assert user.email == "<EMAIL>"
        assert user.employee_id is None
        assert user.department is None
    
    def test_image_model_creation(self, test_db_session, test_user_data):
        """Test creating an Image model instance."""
        # Create user first
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create image
        image = Image(
            filename="test_image.jpg",
            image_url="static/images/test_image.jpg",
            user_id=user.user_id
        )
        test_db_session.add(image)
        test_db_session.commit()
        test_db_session.refresh(image)
        
        assert image.id is not None
        assert image.filename == "test_image.jpg"
        assert image.image_url == "static/images/test_image.jpg"
        assert image.user_id == user.user_id
    
    def test_encoding_model_creation(self, test_db_session, test_user_data, test_face_encoding):
        """Test creating an Encoding model instance."""
        # Create user first
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create image
        image = Image(
            filename="test_image.jpg",
            image_url="static/images/test_image.jpg",
            user_id=user.user_id
        )
        test_db_session.add(image)
        test_db_session.commit()
        test_db_session.refresh(image)
        
        # Create encoding
        encoding = Encoding(
            encoding=test_face_encoding,
            user_id=user.user_id,
            image_id=image.id
        )
        test_db_session.add(encoding)
        test_db_session.commit()
        test_db_session.refresh(encoding)
        
        assert encoding.id is not None
        assert encoding.encoding == test_face_encoding
        assert encoding.user_id == user.user_id
        assert encoding.image_id == image.id
    
    def test_camera_model_creation(self, test_db_session, sample_camera_data):
        """Test creating a Camera model instance."""
        camera = Camera(
            name=sample_camera_data["cameraName"],
            rtsp_url=sample_camera_data["rtspUrl"]
        )
        test_db_session.add(camera)
        test_db_session.commit()
        test_db_session.refresh(camera)
        
        assert camera.id is not None
        assert camera.name == sample_camera_data["cameraName"]
        assert camera.rtsp_url == sample_camera_data["rtspUrl"]
    
    def test_attendance_model_creation(self, test_db_session, test_user_data):
        """Test creating an Attendance model instance."""
        # Create user first
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create attendance
        attendance = Attendance(
            user_id=user.user_id,
            camera_name="Test Camera"
        )
        test_db_session.add(attendance)
        test_db_session.commit()
        test_db_session.refresh(attendance)
        
        assert attendance.id is not None
        assert attendance.user_id == user.user_id
        assert attendance.camera_name == "Test Camera"
        assert attendance.timestamp is not None
    
    def test_unknown_model_creation(self, test_db_session, test_face_encoding):
        """Test creating an Unknown model instance."""
        unknown = Unknown(
            persistent_id="unknown_123",
            encoding=test_face_encoding,
            image_path="Dataset/unknown/unknown_123_1234567890.jpg"
        )
        test_db_session.add(unknown)
        test_db_session.commit()
        test_db_session.refresh(unknown)
        
        assert unknown.id is not None
        assert unknown.persistent_id == "unknown_123"
        assert unknown.encoding == test_face_encoding
        assert unknown.image_path == "Dataset/unknown/unknown_123_1234567890.jpg"
    
    def test_unknown_attendance_model_creation(self, test_db_session, test_face_encoding):
        """Test creating an UnknownAttendance model instance."""
        # Create unknown person first
        unknown = Unknown(
            persistent_id="unknown_123",
            encoding=test_face_encoding,
            image_path="Dataset/unknown/unknown_123_1234567890.jpg"
        )
        test_db_session.add(unknown)
        test_db_session.commit()
        test_db_session.refresh(unknown)
        
        # Create unknown attendance
        unknown_attendance = UnknownAttendance(
            unknown_id=unknown.id,
            camera_name="Test Camera",
            image_path="Dataset/unknown/unknown_123_1234567890.jpg"
        )
        test_db_session.add(unknown_attendance)
        test_db_session.commit()
        test_db_session.refresh(unknown_attendance)
        
        assert unknown_attendance.id is not None
        assert unknown_attendance.unknown_id == unknown.id
        assert unknown_attendance.camera_name == "Test Camera"
        assert unknown_attendance.image_path == "Dataset/unknown/unknown_123_1234567890.jpg"
        assert unknown_attendance.timestamp is not None
    
    def test_user_image_relationship(self, test_db_session, test_user_data):
        """Test the relationship between User and Image models."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create multiple images for the user
        image1 = Image(filename="image1.jpg", image_url="static/images/image1.jpg", user_id=user.user_id)
        image2 = Image(filename="image2.jpg", image_url="static/images/image2.jpg", user_id=user.user_id)
        
        test_db_session.add(image1)
        test_db_session.add(image2)
        test_db_session.commit()
        
        # Test relationship
        user_from_db = test_db_session.query(User).filter(User.user_id == user.user_id).first()
        assert len(user_from_db.images) == 2
        assert image1 in user_from_db.images
        assert image2 in user_from_db.images
    
    def test_image_encoding_relationship(self, test_db_session, test_user_data, test_face_encoding):
        """Test the relationship between Image and Encoding models."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create image
        image = Image(filename="test.jpg", image_url="static/images/test.jpg", user_id=user.user_id)
        test_db_session.add(image)
        test_db_session.commit()
        test_db_session.refresh(image)
        
        # Create multiple encodings for the image
        encoding1 = Encoding(encoding=test_face_encoding, user_id=user.user_id, image_id=image.id)
        encoding2 = Encoding(encoding=test_face_encoding, user_id=user.user_id, image_id=image.id)
        
        test_db_session.add(encoding1)
        test_db_session.add(encoding2)
        test_db_session.commit()
        
        # Test relationship
        image_from_db = test_db_session.query(Image).filter(Image.id == image.id).first()
        assert len(image_from_db.encodings) == 2
        assert encoding1 in image_from_db.encodings
        assert encoding2 in image_from_db.encodings
    
    def test_user_attendance_relationship(self, test_db_session, test_user_data):
        """Test the relationship between User and Attendance models."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create multiple attendance records
        attendance1 = Attendance(user_id=user.user_id, camera_name="Camera1")
        attendance2 = Attendance(user_id=user.user_id, camera_name="Camera2")
        
        test_db_session.add(attendance1)
        test_db_session.add(attendance2)
        test_db_session.commit()
        
        # Test relationship
        user_from_db = test_db_session.query(User).filter(User.user_id == user.user_id).first()
        assert len(user_from_db.attendances) == 2
        assert attendance1 in user_from_db.attendances
        assert attendance2 in user_from_db.attendances
    
    def test_query_users_by_username(self, test_db_session, test_user_data):
        """Test querying users by username."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        
        # Query by username
        found_user = test_db_session.query(User).filter(User.username == test_user_data["username"]).first()
        assert found_user is not None
        assert found_user.username == test_user_data["username"]
        assert found_user.email == test_user_data["email"]
    
    def test_query_users_by_email(self, test_db_session, test_user_data):
        """Test querying users by email."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        
        # Query by email
        found_user = test_db_session.query(User).filter(User.email == test_user_data["email"]).first()
        assert found_user is not None
        assert found_user.username == test_user_data["username"]
        assert found_user.email == test_user_data["email"]
    
    def test_query_encodings_by_user(self, test_db_session, test_user_data, test_face_encoding):
        """Test querying encodings by user."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create encoding
        encoding = Encoding(encoding=test_face_encoding, user_id=user.user_id)
        test_db_session.add(encoding)
        test_db_session.commit()
        
        # Query encodings by user
        found_encodings = test_db_session.query(Encoding).filter(Encoding.user_id == user.user_id).all()
        assert len(found_encodings) == 1
        assert found_encodings[0].encoding == test_face_encoding
    
    def test_query_attendance_by_date_range(self, test_db_session, test_user_data):
        """Test querying attendance by date range."""
        # Create user
        user = User(**test_user_data)
        test_db_session.add(user)
        test_db_session.commit()
        test_db_session.refresh(user)
        
        # Create attendance records
        attendance1 = Attendance(user_id=user.user_id, camera_name="Camera1")
        attendance2 = Attendance(user_id=user.user_id, camera_name="Camera2")
        
        test_db_session.add(attendance1)
        test_db_session.add(attendance2)
        test_db_session.commit()
        
        # Query all attendance for user
        found_attendance = test_db_session.query(Attendance).filter(Attendance.user_id == user.user_id).all()
        assert len(found_attendance) == 2


if __name__ == "__main__":
    pytest.main([__file__])
