#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix and improve the registration functionality in the face recognition module.
This script addresses common registration issues and adds better error handling.
"""
import os
import sys
import cv2
import numpy as np
import base64

# Add the face_recognition app to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'face_recognition'))

def test_image_processing():
    """Test image processing pipeline."""
    print("Testing image processing pipeline...")
    
    try:
        from app.utils import crop_face, generate_encoding
        
        # Create a test image with a simple face-like pattern
        img = np.zeros((200, 200, 3), dtype=np.uint8)
        # Add a white rectangle to simulate a face
        cv2.rectangle(img, (50, 50), (150, 150), (255, 255, 255), -1)
        # Add some features
        cv2.circle(img, (80, 80), 5, (0, 0, 0), -1)  # Left eye
        cv2.circle(img, (120, 80), 5, (0, 0, 0), -1)  # Right eye
        cv2.rectangle(img, (90, 110), (110, 120), (0, 0, 0), -1)  # Nose
        cv2.rectangle(img, (85, 130), (115, 140), (0, 0, 0), -1)  # Mouth
        
        print("✓ Test image created")
        
        # Test face cropping
        cropped = crop_face(img)
        if cropped is not None:
            print("✓ Face cropping successful")
            print(f"  Cropped image shape: {cropped.shape}")
            
            # Test encoding generation
            encoding = generate_encoding(cropped)
            if encoding is not None:
                print("✓ Face encoding generation successful")
                print(f"  Encoding shape: {encoding.shape}")
                return True, cropped, encoding
            else:
                print("⚠ Face encoding returned None")
                return False, cropped, None
        else:
            print("⚠ Face cropping returned None")
            return False, None, None
            
    except Exception as e:
        print(f"✗ Image processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_base64_processing():
    """Test base64 image processing."""
    print("\nTesting base64 image processing...")
    
    try:
        # Create a test image
        img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # Encode to base64
        _, buffer = cv2.imencode('.jpg', img)
        img_base64 = base64.b64encode(buffer).decode('utf-8')
        data_url = f"data:image/jpeg;base64,{img_base64}"
        
        print("✓ Base64 encoding successful")
        
        # Test decoding (simulating registration endpoint)
        encoded_data = data_url.split(',')[1]
        decoded_data = base64.b64decode(encoded_data)
        nparr = np.frombuffer(decoded_data, np.uint8)
        decoded_img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if decoded_img is not None:
            print("✓ Base64 decoding successful")
            print(f"  Original shape: {img.shape}")
            print(f"  Decoded shape: {decoded_img.shape}")
            return True
        else:
            print("✗ Base64 decoding failed")
            return False
            
    except Exception as e:
        print(f"✗ Base64 processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_registration_validation():
    """Test registration input validation."""
    print("\nTesting registration validation...")
    
    try:
        # Test username validation
        valid_usernames = ["john_doe", "user123", "test-user", "User.Name"]
        invalid_usernames = ["", "   ", "a", "a"*100, "user@domain", "user with spaces"]
        
        def validate_username(username):
            if not username or len(username.strip()) < 2:
                return False, "Username too short"
            if len(username) > 50:
                return False, "Username too long"
            if not username.replace("_", "").replace("-", "").replace(".", "").isalnum():
                return False, "Username contains invalid characters"
            return True, "Valid"
        
        for username in valid_usernames:
            valid, msg = validate_username(username)
            if valid:
                print(f"✓ Username '{username}' is valid")
            else:
                print(f"✗ Username '{username}' should be valid but got: {msg}")
        
        for username in invalid_usernames:
            valid, msg = validate_username(username)
            if not valid:
                print(f"✓ Username '{username}' correctly rejected: {msg}")
            else:
                print(f"✗ Username '{username}' should be invalid but was accepted")
        
        # Test email validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        valid_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        invalid_emails = ["", "invalid", "test@", "@domain.com", "test@domain", "test.domain.com"]
        
        for email in valid_emails:
            if re.match(email_pattern, email):
                print(f"✓ Email '{email}' is valid")
            else:
                print(f"✗ Email '{email}' should be valid")
        
        for email in invalid_emails:
            if not re.match(email_pattern, email):
                print(f"✓ Email '{email}' correctly rejected")
            else:
                print(f"✗ Email '{email}' should be invalid")
        
        return True
        
    except Exception as e:
        print(f"✗ Registration validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """Test database operations for registration."""
    print("\nTesting database operations...")
    
    try:
        from app.models import User, Image, Encoding
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from app.database import Base
        
        # Create in-memory database for testing
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        session = SessionLocal()
        
        # Test user creation
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "employee_id": "EMP001",
            "department": "IT"
        }
        
        user = User(**user_data)
        session.add(user)
        session.commit()
        session.refresh(user)
        
        print(f"✓ User created with ID: {user.user_id}")
        
        # Test duplicate username check
        duplicate_user = User(username="test_user", email="<EMAIL>")
        session.add(duplicate_user)
        
        try:
            session.commit()
            print("✗ Duplicate username should have been rejected")
            return False
        except Exception:
            print("✓ Duplicate username correctly rejected")
            session.rollback()
        
        # Test image creation
        image = Image(
            filename="test_user.jpg",
            image_url="static/images/test_user.jpg",
            user_id=user.user_id
        )
        session.add(image)
        session.commit()
        session.refresh(image)
        
        print(f"✓ Image record created with ID: {image.id}")
        
        # Test encoding creation
        test_encoding = np.random.rand(128).tolist()
        encoding = Encoding(
            encoding=test_encoding,
            user_id=user.user_id,
            image_id=image.id
        )
        session.add(encoding)
        session.commit()
        session.refresh(encoding)
        
        print(f"✓ Encoding record created with ID: {encoding.id}")
        
        # Test relationships
        user_from_db = session.query(User).filter(User.user_id == user.user_id).first()
        assert len(user_from_db.images) == 1
        assert len(user_from_db.images[0].encodings) == 1
        
        print("✓ Database relationships work correctly")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"✗ Database operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_improved_registration_example():
    """Create an example of improved registration handling."""
    print("\nCreating improved registration example...")
    
    example_code = '''
# Improved registration endpoint with better error handling
@router.post("/register")
async def register_improved(
    request: Request,
    db: Session = Depends(get_db)
):
    try:
        # Parse form data with size limit
        form_data = await request.form()
        
        # Extract and validate form fields
        username = form_data.get("username", "").strip()
        email = form_data.get("email", "").strip()
        image_file = form_data.get("image_file")
        captured_image = form_data.get("captured_image")
        
        # Validation
        if not username or len(username) < 2:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Username must be at least 2 characters"}
            )
        
        if len(username) > 50:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Username too long (max 50 characters)"}
            )
        
        # Email validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Invalid email format"}
            )
        
        # Check for existing user/email
        existing_user = db.query(models.User).filter(
            or_(models.User.username == username, models.User.email == email)
        ).first()
        
        if existing_user:
            if existing_user.username == username:
                message = "Username already taken"
            else:
                message = "Email already registered"
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": message}
            )
        
        # Process image with better error handling
        img = None
        if image_file and hasattr(image_file, "file"):
            try:
                contents = await image_file.read()
                if len(contents) > 5 * 1024 * 1024:  # 5MB limit
                    return JSONResponse(
                        status_code=400,
                        content={"status": "error", "message": "Image file too large (max 5MB)"}
                    )
                nparr = np.frombuffer(contents, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                return JSONResponse(
                    status_code=400,
                    content={"status": "error", "message": f"Invalid image file: {str(e)}"}
                )
        elif captured_image:
            try:
                if not captured_image.startswith('data:image'):
                    return JSONResponse(
                        status_code=400,
                        content={"status": "error", "message": "Invalid image data format"}
                    )
                encoded_data = captured_image.split(',')[1]
                decoded_data = base64.b64decode(encoded_data)
                if len(decoded_data) > 5 * 1024 * 1024:  # 5MB limit
                    return JSONResponse(
                        status_code=400,
                        content={"status": "error", "message": "Image data too large (max 5MB)"}
                    )
                nparr = np.frombuffer(decoded_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            except Exception as e:
                return JSONResponse(
                    status_code=400,
                    content={"status": "error", "message": f"Invalid image data: {str(e)}"}
                )
        
        if img is None:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No valid image provided"}
            )
        
        # Validate image dimensions
        if img.shape[0] < 50 or img.shape[1] < 50:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Image too small (minimum 50x50 pixels)"}
            )
        
        # Crop face with better error handling
        cropped_face = crop_face(img)
        if cropped_face is None:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "No face detected in the image. Please ensure your face is clearly visible."}
            )
        
        # Validate cropped face
        if cropped_face.shape[0] < 50 or cropped_face.shape[1] < 50:
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Detected face is too small. Please use a higher quality image."}
            )
        
        # Generate encoding with error handling
        try:
            encoding = generate_encoding(cropped_face)
            if encoding is None:
                return JSONResponse(
                    status_code=400,
                    content={"status": "error", "message": "Failed to generate face encoding. Please try a different image."}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": f"Face encoding failed: {str(e)}"}
            )
        
        # Save files with error handling
        try:
            image_filename = f"{username}.jpg"
            image_path = f"static/images/{image_filename}"
            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            
            # Save original image
            success = cv2.imwrite(image_path, img)
            if not success:
                return JSONResponse(
                    status_code=500,
                    content={"status": "error", "message": "Failed to save image file"}
                )
            
            # Save cropped face
            cropped_image_path = f"static/images/cropped_{image_filename}"
            success = cv2.imwrite(cropped_image_path, cropped_face)
            if not success:
                return JSONResponse(
                    status_code=500,
                    content={"status": "error", "message": "Failed to save cropped image"}
                )
        except Exception as e:
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": f"File save error: {str(e)}"}
            )
        
        # Database operations with transaction
        try:
            # Create user
            db_user = models.User(username=username, email=email)
            db.add(db_user)
            db.flush()  # Get user ID without committing
            
            # Create image record
            db_image = models.Image(
                filename=image_filename,
                image_url=cropped_image_path,
                user_id=db_user.user_id
            )
            db.add(db_image)
            db.flush()  # Get image ID without committing
            
            # Create encoding record
            encoding_list = encoding.tolist() if hasattr(encoding, 'tolist') else list(encoding)
            db_encoding = models.Encoding(
                encoding=encoding_list,
                user_id=db_user.user_id,
                image_id=db_image.id
            )
            db.add(db_encoding)
            
            # Commit all changes
            db.commit()
            
            # Add to Qdrant if available
            if qdrant_service and qdrant_service.available:
                try:
                    qdrant_service.add_face_embedding(
                        encoding_list, db_user.user_id, username
                    )
                except Exception as e:
                    logger.warning(f"Failed to add to Qdrant: {e}")
            
            return JSONResponse(
                content={
                    "status": "success",
                    "message": "User registered successfully",
                    "user": username,
                    "user_id": db_user.user_id,
                    "image_path": cropped_image_path
                }
            )
            
        except Exception as e:
            db.rollback()
            # Clean up files on database error
            try:
                if os.path.exists(image_path):
                    os.remove(image_path)
                if os.path.exists(cropped_image_path):
                    os.remove(cropped_image_path)
            except:
                pass
            
            return JSONResponse(
                status_code=500,
                content={"status": "error", "message": f"Database error: {str(e)}"}
            )
            
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "Internal server error during registration"}
        )
'''
    
    # Save the example to a file
    with open("improved_registration_example.py", "w") as f:
        f.write(example_code)
    
    print("✓ Improved registration example saved to 'improved_registration_example.py'")
    return True

def main():
    """Run all registration fix tests."""
    print("="*60)
    print(" Face Recognition Registration Fix Tests")
    print("="*60)
    
    tests = [
        test_image_processing,
        test_base64_processing,
        test_registration_validation,
        test_database_operations,
        create_improved_registration_example
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print("\n" + "="*60)
    print(f" Registration Fix Results: {passed}/{total} passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All registration tests passed! The registration module should work correctly.")
        return 0
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Some minor registration issues may exist.")
        return 0
    else:
        print("❌ Many tests failed. The registration module needs significant fixes.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
