<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>User Details - Face Recognition System</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #1abc9c;
      --danger-color: #e74c3c;
      --warning-color: #f39c12;
      --success-color: #2ecc71;
      --text-light: #ecf0f1;
      --text-dark: #2c3e50;
      --bg-light: #f5f7fa;
      --bg-dark: #34495e;
      --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
      line-height: 1.6;
    }

    .header {
      background-color: var(--primary-color);
      color: var(--text-light);
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--shadow);
    }

    .header-title {
      font-size: 20px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 15px;
    }

    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .btn-primary {
      background-color: var(--secondary-color);
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-2px);
    }

    .btn-secondary {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .btn-secondary:hover {
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: white;
    }

    .btn-danger:hover {
      background-color: #c0392b;
      transform: translateY(-2px);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 30px 20px;
    }

    .page-title {
      text-align: center;
      margin: 30px 0;
      color: var(--primary-color);
      position: relative;
      display: inline-block;
      left: 50%;
      transform: translateX(-50%);
      font-size: 28px;
    }

    .page-title:after {
      content: '';
      position: absolute;
      width: 60px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }

    /* Card Styling */
    .card {
      background: white;
      border-radius: 10px;
      padding: 25px;
      margin: 25px 0;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    /* User Profile Styling */
    .user-profile {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      margin-bottom: 30px;
    }

    .user-image-container {
      flex: 0 0 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .user-image {
      width: 200px;
      height: 200px;
      object-fit: cover;
      border-radius: 50%;
      border: 5px solid white;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .user-image:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .user-info {
      flex: 1;
      min-width: 300px;
    }

    .user-name {
      font-size: 28px;
      margin: 0 0 15px 0;
      color: var(--primary-color);
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .user-name i {
      color: var(--secondary-color);
    }

    .user-email {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }

    .user-email i {
      color: var(--secondary-color);
    }

    /* User Stats */
    .user-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-top: 20px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 15px;
      background-color: var(--bg-light);
      border-radius: 8px;
      flex: 1;
      min-width: 200px;
      box-shadow: var(--shadow);
      transition: var(--transition);
    }

    .stat-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    .stat-item i {
      font-size: 24px;
      color: var(--secondary-color);
    }

    .stat-details {
      display: flex;
      flex-direction: column;
    }

    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: var(--primary-color);
    }

    .stat-label {
      font-size: 12px;
      color: #666;
    }

    .user-actions {
      display: flex;
      gap: 15px;
      margin-top: 25px;
    }

    /* Section Styling */
    .section-title {
      font-size: 20px;
      margin: 30px 0 20px 0;
      color: var(--primary-color);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      padding-bottom: 10px;
    }

    .section-title:after {
      content: '';
      position: absolute;
      width: 40px;
      height: 3px;
      background-color: var(--secondary-color);
      bottom: 0;
      left: 0;
    }

    .section-title i {
      color: var(--secondary-color);
    }

    /* Attendance Section */
    .attendance-section {
      margin-bottom: 40px;
    }

    .attendance-list {
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
      overflow: hidden;
      animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .attendance-table {
      width: 100%;
      border-collapse: collapse;
    }

    .attendance-table th,
    .attendance-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    .attendance-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: 500;
    }

    .attendance-table th:first-child {
      border-top-left-radius: 8px;
    }

    .attendance-table th:last-child {
      border-top-right-radius: 8px;
    }

    .attendance-table tr:last-child td {
      border-bottom: none;
    }

    .attendance-table tr:hover {
      background-color: rgba(52, 152, 219, 0.05);
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: 50px 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--shadow);
    }

    .empty-state i {
      font-size: 48px;
      color: var(--secondary-color);
      margin-bottom: 20px;
      display: block;
    }

    .empty-state h3 {
      font-size: 24px;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .empty-state p {
      color: #666;
      margin-bottom: 20px;
    }

    .loading {
      text-align: center;
      padding: 30px;
      color: var(--primary-color);
    }

    /* Toast notification */
    .toast {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      background-color: var(--primary-color);
      color: white;
      padding: 12px 25px;
      border-radius: 8px;
      z-index: 1000;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: opacity 0.3s, transform 0.3s;
      pointer-events: none;
    }

    .toast.show {
      opacity: 1;
      transform: translate(-50%, -10px);
    }

    .toast.success {
      background-color: var(--success-color);
    }

    .toast.error {
      background-color: var(--danger-color);
    }

    .toast.info {
      background-color: var(--secondary-color);
    }

    /* Confirmation Dialog */
    .confirm-dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1100;
      display: none;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(3px);
    }

    .confirm-dialog-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      width: 400px;
      max-width: 90%;
      text-align: center;
      position: relative;
      z-index: 1101;
      animation: dialogFadeIn 0.3s ease;
    }

    @keyframes dialogFadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .confirm-dialog h3 {
      margin-top: 0;
      margin-bottom: 20px;
      color: var(--primary-color);
      font-size: 24px;
    }

    .confirm-dialog p {
      margin-bottom: 20px;
      color: var(--text-dark);
      font-size: 16px;
      line-height: 1.5;
    }

    .confirm-dialog-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 25px;
    }

    .confirm-dialog-buttons button {
      min-width: 100px;
      padding: 10px 15px;
      font-weight: 500;
    }

    /* Tabs */
    .tabs {
      display: flex;
      border-bottom: 1px solid #eee;
      margin-bottom: 20px;
    }

    .tab-btn {
      padding: 10px 15px;
      background: none;
      border: none;
      border-bottom: 3px solid transparent;
      cursor: pointer;
      font-weight: 500;
      color: var(--text-dark);
      transition: var(--transition);
      flex: 1;
      text-align: center;
    }

    .tab-btn:hover {
      color: var(--secondary-color);
    }

    .tab-btn.active {
      color: var(--secondary-color);
      border-bottom-color: var(--secondary-color);
    }

    /* Form Controls */
    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-dark);
    }

    .form-control {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 16px;
      transition: var(--transition);
    }

    .form-control:focus {
      border-color: var(--secondary-color);
      outline: none;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="header-title">Face Recognition System</div>
    <div class="header-actions">
      <button class="btn btn-secondary" id="backBtn">
        <i class="fas fa-arrow-left"></i> Back
      </button>
    </div>
  </div>

  <div class="container">
    <!-- Page Header with User Name and Edit Button -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
      <h1 id="userName" class="page-title" style="margin: 0; font-size: 28px; font-weight: 700;">
        <i class="fas fa-user-circle"></i> <span>Loading...</span>
      </h1>
      <button class="btn btn-primary" id="editUserBtn" style="padding: 8px 15px; font-weight: 500; border-radius: 50px;">
        <i class="fas fa-edit"></i> Edit Profile
      </button>
    </div>

    <!-- Main Content Grid -->
    <div style="display: grid; grid-template-columns: 220px 1fr; gap: 15px;">
      <!-- Left Column: Photo and Actions -->
      <div>
        <!-- User Image Section -->
        <div class="card" style="border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 15px; padding: 15px;">
          <div style="position: relative; margin-bottom: 15px;">
            <img id="userImage" src="" alt="User Profile" class="user-image"
                 style="width: 100%; height: 190px; object-fit: cover; border-radius: 10px;
                        border: 3px solid white; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
          </div>
          <button id="changePhotoBtn" class="btn btn-primary" style="width: 100%; border-radius: 50px; padding: 8px 0; margin-bottom: 10px;">
            <i class="fas fa-camera"></i> Change Photo
          </button>

          <!-- Actions Section -->
          <button class="btn btn-danger" id="deleteUserBtn" style="width: 100%; padding: 8px 0; border-radius: 50px;">
            <i class="fas fa-trash-alt"></i> Delete User
          </button>
        </div>

        <!-- Stats Section -->
        <div class="card" style="border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); padding: 15px;">
          <h3 style="margin-top: 0; margin-bottom: 15px; font-size: 16px; color: var(--primary-color); border-bottom: 2px solid var(--secondary-color); padding-bottom: 8px;">
            <i class="fas fa-chart-bar"></i> Attendance Stats
          </h3>

          <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="background-color: #f8f9fa; padding: 12px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); display: flex; align-items: center;">
              <i class="fas fa-calendar-check" style="font-size: 20px; color: var(--secondary-color); margin-right: 12px;"></i>
              <div>
                <div style="font-size: 20px; font-weight: 600; color: var(--primary-color);" id="attendanceCount">-</div>
                <div style="font-size: 12px; color: #666;">Total Attendances</div>
              </div>
            </div>

            <div style="background-color: #f8f9fa; padding: 12px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); display: flex; align-items: center;">
              <i class="fas fa-clock" style="font-size: 20px; color: var(--secondary-color); margin-right: 12px;"></i>
              <div>
                <div style="font-size: 20px; font-weight: 600; color: var(--primary-color);" id="lastSeen">-</div>
                <div style="font-size: 12px; color: #666;">Last Seen</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: User Details and Attendance -->
      <div style="display: flex; flex-direction: column; gap: 15px;">
        <!-- User Details Section -->
        <div class="card" style="border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <div style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); padding: 15px; color: white;">
            <h3 style="margin: 0; font-size: 18px; font-weight: 600;">
              <i class="fas fa-info-circle"></i> Employee Information
            </h3>
          </div>

          <div style="padding: 15px;">
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">
              <div id="userEmail" class="user-email" style="font-size: 15px; display: flex; align-items: center;">
                <i class="fas fa-envelope" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Email</div>
                  <span style="font-weight: 500;">Loading...</span>
                </div>
              </div>

              <div id="employeeId" style="display: none; font-size: 15px; align-items: center;">
                <i class="fas fa-id-card" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Employee ID</div>
                  <span style="font-weight: 500;">-</span>
                </div>
              </div>

              <div id="department" style="display: none; font-size: 15px; align-items: center;">
                <i class="fas fa-building" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Department</div>
                  <span style="font-weight: 500;">-</span>
                </div>
              </div>

              <div id="dob" style="display: none; font-size: 15px; align-items: center;">
                <i class="fas fa-birthday-cake" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Date of Birth</div>
                  <span style="font-weight: 500;">-</span>
                </div>
              </div>

              <div id="phoneNumber" style="display: none; font-size: 15px; align-items: center;">
                <i class="fas fa-phone" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Phone Number</div>
                  <span style="font-weight: 500;">-</span>
                </div>
              </div>

              <div id="address" style="display: none; font-size: 15px; align-items: center;">
                <i class="fas fa-map-marker-alt" style="color: var(--secondary-color); margin-right: 10px; font-size: 16px; width: 20px; text-align: center;"></i>
                <div>
                  <div style="color: #666; font-size: 12px; margin-bottom: 3px;">Address</div>
                  <span style="font-weight: 500;">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Attendance Section -->
        <div class="card" style="border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
          <div style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); padding: 15px; color: white;">
            <h3 style="margin: 0; font-size: 18px; font-weight: 600;">
              <i class="fas fa-history"></i> Recent Attendance
            </h3>
          </div>
          <div id="attendanceList" class="attendance-list" style="padding: 15px; max-height: 400px; overflow-y: auto;">
            <div class="loading" style="text-align: center; padding: 20px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--secondary-color); margin-bottom: 10px;"></i>
              <p>Loading attendance records...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirmation Dialog -->
  <div id="confirmDialog" class="confirm-dialog">
    <div class="confirm-dialog-content">
      <h3>Confirm Action</h3>
      <p id="confirmMessage">Are you sure you want to perform this action?</p>
      <div class="confirm-dialog-buttons">
        <button class="btn btn-danger" id="confirmYes">Delete</button>
        <button class="btn btn-primary" id="confirmNo">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Edit User Dialog -->
  <div id="editUserDialog" class="confirm-dialog" style="display: none;">
    <div class="confirm-dialog-content" style="width: 600px; max-width: 95%; position: relative; padding: 25px; border-radius: 12px;">
      <button id="closeEditDialogBtn" style="position: absolute; top: 15px; right: 15px; background: none; border: none; font-size: 18px; cursor: pointer; color: #666;">
        <i class="fas fa-times"></i>
      </button>

      <h3 style="margin-top: 0; margin-bottom: 25px; font-size: 22px; color: var(--primary-color); border-bottom: 2px solid var(--secondary-color); padding-bottom: 10px;">
        <i class="fas fa-user-edit"></i> Edit Employee Profile
      </h3>

      <form id="editUserForm">
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-bottom: 25px;">
          <!-- Basic Information -->
          <div>
            <label for="editUsername" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-user" style="color: var(--secondary-color); margin-right: 8px;"></i>Username
            </label>
            <input type="text" id="editUsername" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">
          </div>

          <div>
            <label for="editEmail" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-envelope" style="color: var(--secondary-color); margin-right: 8px;"></i>Email
            </label>
            <input type="email" id="editEmail" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">
          </div>

          <div>
            <label for="editEmployeeId" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-id-card" style="color: var(--secondary-color); margin-right: 8px;"></i>Employee ID
            </label>
            <input type="text" id="editEmployeeId" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">
          </div>

          <div>
            <label for="editDepartment" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-building" style="color: var(--secondary-color); margin-right: 8px;"></i>Department
            </label>
            <input type="text" id="editDepartment" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">
          </div>

          <div>
            <label for="editDob" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-birthday-cake" style="color: var(--secondary-color); margin-right: 8px;"></i>Date of Birth
            </label>
            <input type="text" id="editDob" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;" placeholder="YYYY-MM-DD">
          </div>

          <div>
            <label for="editPhoneNumber" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
              <i class="fas fa-phone" style="color: var(--secondary-color); margin-right: 8px;"></i>Phone Number
            </label>
            <input type="text" id="editPhoneNumber" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd;">
          </div>
        </div>

        <div style="margin-bottom: 25px;">
          <label for="editAddress" style="display: block; margin-bottom: 8px; font-weight: 600; color: var(--primary-color);">
            <i class="fas fa-map-marker-alt" style="color: var(--secondary-color); margin-right: 8px;"></i>Address
          </label>
          <textarea id="editAddress" class="form-control" style="width: 100%; padding: 10px; border-radius: 6px; border: 1px solid #ddd; resize: vertical;" rows="3"></textarea>
        </div>
      </form>

      <div style="display: flex; justify-content: center; gap: 15px; margin-top: 20px;">
        <button id="saveUserBtn" class="btn btn-success" style="padding: 12px 25px; font-size: 16px; font-weight: 600; min-width: 150px; border-radius: 50px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <i class="fas fa-save" style="margin-right: 8px;"></i> Save Changes
        </button>
        <button id="cancelEditBtn" class="btn btn-secondary" style="padding: 12px 25px; border-radius: 50px;">
          <i class="fas fa-times" style="margin-right: 8px;"></i> Cancel
        </button>
      </div>
    </div>
  </div>

  <!-- Photo Upload Dialog -->
  <div id="photoUploadDialog" class="confirm-dialog">
    <div class="confirm-dialog-content" style="width: 500px; max-width: 95%; position: relative;">
      <button id="closeDialogBtn" style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 18px; cursor: pointer; color: #666;">
        <i class="fas fa-times"></i>
      </button>
      <h3><i class="fas fa-camera"></i> Update Profile Photo</h3>
      <p>Upload a new photo or take a picture with your camera.</p>

      <div class="tabs" style="margin: 20px 0;">
        <button id="uploadTabBtn" class="tab-btn active">
          <i class="fas fa-upload"></i> Upload
        </button>
        <button id="cameraTabBtn" class="tab-btn">
          <i class="fas fa-camera"></i> Camera
        </button>
      </div>

      <div id="uploadTab" class="tab-content">
        <form id="photoUploadForm" enctype="multipart/form-data">
          <div style="margin-bottom: 15px;">
            <label for="photoFile" class="form-label">Select an image file:</label>
            <input type="file" id="photoFile" name="photo" accept="image/*" class="form-control">
          </div>
          <div id="uploadPreview" style="display: none; margin-bottom: 15px;">
            <img id="previewImage" src="" alt="Preview" style="max-width: 100%; max-height: 300px; border-radius: 8px;">
          </div>
        </form>
      </div>

      <div id="cameraTab" class="tab-content" style="display: none;">
        <div style="margin-bottom: 15px;">
          <video id="cameraPreview" style="width: 100%; border-radius: 8px; display: none;"></video>
          <canvas id="captureCanvas" style="display: none;"></canvas>

          <!-- Camera controls before capture -->
          <div id="cameraBtns" style="margin-top: 10px; display: none; text-align: center;">
            <button id="captureBtn" class="btn btn-primary">
              <i class="fas fa-camera"></i> Capture Photo
            </button>
          </div>

          <!-- Preview after capture -->
          <div id="capturePreview" style="display: none; margin-top: 15px; text-align: center;">
            <img id="capturedImage" src="" alt="Captured" style="max-width: 100%; max-height: 300px; border-radius: 8px; margin-bottom: 15px;">

            <!-- Post-capture controls -->
            <div style="display: flex; justify-content: center; gap: 10px; margin-top: 10px;">
              <button id="retakeBtn" class="btn btn-secondary">
                <i class="fas fa-redo"></i> Retake Photo
              </button>
              <button id="useCapturedBtn" class="btn btn-success">
                <i class="fas fa-check"></i> Use This Photo
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="confirm-dialog-buttons">
        <button class="btn btn-success" id="savePhotoBtn">
          <i class="fas fa-save"></i> Save Photo
        </button>
        <button class="btn btn-secondary" id="cancelPhotoBtn">
          <i class="fas fa-times"></i> Cancel
        </button>
      </div>
    </div>
  </div>

  <script>
    // DOM Elements
    const backBtn = document.getElementById('backBtn');
    const userImage = document.getElementById('userImage');
    const userName = document.getElementById('userName').querySelector('span');
    const userEmail = document.getElementById('userEmail').querySelector('span');
    const deleteUserBtn = document.getElementById('deleteUserBtn');
    const editUserBtn = document.getElementById('editUserBtn');
    const attendanceList = document.getElementById('attendanceList');
    const confirmDialog = document.getElementById('confirmDialog');
    const confirmYes = document.getElementById('confirmYes');
    const confirmNo = document.getElementById('confirmNo');
    const confirmMessage = document.getElementById('confirmMessage');
    const attendanceCount = document.getElementById('attendanceCount');
    const lastSeen = document.getElementById('lastSeen');

    // Employee details elements
    const employeeId = document.getElementById('employeeId');
    const department = document.getElementById('department');
    const dob = document.getElementById('dob');
    const phoneNumber = document.getElementById('phoneNumber');
    const address = document.getElementById('address');

    // Photo Upload Elements
    const changePhotoBtn = document.getElementById('changePhotoBtn');
    const photoUploadDialog = document.getElementById('photoUploadDialog');
    const closeDialogBtn = document.getElementById('closeDialogBtn');
    const uploadTabBtn = document.getElementById('uploadTabBtn');
    const cameraTabBtn = document.getElementById('cameraTabBtn');
    const uploadTab = document.getElementById('uploadTab');
    const cameraTab = document.getElementById('cameraTab');
    const photoFile = document.getElementById('photoFile');
    const uploadPreview = document.getElementById('uploadPreview');
    const previewImage = document.getElementById('previewImage');
    const cameraPreview = document.getElementById('cameraPreview');
    const captureCanvas = document.getElementById('captureCanvas');
    const cameraBtns = document.getElementById('cameraBtns');
    const captureBtn = document.getElementById('captureBtn');
    const capturePreview = document.getElementById('capturePreview');
    const capturedImage = document.getElementById('capturedImage');
    const retakeBtn = document.getElementById('retakeBtn');
    const useCapturedBtn = document.getElementById('useCapturedBtn');
    const savePhotoBtn = document.getElementById('savePhotoBtn');
    const cancelPhotoBtn = document.getElementById('cancelPhotoBtn');

    // Get user ID from URL
    const pathParts = window.location.pathname.split('/');
    const userId = pathParts[pathParts.length - 2]; // Get the ID before the "page" part
    console.log("User ID extracted from URL:", userId);

    // Load user details
    async function loadUserDetails() {
      try {
        console.log(`Fetching user details from: /face_recognition/user/${userId}`);
        const response = await fetch(`/face_recognition/user/${userId}`);

        if (!response.ok) {
          console.error(`HTTP error! status: ${response.status}`);
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const user = await response.json();
        console.log("User data received:", user);

        // Update user details
        userName.textContent = user.username;
        userEmail.textContent = user.email;

        // Employee details are handled individually below

        // Update employee details
        if (user.employee_id) {
          employeeId.style.display = 'flex';
          employeeId.querySelector('span').textContent = user.employee_id;
        } else {
          employeeId.style.display = 'none';
        }

        if (user.department) {
          department.style.display = 'flex';
          department.querySelector('span').textContent = user.department;
        } else {
          department.style.display = 'none';
        }

        if (user.dob) {
          dob.style.display = 'flex';
          dob.querySelector('span').textContent = user.dob;
        } else {
          dob.style.display = 'none';
        }

        if (user.phone_number) {
          phoneNumber.style.display = 'flex';
          phoneNumber.querySelector('span').textContent = user.phone_number;
        } else {
          phoneNumber.style.display = 'none';
        }

        if (user.address) {
          address.style.display = 'flex';
          address.querySelector('span').textContent = user.address;
        } else {
          address.style.display = 'none';
        }

        // Use the original uploaded image from images folder
        // Encode the username to handle spaces and special characters
        const encodedUsername = encodeURIComponent(user.username);
        userImage.src = `/images/${encodedUsername}.jpg`;

        // Store fallback images
        let fallbackImages = [];

        // First fallback: image from database
        if (user.images && user.images.length > 0) {
          user.images.forEach(img => {
            fallbackImages.push(img.image_url.replace('./', '/'));
          });
        }

        // Second fallback: cropped face image
        fallbackImages.push(`/cropped_faces/${encodedUsername}.jpg`);

        // Handle image loading error with multiple fallbacks
        userImage.onerror = function() {
          if (fallbackImages.length > 0) {
            // Try the next fallback image
            const nextFallback = fallbackImages.shift();
            console.log(`Trying fallback image: ${nextFallback}`);
            this.src = nextFallback;

            // If all fallbacks fail, hide the image
            if (fallbackImages.length === 0) {
              this.onerror = function() {
                console.error('All image fallbacks failed');
                this.style.display = 'none';
              };
            }
          } else {
            // No more fallbacks, hide the image
            console.error('No fallback images available');
            this.style.display = 'none';
          }
        };

        // Update stats
        if (user.attendance && user.attendance.length > 0) {
          // Update attendance count
          attendanceCount.textContent = user.attendance.length;

          // Update last seen (most recent attendance)
          const lastAttendance = new Date(user.attendance[0].timestamp);
          const now = new Date();
          const diffTime = Math.abs(now - lastAttendance);
          const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays === 0) {
            lastSeen.textContent = 'Today';
          } else if (diffDays === 1) {
            lastSeen.textContent = 'Yesterday';
          } else {
            lastSeen.textContent = `${diffDays} days ago`;
          }
        } else {
          attendanceCount.textContent = '0';
          lastSeen.textContent = 'Never';
        }

        // Load attendance records
        console.log("Attendance records:", user.attendance);
        try {
          if (user.attendance && user.attendance.length > 0) {
            console.log(`Found ${user.attendance.length} attendance records`);
            const table = document.createElement('table');
            table.className = 'attendance-table';
            table.style.width = '100%';
            table.style.borderCollapse = 'collapse';
            table.style.borderRadius = '8px';
            table.style.overflow = 'hidden';
            table.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';

            table.innerHTML = `
              <thead style="background-color: var(--secondary-color); color: white;">
                <tr>
                  <th style="padding: 12px 15px; text-align: left; font-weight: 600;"><i class="fas fa-calendar-alt"></i> Date & Time</th>
                  <th style="padding: 12px 15px; text-align: left; font-weight: 600;"><i class="fas fa-video"></i> Camera</th>
                </tr>
              </thead>
              <tbody></tbody>
            `;

            const tbody = table.querySelector('tbody');

            user.attendance.forEach(record => {
              console.log("Processing record:", record);
              try {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #eee';

                // Alternate row colors
                if (tbody.children.length % 2 === 0) {
                  row.style.backgroundColor = '#f8f9fa';
                } else {
                  row.style.backgroundColor = 'white';
                }

                const date = new Date(record.timestamp);

                // Format date nicely
                const formattedDate = new Intl.DateTimeFormat('en-US', {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                }).format(date);

                row.innerHTML = `
                  <td style="padding: 12px 15px;">${formattedDate}</td>
                  <td style="padding: 12px 15px;"><i class="fas fa-video" style="color: var(--secondary-color); margin-right: 5px;"></i> ${record.camera_name || 'System Camera'}</td>
                `;

                tbody.appendChild(row);
              } catch (recordError) {
                console.error("Error processing attendance record:", recordError, record);
              }
            });

            attendanceList.innerHTML = '';
            attendanceList.appendChild(table);
          } else {
            console.log("No attendance records found");
            attendanceList.innerHTML = `
              <div style="text-align: center; padding: 30px; background-color: #f8f9fa; border-radius: 8px;">
                <i class="fas fa-calendar-times" style="font-size: 48px; color: var(--secondary-color); margin-bottom: 15px;"></i>
                <h3 style="margin-bottom: 10px; color: var(--primary-color);">No Attendance Records</h3>
                <p style="color: #666;">This user has not been detected by any camera yet.</p>
              </div>
            `;
          }
        } catch (attendanceError) {
          console.error("Error processing attendance records:", attendanceError);
          attendanceList.innerHTML = `
            <div style="text-align: center; padding: 30px; background-color: #fff0f0; border-radius: 8px; border: 1px solid #ffcccc;">
              <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--danger-color); margin-bottom: 15px;"></i>
              <h3 style="margin-bottom: 10px; color: var(--danger-color);">Error Loading Records</h3>
              <p style="color: #666; margin-bottom: 15px;">There was a problem displaying attendance records.</p>
              <button class="btn btn-primary" onclick="loadUserDetails()" style="padding: 8px 20px; border-radius: 50px;">
                <i class="fas fa-sync-alt"></i> Try Again
              </button>
            </div>
          `;
        }
      } catch (error) {
        console.error('Error loading user details:', error);
        showToast('Failed to load user details. Please try again.', 'error');

        // Show error state
        userName.textContent = 'Error loading user';
        userEmail.textContent = 'Could not load user details';
        attendanceCount.textContent = '-';
        lastSeen.textContent = '-';

        // Show error in attendance section
        attendanceList.innerHTML = `
          <div style="text-align: center; padding: 30px; background-color: #fff0f0; border-radius: 8px; border: 1px solid #ffcccc;">
            <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: var(--danger-color); margin-bottom: 15px;"></i>
            <h3 style="margin-bottom: 10px; color: var(--danger-color);">Connection Error</h3>
            <p style="color: #666; margin-bottom: 15px;">Failed to load user data from the server.</p>
            <button class="btn btn-primary" onclick="loadUserDetails()" style="padding: 8px 20px; border-radius: 50px;">
              <i class="fas fa-sync-alt"></i> Try Again
            </button>
          </div>
        `;
      }
    }

    // Function to show confirmation dialog
    function showConfirmDialog(message, onConfirm) {
      confirmMessage.textContent = message;
      confirmDialog.style.display = 'flex';

      confirmYes.onclick = () => {
        confirmDialog.style.display = 'none';
        if (onConfirm) onConfirm();
      };

      confirmNo.onclick = () => {
        confirmDialog.style.display = 'none';
      };
    }

    // Function to delete user
    async function deleteUser() {
      try {
        showToast('Deleting user...', 'info');
        console.log(`Deleting user with ID: ${userId}`);

        const response = await fetch(`/face_recognition/users/${userId}`, {
          method: 'DELETE'
        });

        console.log(`Delete response status: ${response.status}`);
        const result = await response.json();
        console.log("Delete result:", result);

        if (response.ok) {
          showToast(result.message || 'User deleted successfully', 'success');

          // Redirect back to admin panel after a short delay
          setTimeout(() => {
            window.location.href = '/face_recognition/admin';
          }, 1500);
        } else {
          showToast(result.detail || 'Failed to delete user', 'error');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        showToast('Failed to delete user. Please try again.', 'error');
      }
    }

    // Function to show toast notification
    function showToast(message, type = 'info') {
      // Create toast element if it doesn't exist
      let toast = document.getElementById('toast');
      if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        document.body.appendChild(toast);
      }

      // Set toast content and style based on type
      toast.textContent = message;
      toast.className = `toast ${type}`;

      // Show the toast
      toast.classList.add('show');

      // Hide after 3 seconds
      setTimeout(() => {
        toast.classList.remove('show');
      }, 3000);
    }

    // Photo Upload Functions
    let stream = null;
    let capturedPhotoBlob = null;

    // Function to show the photo upload dialog
    function showPhotoUploadDialog() {
      photoUploadDialog.style.display = 'flex';

      // Reset the form
      document.getElementById('photoUploadForm').reset();
      uploadPreview.style.display = 'none';
      capturePreview.style.display = 'none';

      // Default to upload tab
      showTab('upload');
    }

    // Function to show a specific tab
    function showTab(tabName) {
      if (tabName === 'upload') {
        uploadTabBtn.classList.add('active');
        cameraTabBtn.classList.remove('active');
        uploadTab.style.display = 'block';
        cameraTab.style.display = 'none';

        // Stop camera if it's running
        stopCamera();
      } else if (tabName === 'camera') {
        uploadTabBtn.classList.remove('active');
        cameraTabBtn.classList.add('active');
        uploadTab.style.display = 'none';
        cameraTab.style.display = 'block';

        // Start camera
        startCamera();
      }
    }

    // Function to start the camera
    async function startCamera() {
      try {
        stream = await navigator.mediaDevices.getUserMedia({ video: true });
        cameraPreview.srcObject = stream;
        cameraPreview.style.display = 'block';
        cameraBtns.style.display = 'block';
        cameraPreview.play();
      } catch (error) {
        console.error('Error accessing camera:', error);
        showToast('Failed to access camera. Please check permissions.', 'error');
      }
    }

    // Function to stop the camera
    function stopCamera() {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
      }
      cameraPreview.style.display = 'none';
      cameraBtns.style.display = 'none';
    }

    // Function to capture a photo from the camera
    function capturePhoto() {
      if (!stream) return;

      // Set canvas dimensions to match video
      captureCanvas.width = cameraPreview.videoWidth;
      captureCanvas.height = cameraPreview.videoHeight;

      // Draw the current video frame to the canvas
      const context = captureCanvas.getContext('2d');
      context.drawImage(cameraPreview, 0, 0, captureCanvas.width, captureCanvas.height);

      // Convert canvas to image
      capturedImage.src = captureCanvas.toDataURL('image/jpeg');

      // Hide camera preview and controls, show the captured image
      cameraPreview.style.display = 'none';
      cameraBtns.style.display = 'none';
      capturePreview.style.display = 'block';

      // Convert to blob for upload
      captureCanvas.toBlob(blob => {
        capturedPhotoBlob = blob;
      }, 'image/jpeg', 0.95);
    }

    // Function to retake photo
    function retakePhoto() {
      // Hide the capture preview and show the camera preview again
      capturePreview.style.display = 'none';
      cameraPreview.style.display = 'block';
      cameraBtns.style.display = 'block';

      // Reset the captured photo blob
      capturedPhotoBlob = null;
    }

    // Function to upload the profile photo
    async function uploadProfilePhoto() {
      try {
        showToast('Uploading photo...', 'info');

        const formData = new FormData();

        // Add the photo file from either upload or camera
        if (uploadTabBtn.classList.contains('active')) {
          if (!photoFile.files || photoFile.files.length === 0) {
            showToast('Please select a file first', 'error');
            return;
          }
          formData.append('photo', photoFile.files[0]);
        } else {
          if (!capturedPhotoBlob) {
            showToast('Please capture a photo first', 'error');
            return;
          }
          formData.append('photo', capturedPhotoBlob, 'captured_photo.jpg');
        }

        // Send the request
        const response = await fetch(`/face_recognition/user/${userId}/update-photo`, {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'Profile photo updated successfully', 'success');

          // Close the dialog
          photoUploadDialog.style.display = 'none';

          // Stop camera if it's running
          stopCamera();

          // Reload the user image with a cache-busting parameter
          const timestamp = new Date().getTime();
          userImage.src = `${userImage.src.split('?')[0]}?t=${timestamp}`;

          // Reload user details to get updated encodings
          loadUserDetails();
        } else {
          showToast(result.detail || 'Failed to update profile photo', 'error');
        }
      } catch (error) {
        console.error('Error uploading profile photo:', error);
        showToast('Failed to upload profile photo. Please try again.', 'error');
      }
    }

    // Function to show the edit user dialog
    function showEditUserDialog() {
      // Populate form with current user data
      editUsername.value = userName.textContent;
      editEmail.value = userEmail.textContent;

      // Only set values if the elements are visible (data exists)
      if (employeeId.style.display === 'flex') {
        editEmployeeId.value = employeeId.querySelector('span').textContent;
      } else {
        editEmployeeId.value = '';
      }

      if (department.style.display === 'flex') {
        editDepartment.value = department.querySelector('span').textContent;
      } else {
        editDepartment.value = '';
      }

      if (dob.style.display === 'flex') {
        editDob.value = dob.querySelector('span').textContent;
      } else {
        editDob.value = '';
      }

      if (phoneNumber.style.display === 'flex') {
        editPhoneNumber.value = phoneNumber.querySelector('span').textContent;
      } else {
        editPhoneNumber.value = '';
      }

      if (address.style.display === 'flex') {
        editAddress.value = address.querySelector('span').textContent;
      } else {
        editAddress.value = '';
      }

      // Show the dialog
      editUserDialog.style.display = 'flex';
    }

    // Function to update user information
    async function updateUserInfo() {
      try {
        showToast('Updating user information...', 'info');

        // Prepare the data - only include fields that have values
        const userData = {};

        // Only add fields that have values
        const username = editUsername.value.trim();
        if (username) userData.username = username;

        const email = editEmail.value.trim();
        if (email) userData.email = email;

        const employeeIdValue = editEmployeeId.value.trim();
        if (employeeIdValue) userData.employee_id = employeeIdValue;

        const departmentValue = editDepartment.value.trim();
        if (departmentValue) userData.department = departmentValue;

        const dobValue = editDob.value.trim();
        if (dobValue) userData.dob = dobValue;

        const phoneNumberValue = editPhoneNumber.value.trim();
        if (phoneNumberValue) userData.phone_number = phoneNumberValue;

        const addressValue = editAddress.value.trim();
        if (addressValue) userData.address = addressValue;

        console.log('Sending user data:', userData);

        // Send the request
        const response = await fetch(`/face_recognition/user/${userId}/update`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
          showToast(result.message || 'User information updated successfully', 'success');

          // Close the dialog
          editUserDialog.style.display = 'none';

          // Reload user details to show updated information
          loadUserDetails();
        } else {
          showToast(result.detail || 'Failed to update user information', 'error');
        }
      } catch (error) {
        console.error('Error updating user information:', error);
        showToast('Failed to update user information. Please try again.', 'error');
      }
    }

    // Event Listeners
    backBtn.addEventListener('click', () => {
      // Redirect to the user management tab in the admin panel
      window.location.href = '/face_recognition/admin#user-management';
    });

    deleteUserBtn.addEventListener('click', () => {
      showConfirmDialog(`Are you sure you want to delete this user?`, deleteUser);
    });

    // Edit user dialog event listeners
    editUserBtn.addEventListener('click', showEditUserDialog);

    closeEditDialogBtn.addEventListener('click', () => {
      editUserDialog.style.display = 'none';
    });

    saveUserBtn.addEventListener('click', updateUserInfo);

    cancelEditBtn.addEventListener('click', () => {
      editUserDialog.style.display = 'none';
    });

    // Close dialog when clicking outside of it
    editUserDialog.addEventListener('click', (e) => {
      if (e.target === editUserDialog) {
        editUserDialog.style.display = 'none';
      }
    });

    // Photo Upload Event Listeners
    changePhotoBtn.addEventListener('click', showPhotoUploadDialog);

    // Close dialog button
    closeDialogBtn.addEventListener('click', () => {
      photoUploadDialog.style.display = 'none';
      stopCamera();
    });

    // Tab switching
    uploadTabBtn.addEventListener('click', () => showTab('upload'));
    cameraTabBtn.addEventListener('click', () => showTab('camera'));

    // File upload preview
    photoFile.addEventListener('change', () => {
      if (photoFile.files && photoFile.files[0]) {
        const reader = new FileReader();
        reader.onload = e => {
          previewImage.src = e.target.result;
          uploadPreview.style.display = 'block';
        };
        reader.readAsDataURL(photoFile.files[0]);
      }
    });

    // Camera capture buttons
    captureBtn.addEventListener('click', capturePhoto);
    retakeBtn.addEventListener('click', retakePhoto);

    // Use captured photo button (same as save photo)
    useCapturedBtn.addEventListener('click', uploadProfilePhoto);

    // Save and cancel buttons
    savePhotoBtn.addEventListener('click', uploadProfilePhoto);
    cancelPhotoBtn.addEventListener('click', () => {
      photoUploadDialog.style.display = 'none';
      stopCamera();
    });

    // Close dialog when clicking outside of it
    photoUploadDialog.addEventListener('click', (e) => {
      if (e.target === photoUploadDialog) {
        photoUploadDialog.style.display = 'none';
        stopCamera();
      }
    });

    // Load user details on page load
    loadUserDetails();
  </script>
</body>
</html>
