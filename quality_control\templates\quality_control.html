<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoGo - Water Quality Control Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

    <style>
        :root {
            --primary-color: #00a67d;
            --secondary-color: #005d45;
            --bg-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
            --danger-color: #ff3b30;
            --warning-color: #ffcc00;
            --success-color: #34c759;
            --panel-bg: #ffffff;
            --orange-color: #FF9F29;
            --sidebar-bg: #1e1e1e;
            --sidebar-text: #ffffff;
            --active-menu: #00a67d;
            --menu-hover: #333333;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            display: flex;
            background-color: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            overflow: hidden;
        }

        /* Sidebar - Updated to match Vigilant Eye design */
        .sidebar {
            width: 280px;
            background-color: var(--sidebar-bg);
            color: var(--sidebar-text);
            display: flex;
            flex-direction: column;
            padding: 0;
            overflow-y: auto;
        }

        .logo-container {
            padding: 20px 15px;
        }

        .logo-title {
            color: #17e68d;
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: var(--sidebar-text);
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .menu-item:hover {
            background-color: var(--menu-hover);
        }

        .menu-item.active {
            background-color: var(--active-menu);
        }

        .menu-icon {
            margin-right: 15px;
            width: 20px;
            text-align: center;
        }

        .menu-text {
            font-size: 16px;
        }

        .sidebar-footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            font-size: 14px;
            color: #8a8a8a;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #34c759;
            margin-right: 8px;
        }

        /* Main Content */
        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            padding: 12px 20px;
            background-color: var(--panel-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        /* Modified content-area layout */
        .content-area {
            flex-grow: 1;
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto 1fr;
            gap: 15px;
            padding: 15px;
            height: calc(100vh - 57px);
        }

        /* Water Level Status Panel - Modified width */
        .water-level-status {
            grid-column: 2;
            grid-row: 1;
            background-color: var(--panel-bg);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: 5px;
            display: flex;
            flex-direction: column;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .status-header h2 {
            font-size: 16px;
            font-weight: 600;
        }

        .recommended-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 6px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 13px;
        }

        .recommended-button:hover {
            background-color: var(--secondary-color);
        }

        .water-levels {
            display: flex;
            justify-content: space-between;
            gap: 15px;
        }

        .level-card {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .level-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .level-info {
            flex: 1;
        }

        .current-level .level-circle {
            background-color: #4285F4;
        }

        .recommended-level .level-circle {
            background-color: var(--primary-color);
        }

        .minimum-level .level-circle {
            background-color: var(--orange-color);
        }

        .level-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 3px;
        }

        .level-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
        }

        .progress-bar {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 4px;
        }

        .current-level .progress-fill {
            background-color: #4285F4;
            width: 75%;
        }

        .recommended-level .progress-fill {
            background-color: var(--primary-color);
            width: 50%;
        }

        .minimum-level .progress-fill {
            background-color: var(--orange-color);
            width: 68%;
        }

        /* Main panels layout - Modified */
        .webcam-panel {
            grid-column: 1;
            grid-row: 1 / span 2;
            background-color: var(--panel-bg);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .webcam-container {
            flex-grow: 1;
            position: relative;
            background-color: #e8f8f5;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .webcam-feed {
            max-width: 100%;
            max-height: 100%;
            display: block;
        }

        .water-level-indicators {
            position: absolute;
            pointer-events: none;
            width: 100%;
            height: 100%;
        }

        .water-bottle {
            position: relative;
            height: 70%;
            top: 15%;
        }

        .recommended-level-marker {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            padding: 4px;
            background-color: rgba(255, 204, 0, 0.8);
            color: #000;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }

        .current-level-marker {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            padding: 4px;
            background-color: rgba(0, 255, 255, 0.8);
            color: #000;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }

        /* Info panel - Modified position */
        .info-panel {
            grid-column: 2;
            grid-row: 2;
            background-color: var(--panel-bg);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .observations-list {
            padding: 15px;
            flex-grow: 1;
            overflow: auto;
        }

        .observation-item {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .observation-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .observation-label {
            font-weight: 600;
            margin-bottom: 4px;
            font-size: 13px;
        }

        .observation-value {
            padding: 6px 10px;
            background-color: #f7f7f7;
            border-radius: 4px;
            font-size: 13px;
        }

        .observation-value.success {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .observation-value.warning {
            background-color: rgba(255, 204, 0, 0.1);
            color: #b8860b;
        }

        .observation-value.danger {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        /* Control Buttons */
        .control-buttons {
            padding: 12px 15px;
            display: flex;
            gap: 8px;
            border-top: 1px solid var(--border-color);
        }

        .control-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 13px;
        }

        .control-button:hover {
            background-color: var(--secondary-color);
        }

        .control-button.stop {
            background-color: var(--danger-color);
        }

        .control-button.stop:hover {
            background-color: #d63026;
        }

        .nav-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .nav-button:hover {
            background-color: #e9e9e9;
        }

        .webcam-status.active {
            color: var(--success-color);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 30px;
            border: 1px solid #888;
            width: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .close-btn {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            top: 10px;
            right: 20px;
        }

        .close-btn:hover {
            color: #000;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .modal-body {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .modal-body input {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        .modal-footer {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }

        .submit-btn, .cancel-btn {
            padding: 10px 15px;
            font-size: 14px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .submit-btn {
            background-color: var(--primary-color);
            color: white;
        }

        .submit-btn:hover {
            background-color: var(--secondary-color);
        }

        .cancel-btn {
            background-color: #f1f1f1;
            color: #333;
        }

        .cancel-btn:hover {
            background-color: #e1e1e1;
        }

        .hidden {
            display: none;
        }

        .menu-link {
            text-decoration: none; /* Remove underline */
            color: inherit; /* Inherit text color from parent */
            display: block; /* Make the entire area clickable */
        }
    </style>
</head>
<body>
    <!-- Sidebar - Updated to match Vigilant Eye design -->
    <div class="sidebar">
        <div class="logo-container">
            <div class="logo-title">Vigilant Eye</div>
        </div>
        <div class="sidebar-menu">
            <a href="/" class="menu-item" class="menu-link">
                <div class="menu-icon">
                    <i class="fas fa-home"></i>
                </div>
                <div class="menu-text">Home</div>
            </a>
            <a class="menu-item active">
                <div class="menu-icon">
                    <i class="fas fa-video"></i>
                </div>
                <div class="menu-text">Monitoring</div>
            </a>
            <a class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="menu-text">Focus Areas</div>
            </a>
            <a class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="menu-text">Camera Management</div>
            </a>
            <a class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="menu-text">Alert Management</div>
            </a>
            <a class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="menu-text">Analytics</div>
            </a>
            <a class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="menu-text">Event History</div>
            </a>
            <a class="menu-item" id="homepageBtn">
                <div class="menu-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="menu-text">Go to Homepage</div>
            </a>
        </div>
        <div class="sidebar-footer">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>System Online</span>
            </div>
            <div>v2.3.5 | IP: 127.0.0.1:8000</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="header">
            <h1>Water Quality Control Dashboard</h1>
            <div class="user-info">
                <div class="user-avatar">A</div>
                <div class="user-name">Admin</div>
            </div>
        </div>

        <div class="content-area">
            <!-- Water Level Status Panel -->
            <div class="water-level-status">
                <div class="status-header">
                    <h2>Water Level Status</h2>
                    <button class="recommended-button" id="waterlevelBtn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                        Recommended Water Level
                    </button>
                </div>
                <div class="water-levels">
                    <div class="level-card recommended-level">
                        <div class="level-circle">50%</div>
                        <div class="level-info">
                            <div class="level-title">Recommended Level</div>
                            <div class="level-desc">Target for optimal operation</div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info Panel -->
            <div class="info-panel">
                <div class="panel-header">
                    Live Observations
                </div>
                <div class="observations-list">
                    <div class="observation-item">
                        <div class="observation-label">Bottle:</div>
                        <div class="observation-value" id="bottleStatus">N/A</div>
                    </div>
                    <div class="observation-item">
                        <div class="observation-label">Cap:</div>
                        <div class="observation-value" id="capStatus">N/A</div>
                    </div>
                    <div class="observation-item">
                        <div class="observation-label">WaterLevel:</div>
                        <div class="observation-value" id="waterLevelStatus">N/A</div>
                    </div>
                    <div class="observation-item">
                        <div class="observation-label">Fill Percentage:</div>
                        <div class="observation-value" id="fillPercentage">N/A</div>
                    </div>
                    <div class="observation-item">
                        <div class="observation-label">Last Check:</div>
                        <div class="observation-value" id="lastCheck">N/A</div>
                    </div>
                </div>
                <div class="control-buttons">
                    <button class="control-button" id="startStreamBtn">Start Check</button>
                    <button class="control-button stop" id="stopStreamBtn">Stop Check</button>
                </div>
            </div>

            <!-- Webcam Panel -->
            <div class="webcam-panel">
                <div class="panel-header">
                    <div>Live Webcam</div>
                    <div class="webcam-status active">Active</div>
                </div>
                <div class="webcam-container">
                    <img id="webcamVideo" src="data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 600' width='800' height='600'%3E%3Crect width='800' height='600' fill='%23e8f8f5'/%3E%3Crect x='300' y='100' width='200' height='400' rx='20' fill='%23d3d3d3' opacity='0.7'/%3E%3Crect x='320' y='150' width='160' height='300' rx='10' fill='%23a67c52' opacity='0.7'/%3E%3Crect x='320' y='300' width='160' height='150' rx='0' fill='%23cd853f' opacity='0.8'/%3E%3C/svg%3E" alt="Water Bottle Sample" class="webcam-feed">

                    <!-- <div class="water-level-indicators">
                        <div class="water-bottle">
                            <div class="recommended-level-marker" style="top: 35%;">Recommended WaterLevel</div>
                            <div class="current-level-marker" style="top: 50%;">WaterLevel</div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    <!-- Water Level Modal -->
    <div id="waterLevelModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" id="closeWaterLevelModal">&times;</span>
            <h2 class="modal-title">Recommended Water Level</h2>

            <div class="modal-body">
                <p>Please enter the recommended water level below:</p>
                <input type="number" id="recommendedWaterLevel" name="recommendedWaterLevel" placeholder="Enter Water Level (0-100)" required>
            </div>

            <div class="modal-footer">
                <button class="submit-btn" id="submitWaterLevel">Submit</button>
                <button class="cancel-btn" id="closeWaterLevelModalFooter">Cancel</button>
            </div>
        </div>
    </div>


  <script>
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const webcamVideo = document.getElementById('webcamVideo');
    const homepageBtn = document.getElementById("homepageBtn");
    const webcamStatus = document.querySelector(".webcam-status");

    const bottleStatus = document.getElementById("bottleStatus");
    const capStatus = document.getElementById("capStatus");
    const waterLevelStatus = document.getElementById("waterLevelStatus");
    const fillPercentage = document.getElementById("fillPercentage");
    const lastCheck = document.getElementById("lastCheck");

    let ws = null;
    let isStreaming = false;

    const waterLevelBtn = document.getElementById("waterlevelBtn");
    const waterLevelModal = document.getElementById("waterLevelModal");
    const closeWaterLevelModal = document.getElementById("closeWaterLevelModal");
    const closeWaterLevelModalFooter = document.getElementById("closeWaterLevelModalFooter");
    const submitWaterLevelBtn = document.getElementById("submitWaterLevel");

    // Open Modal
    waterLevelBtn.addEventListener("click", () => {
      waterLevelModal.style.display = "block";
    });

    // Close Modal when clicking close buttons
    closeWaterLevelModal.addEventListener("click", () => {
      waterLevelModal.style.display = "none";
    });

    closeWaterLevelModalFooter.addEventListener("click", () => {
      waterLevelModal.style.display = "none";
    });

    // Close modal when clicking outside the modal
    window.addEventListener("click", (event) => {
      if (event.target === waterLevelModal) {
        waterLevelModal.style.display = "none";
      }
    });

    // Handle Submit
    submitWaterLevelBtn.addEventListener("click", async () => {
      let percentage = parseFloat(document.getElementById("recommendedWaterLevel").value);

      // Validate input: Ensure it's a number and within 0-100 range
      if (isNaN(percentage) || percentage < 0 || percentage > 100) {
          alert("Please enter a valid water level between 0 and 100.");
          return;
      }

      let decimalValue = percentage / 100; // Convert percentage to decimal

      try {
          const response = await fetch("http://localhost:8000/quality_control/set-waterlevel", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ level: decimalValue }),
          });

          const result = await response.json();

          if (response.ok) {
              alert("Water level set successfully!");
              // Update the recommended level display
              document.querySelector(".recommended-level .level-circle").textContent = percentage + "%";
              document.querySelector(".recommended-level .progress-fill").style.width = percentage + "%";

              // Update the recommended level marker position
              const recommendedMarker = document.querySelector(".recommended-level-marker");
              if (recommendedMarker) {
                  // Invert the percentage (100% - percentage) since 0% is at the bottom
                  const topPosition = 100 - percentage;
                  recommendedMarker.style.top = topPosition + "%";
                  recommendedMarker.textContent = "Recommended Water Level: " + percentage + "%";
              }

              document.getElementById("recommendedWaterLevel").value = ""; // Clear input field
              waterLevelModal.style.display = "none"; // Close modal
          } else {
              alert(`Error: ${result.detail || "Failed to set water level."}`);
          }
      } catch (error) {
          console.error("Error:", error);
          alert("Something went wrong. Please try again.");
      }
    });

    homepageBtn.addEventListener("click", () => {
      window.location.href = "/";
    });

    // Function to update observation status classes
    function updateStatusClasses(element, status) {
        element.classList.remove("success", "warning", "danger");

        if (status === "Valid" || status === "OK") {
            element.classList.add("success");
        } else if (status === "Below Recommended" || status.includes("Warning")) {
            element.classList.add("warning");
        } else if (status === "Invalid" || status.includes("Error")) {
            element.classList.add("danger");
        }
    }

    // Start webcam streaming
    startStreamBtn.addEventListener("click", () => {
        if (isStreaming) return;

        isStreaming = true;
        webcamStatus.textContent = "Streaming";
        webcamStatus.classList.add("active");

        if (!ws) {
            ws = new WebSocket("ws://localhost:8000/quality_control/ws");

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);

                if (data.error) {
                    alert(data.error);  // Show alert if no camera is found
                    webcamVideo.src = "data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 600' width='800' height='600'%3E%3Crect width='800' height='600' fill='%23e8f8f5'/%3E%3Crect x='300' y='100' width='200' height='400' rx='20' fill='%23d3d3d3' opacity='0.7'/%3E%3Crect x='320' y='150' width='160' height='300' rx='10' fill='%23a67c52' opacity='0.7'/%3E%3Crect x='320' y='300' width='160' height='150' rx='0' fill='%23cd853f' opacity='0.8'/%3E%3C/svg%3E";
                    bottleStatus.textContent = "N/A";
                    capStatus.textContent = "N/A";
                    waterLevelStatus.textContent = "N/A";
                    fillPercentage.textContent = "N/A";
                    lastCheck.textContent = "N/A";

                    webcamStatus.textContent = "Inactive";
                    webcamStatus.classList.remove("active");
                    isStreaming = false;

                    ws.close();
                    return;
                }

                if (data.image) {
                    webcamVideo.src = "data:image/jpeg;base64," + data.image;
                }

                if (data.bottle) {
                    bottleStatus.textContent = data.bottle;
                    updateStatusClasses(bottleStatus, data.bottle);
                }

                if (data.cap) {
                    capStatus.textContent = data.cap;
                    updateStatusClasses(capStatus, data.cap);
                }

                if (data.waterLevel) {
                    waterLevelStatus.textContent = data.waterLevel;
                    updateStatusClasses(waterLevelStatus, data.waterLevel);
                }

                // Update fill percentage if available
                if (data.fillPercentage) {
                    fillPercentage.textContent = data.fillPercentage;

                    // Update the current level display
                    document.querySelector(".current-level .level-circle").textContent = data.fillPercentage;
                    document.querySelector(".current-level .progress-fill").style.width = data.fillPercentage;

                    // Update the current level marker position
                    const currentMarker = document.querySelector(".current-level-marker");
                    if (currentMarker) {
                        // Extract the percentage value
                        const percentValue = parseFloat(data.fillPercentage);
                        if (!isNaN(percentValue)) {
                            // Invert the percentage (100% - percentage) since 0% is at the bottom
                            const topPosition = 100 - percentValue;
                            currentMarker.style.top = topPosition + "%";
                        }
                    }
                }

                // Update last check timestamp
                const now = new Date();
                const formattedDate = now.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });
                const formattedTime = now.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                lastCheck.textContent = `${formattedDate} - ${formattedTime}`;
            };

            ws.onclose = () => {
                console.log("WebSocket closed");
                ws = null;
                isStreaming = false;
                webcamStatus.textContent = "Inactive";
                webcamStatus.classList.remove("active");
            };

            ws.onerror = (error) => {
                console.error("WebSocket error:", error);
                isStreaming = false;
                webcamStatus.textContent = "Error";
                webcamStatus.classList.remove("active");
            };
        }
    });

    // Stop webcam streaming
    stopStreamBtn.addEventListener("click", () => {
        if (ws) {
            ws.send(JSON.stringify({ action: "stop" }));  // Send stop signal
            ws.close();
        }

        // Reset webcam feed to placeholder
        webcamVideo.src = "data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 600' width='800' height='600'%3E%3Crect width='800' height='600' fill='%23e8f8f5'/%3E%3Crect x='300' y='100' width='200' height='400' rx='20' fill='%23d3d3d3' opacity='0.7'/%3E%3Crect x='320' y='150' width='160' height='300' rx='10' fill='%23a67c52' opacity='0.7'/%3E%3Crect x='320' y='300' width='160' height='150' rx='0' fill='%23cd853f' opacity='0.8'/%3E%3C/svg%3E";

        // Reset observation values
        bottleStatus.textContent = "N/A";
        bottleStatus.className = "observation-value";

        capStatus.textContent = "N/A";
        capStatus.className = "observation-value";

        waterLevelStatus.textContent = "N/A";
        waterLevelStatus.className = "observation-value";

        fillPercentage.textContent = "N/A";
        lastCheck.textContent = "N/A";

        // Update webcam status
        webcamStatus.textContent = "Inactive";
        webcamStatus.classList.remove("active");
        isStreaming = false;
    });

  </script>

</body>
</html>