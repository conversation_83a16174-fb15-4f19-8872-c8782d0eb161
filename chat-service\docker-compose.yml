services:
  chatbot:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_USER=root
      - DB_PASSWORD=<PERSON><PERSON>eeth@2002
      - DB_HOST=host.docker.internal
      - DB_NAME=surveillance_system
      - DB_PORT=3306
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

  db:
    image: mysql:8.0
    volumes:
      - mysql-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=Navaneeth@2002
      - MYSQL_DATABASE=surveillance_system
    ports:
      - "3307:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-prootpassword"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql-data:

