import httpx
import json
from jinja2 import Template
from models import WebhookPayload
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from modules.face_recognition.models import WhatsApp, Webhook, CameraPermission, User, Camera, SMS, Email

from utils.whatsapp import trigger_whatsapp_webhook
from utils.email import send_email
from utils.sms import send_sms 


class FaceRecognitionModule:
    
    def __init__(self):
        pass
        
    async def handle_event(self, payload: WebhookPayload, db: AsyncSession):
        user_id = payload.user_id
        camera_id = payload.camera_id
        event = payload.event
        
        # Get username and camera name if not provided
        username = payload.username
        camera_name = payload.camera_name
        
        if username is None:
            username = await self.get_username_from_id(user_id, db)
        if camera_name is None:
            camera_name = await self.get_cameraname_from_id(camera_id, db)
        
        whatsapp = payload.whatsapp
        webhooks = payload.webhooks
        email = payload.email
        sms = payload.sms
        
        permitted = await self.is_permitted(user_id, camera_id, db)
        
        if not permitted:
            print("Not permitted")

            # 1. Fetch active WhatsApp contacts
            if whatsapp:
                phone_numbers = await self.get_active_whatsapp_contacts(db)
                print("Phone numbers:", phone_numbers)

                # 2. Send message to each number
                for number in phone_numbers:
                    message = f"Event: {event}\n\nUser: {username}\n\nCamera: {camera_name}\n\nUser ID: {user_id}\n\nCamera ID: {camera_id}"
                    await trigger_whatsapp_webhook(payload, phone_number=number, message=message)
            
            if webhooks:
                print("webhooks triggered..")
                await self.trigger_webhooks(payload, db, username, camera_name)
                
            if email:
                print("email triggered...")
                # await send_email(payload)
                
            if sms:
                print("sms triggered...")
                # await send_sms(payload)
                

        return {"status": "handled"}
    
    async def is_permitted(self, user_id: int, camera_id: int, db: AsyncSession) -> bool:
        stmt = select(CameraPermission).where(
            CameraPermission.user_id == user_id,
            CameraPermission.camera_id == camera_id
        )
        result = await db.execute(stmt)
        permission = result.scalar_one_or_none()
        return permission is not None

    async def get_active_whatsapp_contacts(self, db: AsyncSession):
        stmt = select(WhatsApp).where(WhatsApp.is_active == True)
        result = await db.execute(stmt)
        contacts = result.scalars().all()
        return [contact.phone_number for contact in contacts]
    
    async def get_active_webhooks(self, db: AsyncSession):
        stmt = select(Webhook).where(Webhook.is_active == True)
        result = await db.execute(stmt)
        webhooks = result.scalars().all()
        return webhooks
    
    async def get_username_from_id(self, user_id: int, db: AsyncSession):
        # Fetch username
        user_stmt = select(User.username).where(User.id == user_id)
        user_result = await db.execute(user_stmt)
        username = user_result.scalar_one_or_none()
        return username
        
    async def get_cameraname_from_id(self, camera_id: int, db: AsyncSession):
        # Fetch camera name
        camera_stmt = select(Camera.name).where(Camera.id == camera_id)
        camera_result = await db.execute(camera_stmt)
        camera_name = camera_result.scalar_one_or_none()
        return camera_name
    
    async def trigger_webhooks(self, payload: WebhookPayload, db: AsyncSession, username: str = None, camera_name: str = None):
        webhooks = await self.get_active_webhooks(db)
        
        context = {
            "user_id": payload.user_id,
            "camera_id": payload.camera_id,
            "username": username,
            "camera_name": camera_name,
            "event": payload.event,
            "timestamp": payload.timestamp
        }
        
        async with httpx.AsyncClient() as client:
            for webhook in webhooks:
                try:
                    # Render payload using Jinja2 template if available
                    if webhook.payload_structure:
                        template = Template(webhook.payload_structure)
                        rendered_payload = template.render(**context)
                        try:
                            json_payload = json.loads(rendered_payload)
                        except json.JSONDecodeError as e:
                            print(f"[ERROR] Invalid JSON in webhook {webhook.id}: {e}")
                            continue
                    else:
                        json_payload = context  # fallback
                    
                    method = webhook.http_method.upper()
                    url = webhook.webhook_url

                    # Parse headers if they exist and are JSON-formatted
                    try:
                        headers = json.loads(webhook.headers) if webhook.headers else {}
                    except json.JSONDecodeError:
                        print(f"[WARN] Headers in webhook {webhook.id} are not valid JSON, ignoring them.")
                        headers = {}

                    print(f"[INFO] Triggering webhook {webhook.id} with method {method} to {url}")

                    # Perform the HTTP request
                    if method == "POST":
                        response = await client.post(url, json=json_payload, headers=headers)
                    elif method == "PUT":
                        response = await client.put(url, json=json_payload, headers=headers)
                    elif method == "GET":
                        response = await client.get(url, params=json_payload, headers=headers)
                    elif method == "DELETE":
                        response = await client.delete(url, json=json_payload, headers=headers)
                    else:
                        print(f"[WARN] Unsupported HTTP method: {method}")
                        continue

                    print(f"[INFO] Webhook {webhook.id} responded with status {response.status_code}")

                except Exception as e:
                    print(f"[ERROR] Exception when triggering webhook {webhook.id}: {e}")
